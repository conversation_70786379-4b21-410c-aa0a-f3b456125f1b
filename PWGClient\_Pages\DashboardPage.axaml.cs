using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using Sentry;
using Avalonia.Controls.Primitives;
using System.Diagnostics;
using MathNet.Numerics;
using System.Linq;
using Tabula.PWG.SARGAME.Utils;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class DashboardPage : UserControl
	{
		public DashboardPage()
		{
			InitializeComponent();
		}

		void dashboard_tabcontrol_selectionchanged(object sender, SelectionChangedEventArgs args)
		{
			if (args.AddedItems[0] == tab_games)
			{
				SARGAME.Instance?.LoadInstalledPackagesAndModules();
			}
			else if (args.AddedItems[0] == tab_store)
			{
				App.Instance?.CheckStoreUpdates();
			}
		}

		async void bt_info_Click(object sender, RoutedEventArgs args)
		{
			PackageModule module = (sender as Avalonia.Controls.Control).DataContext as PackageModule;
			MainWindowUI.Instance.SelectedModule = module;

			var w = new ModuleInfoWindow();

			await w.ShowDialog(App.MainWindow);
		}

		async void bt_start_Click(object sender, RoutedEventArgs args)
		{
			PackageModule module = (sender as Avalonia.Controls.Control).DataContext as PackageModule;

			if (module == null)
				return;

			bool use_last_config = false;

			if (MainWindowUI.Instance.LastMonitor!=null && MainWindowUI.Instance.LastScenario != null)
			{
				var choice = await ProgressDialog.ShowChoicesAsync(
						"Use last configuration?",
						new string[] { "Yes", "No" });

				use_last_config = choice == ProgressDialog.Choice.Choice1;

				if (!use_last_config)
				{
					MainWindowUI.Instance.LastIsWindowed = null;
				}
			}

			StartModule(module, quickstart: use_last_config);
		}

		async void StartModule(PackageModule module, bool quickstart=false, bool quickswitch=true)
		{
			bool IsWindowed = false;

			if (!await MainView.Instance.AskLicenseActivation())
				return;

			if (!module.IsInstalled)
			{
				// route to install (and go back to games tab)
				await InstallPackage(module);

				return;
			}

			var running_module = SARGAME.Instance.GetRunningModule();

			// check the package minimum protocol
			// NOTE: protocol is always zero so this is always true, we are now checking based on editor and package versions
			if (SARGAME.Version < module.Package.protocol)
			{
				await ProgressDialog.ShowMessageAsync("This application requires a newer Editor protocol version.\nPlease update the Editor.");
				return;
			}

			if (!VersionChecker.IsCompatibleVersion(App.Instance.GetVersion(), module.Package.StorePackage.Version))
			{
				var versionMessage = VersionChecker.GetVersionMismatchMessage(App.Instance.GetVersion(), module.Package.StorePackage.Version);
				await ProgressDialog.ShowMessageAsync(versionMessage);
				return;
			}

			ProgressDialog msgbox;

			// If process il already running hook to it
			if (App.IsConnected)
			{
				if (running_module != null)
				{
					if ((running_module.Package.id == module.Package.id) &&
						(running_module.id == module.id)
						)
						await ProgressDialog.ShowMessageAsync("Editor is already connected.");
					else
					{
						if (quickswitch)
						{
							// auto disconnect first, reconnect with same settings
							await App.Instance.DisconnectAndStopModule();
							goto start_module;
						}
						else
							await ProgressDialog.ShowMessageAsync("Editor is connected.\nPlease disconnect and stop to play another application");
					}
				}
				else
				{
					// strange case... probably antivirus?
					// TODO: issue an a warning
				}

				return;
			}
			else
			{
				// not connected, but check if we have a process running
				if (running_module != null)
				{
					if ((running_module.Package.id == module.Package.id) &&
						(running_module.id == module.id)
						)
					{
						var choice = await ProgressDialog.ShowChoicesAsync(
						"This application is already running.",
						new string[] { "Connect", "Cancel" });

						if (choice == ProgressDialog.Choice.Choice1)
							goto connect;

						return;
					}
					else
					{
						await ProgressDialog.ShowMessageAsync("Another application is already running.");
						return;
					}
				}
			}

		start_module:

			// Check the module is really there!
			if (!module.IsInstalled)
			{
				// Error, refresh
				SARGAME.Instance.LoadInstalledPackagesAndModules();
				return;
			}

			if (module.GetProcess() == null)
			{
				// start the scenario chooser
				var scenario = quickstart ? MainWindowUI.Instance.LastScenario : await ScenarioChooser.Show();

				if (scenario == null)
					return;

				MainWindowUI.Instance.LastScenario = scenario;

				var name = scenario.Name;

				var monitor = quickstart ? MainWindowUI.Instance.LastMonitor : await MonitorChooser.Show();

				if (monitor == null)
					return;

				MainWindowUI.Instance.LastMonitor = monitor;

				IsWindowed = MainWindowUI.Instance.LastIsWindowed!=null ? (bool)MainWindowUI.Instance.LastIsWindowed : false;

				if (monitor.IsCurrentScreen)
				{
					ProgressDialog.Choice choice;

					if (!quickstart || MainWindowUI.Instance.LastIsWindowed == null)
					{
						choice = await ProgressDialog.ShowChoicesAsync(
							"This is the desktop monitor!\n\nIf you run the application fullscreen you will not be able to use the editor easily (ALT+Tab to select it).",
							new string[] { "Start in a Window", "Start Fullscreen" });

						IsWindowed = (choice == ProgressDialog.Choice.Choice1);
					}
				}

				MainWindowUI.Instance.LastIsWindowed = IsWindowed;

				module.SelectedScenario = scenario;


				// Check the monitor resolution is not too high (>4k)
				if (!IsWindowed && !MainWindowUI.Instance.CustomResolutionEnabled && monitor.system_width > 3840)
				{
					await ProgressDialog.ShowMessageAsync("The monitor resolution is too high!\nMaximum allowed is 3840 pixels wide, please lower the resolution.");
					return;
				}

				DateTime start_check = DateTime.Now;

				// Start

				// report the display size
				SentrySdk.StartTransaction("module_start", $"module:{module.id} display:({monitor.system_width}x{monitor.system_height}) windowed:{IsWindowed}").Finish();

				int start_status = 0;   // will transition to 1 for OK or error codes

				Task.Run(async () => await module.Start(
					windowed: IsWindowed,
					monitor: monitor,
					spout: (MainWindowUI.Instance.CustomResolutionEnabled && MainWindowUI.Instance.CustomResolutionSpout),
					popupwindow: (MainWindowUI.Instance.CustomResolutionEnabled && !MainWindowUI.Instance.CustomResolutionSpout),
					onStartStatus: (s) => start_status = s));

				msgbox = ProgressDialog.ShowMessage(
					message: "Starting...",
					progress: true);

				// wait for status to change with timeout
				var sw = new Stopwatch();
				sw.Start();
				while (start_status == 0 && sw.ElapsedMilliseconds < 10000)
					await Task.Delay(20);

				string start_error = null;
				switch (start_status)
				{
					case 0: // the module did not start!
						start_error = "ERROR: The module did not start.";
						break;

					case 1: //OK
						break;

					case -10:   // error in copying template project
						start_error = "ERROR: Copying template project file.";
						break;

					case -11:   // missing project file
						start_error = "ERROR: Missing project file.";
						break;

					case -20:   // general exception
						start_error = "ERROR: General Exception";
						break;
				}

				if (!string.IsNullOrEmpty(start_error))
				{
					// expired
					msgbox.Close();

					string msg = SARGAME.Instance.IsLicenseError ? "License error. Please reactivate it." : start_error;

					await ProgressDialog.ShowMessageAsync(msg);

					MainView.Instance.ToggleSceneControlsRendering(true);

					return;
				}



				// Wait for module info to be available
				var procinfo = await module.WaitProcessInfo(start_check, 60000);

				if (procinfo == null)
				{
					// expired
					msgbox.Close();

					string msg = SARGAME.Instance.IsLicenseError ? "License error. Please reactivate it." : "Cannot start module. Please retry.";

					await ProgressDialog.ShowMessageAsync(msg);

					MainView.Instance.ToggleSceneControlsRendering(true);

					return;
				}

				// Wait some other seconds ?
				await Task.Delay(2000);

				msgbox.Close();
			}

		connect:
			// try to connect (15 secs, or user cancel)

			MainWindowUI.Instance.AreStructuresLocked = false;

			var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15));

			msgbox = ProgressDialog.ShowMessage(
						message: "Connecting...",
						cancel_button_text: "Cancel",
						on_cancel_button_click: () => cts.Cancel(),
						progress: true);

			var ret = await App.Instance.ConnectToServer(main_timeout_token: cts.Token);

			if (ret <= 0)
			{
				msgbox.Close();

				string err = "";
				switch (ret)
				{
					case -30: err = "Protocol version mismatch. Please update the Editor or use a compatible package version"; break;
				}

				App.Local.log("Cannot connect to server: " + err);

				if (!cts.IsCancellationRequested)
					await ProgressDialog.ShowMessageAsync($"Cannot connect to server.\n{err}\nPlease retry.");

				MainView.Instance.ToggleSceneControlsRendering(true);

				await App.Instance.DisconnectFromServer();

				return;
			}

			// Connected head to the Projection Tab or the Level Design tab depending if we are on the calibrator
			if (module.id == "calibrator")
				MainView.Instance.tab_projection.IsSelected = true;
			else
				MainView.Instance.tab_level_design.IsSelected = true;

			msgbox.Close();

			/*
			await ProgressDialog.ShowAsync(new ProgressDialogView()
			{
				Message = "Connecting...",
				IsProgressIndeterminate = true,
				ShowCancelButton = true,
				CancellationToken = cts.Token,
				Action = async (token) =>
				{
					while (true)
					{
						token.ThrowIfCancellationRequested();

						int status = -1;

						var op = Dispatcher.UIThread.InvokeAsync(async () 
							=> await App.Instance.ConnectToServer(token));

						status = await op.Result;

						if (status > 0)
							break;

						await Task.Delay(TimeSpan.FromSeconds(1));
					}
				},
				CloseOnTaskFinish = true
			}, "dialog");
			*/

			DataContext = MainWindowUI.Instance;
			MainWindowUI.Instance.RaiseAllPropertyChanged();
			MainView.Instance.ToggleSceneControlsRendering(true);

			await App.Instance.CheckProtocolVersion();

			if (IsWindowed)
			{
				// If we started windows, we need to resize the warp
				try
				{
					MainWindowUI.Instance.View.OutputSurfaces[0].ResetSurfaceWarp();
				}
				catch { }
			}
		}

		async void bt_install_Click(object sender, RoutedEventArgs args)
		{
			PackageModule module = (sender as Avalonia.Controls.Control).DataContext as PackageModule;
			await InstallPackage(module);
		}

		public async Task InstallPackage(PackageModule module)
		{
			try
			{
				tab_store.IsSelected = true;

				var store_package = (from p in SARGAME.Instance.CurrentStore.packages where p.id == module.package_id select p).FirstOrDefault();
				if (store_package != null)
				{
					var ret = await StorePage.DownloadAndInstallPackage(store_package, show_ui: true);
				}
			}
			catch (Exception ex)
			{
				SARGAME.App.logException("bt_install_Click", ex);
				await ProgressDialog.ShowMessageAsync("An error occurred installing the package", "Continue");
			}

			// return to  apps
			tab_games.IsSelected = true;
		}

	}
	

}
