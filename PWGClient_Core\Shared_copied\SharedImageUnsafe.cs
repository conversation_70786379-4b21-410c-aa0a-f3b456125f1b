﻿//TABULA_GUID:{209BEBA2-6DB8-44B6-80FF-7CD696E47019}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

// NOTE: This is more generalized shared memory image, taken from the TMB one
// Depends on SharedMemory.cs

namespace Tabula
{
    /**
     * Usage:
     * 
     *   // Server
     *   var server_shim = new SharedImageWrapper();
     *         
     *   // creates a 1920x1080x3 image RGB24
     *   if (!server_shim.Open("shmimage_name", SharedImageDescription.RGB(1920,1080,3), true))
     *      return false;
     *      
     *   // Updates it whenever needed with the IntPtr
     *   server_shim.WriteImageData(im_width, im_height, im_ptr);
     *      
     *   // Client
     *   var client_shim = new SharedImageWrapper();
     *   
     *   // opens the same kind of image
     *   if (!client_shim.IsAvailable)
     *      if (!client_shim.Open("shmimage_name", SharedImageDescription.RGB(1920,1080,3), false))
     *          return false;
     *   
     *   // checks it was updated
     *   if (client_shim.IsUpdated)
     *   {
     *       // reads into dest_buffer (IntPtr unsafe)
     *       client_shim.ReadImageData(dest_buffer);
     *       
     *       client_shim.IsUpdated = true;
     *   }
     *   
     */


    // This meta-structure describes the byte ordering of fields of a backing shared image
    // this way there is NO NEED to define a real structure, we just allocate data and refer to the computed field offsets
    public struct SharedImageDescription
    {
        public uint IMAGEBPP;
        public uint IMAGESIZEMAX;

        // Unsafe memory layout
        public int _size;

        public int _id;
        public int _width;
        public int _height;
        public int _image_data;

        // Helper to create any RGB image
        public static SharedImageDescription RGB(uint width, uint height, uint bpp)
        {
            var s = new SharedImageDescription();

            s.IMAGEBPP = bpp;
            s.IMAGESIZEMAX = (width * height * bpp);

            s._id = 0;
            s._width = 4;
            s._height = 8;
            s._image_data = 12;
            s._size = s._image_data + (int) s.IMAGESIZEMAX;

            return s;
        }
    }

    // This is a sample SharedImage RGB24 1920*1080*3
    /*
    public unsafe struct SharedImage
    {
        public const int IMAGEBPP = 3;  // format is always RGB24       
        public const int IMAGESIZEMAX = 1920 * 1080 * IMAGEBPP;

        public uint id; // c++ long is c# int

        public uint width;
        public uint height;

        public fixed byte image_data[IMAGESIZEMAX];

        // Unsafe memory layout
        public const int _size = 12 + IMAGESIZEMAX;

        public const int _id = 0;
        public const int _width = 4;
        public const int _height = 8;
        public const int _image_data = 12;
    }
    */

    // wrapper class to easily access the generic shared image
    internal unsafe class SharedImageWrapper : SharedMemoryUnsafe
    {
        private SharedImageDescription desc;
        private uint last_id = 0;

        public uint Id => *_uint(desc._id);
        public uint Width => *_uint(desc._width);
        public uint Height => *_uint(desc._height);
        public IntPtr ImageData { get; private set; }   // direct pointer to first byte if image data

        // helper functions to directly access structure fields 
        public uint* _uint(int offset) => (uint*)(Data + offset);
        public int* _int(int offset) => (int*)(Data + offset);
        public byte* _byte_array(int offset, int index) => (byte*)(Data + offset + index * sizeof(byte));

        // NOTE: Semantics is special
        public bool IsUpdated
        {
            get => Id != last_id;
            set => last_id = Id;
        }

        public bool HasSkippedFrame => (Id - last_id) > 1;
        

        public bool Open(string shared_memory_name, SharedImageDescription image_desc, bool create_if_nonexisting)
        {
            desc = image_desc;

            if (!Open(shared_memory_name, desc._size, create_if_nonexisting))
                return false;

            ImageData = new IntPtr((void*)_byte_array(desc._image_data, 0));

            return true;
        }

        // Updates the image and increments the id
        public void WriteImageData(uint width, uint height, IntPtr src)
        {
            uint id = *_uint(desc._id);

            *_uint(desc._width) = width;
            *_uint(desc._height) = height;

            // copy bytes from source
            CopyMemory(ImageData, src, width * height * desc.IMAGEBPP);

            id++;
            *_uint(desc._id) = id;
        }

        public void ReadImageData(IntPtr dst)
        {
            CopyMemory(dst, ImageData, Width * Height * desc.IMAGEBPP);
        }

        // clears to black
        public void Clear()
        {
            uint id = *_uint(desc._id);

            // zero
            ZeroMemory(ImageData, *_uint(desc._width) * *_uint(desc._height) * desc.IMAGEBPP);

            id++;
            *_uint(desc._id) = id;
        }

        public override void Close()
        {
            ImageData = IntPtr.Zero;

            base.Close();
        }

#if NET5_0
        [DllImport("kernel32.dll", EntryPoint = "RtlMoveMemory", SetLastError = false)]
        public static extern void CopyMemory(IntPtr dest, IntPtr src, uint count);

        [DllImport("Kernel32.dll", EntryPoint = "ZeroMemory", SetLastError = false)]
        public static extern void ZeroMemory(IntPtr dest, uint size);
#else
        [DllImport("kernel32.dll", EntryPoint = "CopyMemory", SetLastError = false)]
        public static extern void CopyMemory(IntPtr dest, IntPtr src, uint count);

        [DllImport("Kernel32.dll", EntryPoint = "ZeroMemory", SetLastError = false)]
        public static extern void ZeroMemory(IntPtr dest, uint size);        
#endif

    }
}
