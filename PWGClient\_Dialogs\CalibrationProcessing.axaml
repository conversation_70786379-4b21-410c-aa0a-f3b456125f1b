<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
			 xmlns:tabula_unity="clr-namespace:Tabula.Unity"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.CalibrationProcessing" Background="Transparent">
		<Border Classes="Card" Background="{DynamicResource ExpanderBackground}">
			<Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto"  Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
				<materialIcons:MaterialIcon Grid.Row="0" Width="60" Height="60" Margin="15" Kind="{Binding Icon}" HorizontalAlignment="Center"/>
				<TextBlock Grid.Row="1" Text="{Binding Message}" TextWrapping="Wrap" Margin="10" HorizontalAlignment="Center"/>
				<TextBlock Grid.Row="2" Text="{Binding AuthCode}" IsVisible="{Binding IsAuthCodeVisible}" TextWrapping="Wrap" Margin="20" FontSize="24" FontWeight="Bold" FontFamily="Courier" HorizontalAlignment="Center"/>
				<ProgressBar Grid.Row="3" IsIndeterminate="true" IsVisible="{Binding IsProgressVisible}" Minimum="0" Maximum="100" Margin="10"/>

				<Button Grid.Row="4" Click="bt_cancel_Click" Margin="10,20,10,10" HorizontalAlignment="Center">
					<TextBlock Text="Cancel" FontFamily="Courier" HorizontalAlignment="Center"/>
				</Button>
			</Grid>
		</Border>
</UserControl>
