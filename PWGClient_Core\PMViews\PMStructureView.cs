﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Input;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

// Base class for a visual view of a GuidObject class


namespace Tabula.PMCore
{

    // Extension of the auto-generated view class with methods to call from xaml/GUI
    // NOTE: since callable methods have a Task signature they cannot be called from CallMethodAction... so many wrappers methods.... hope to get rid of them
    public partial class StructureView : GuidObjectSyncView<Structure>
    {
        public void Remove() => Root.RemoveStructure(this);

        // Test method, "splits" last segment
        public void TestAddVertex()
        {
            var p1 = this.Model.vertices[Model.vertices.Count - 2];
            var p2 = this.Model.vertices[Model.vertices.Count - 1];

            var p3 = new Vector2f((p1.x + p2.x) / 2f, (p1.y + p2.y) / 2f);

            // TODO: report
            //AddVertex(Model.vertices.Count - 1, p3);
        }

        public void SetNextEffect()
        {
            params0.effect = params0.effect + 1;
        }

        public void SetPrevEffect()
        {
            if ((params0.effect - 1) >= -1)
                params0.effect = params0.effect - 1;
        }
    }


    public class PMStructureView : PMEditablePolygonView<Tabula.PMCore.Structure, Tabula.PMCore.StructureView>
    {
        public PMStructureView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }

        // editing color references
        private SKColor Color = new SKColor(0, 0, 100, 150);
        private SKColor ColorHover = new SKColor(0, 0, 255, 150);
        private SKColor ColorDragging = new SKColor(255, 255, 255, 150);


		public override void CreateVisual()
        {
            bool was_selected = Visual!=null ? Visual.IsSelected : false;

            if (Visual != null)
                Visual.Scene.Remove(Visual);

            VisualPoly = new SKFreePolygon(Scene)
            {
                IsDraggable = !SARGAME.iMainWindowUI.AreStructuresLocked,

                IsSelectableWithLeftClick = true,   // must be first selected to be moved

                MustBeValid = true,

				Layer = SKScene.LayerStructures,
				Color = Color,
                ColorHover = ColorHover,
                ColorDragging = ColorDragging
            };

            VisualPoly.Center = new SKPoint(Model.position.x, Model.position.y);

            foreach (var v in Model.vertices)
                VisualPoly.AddSegment(new SKPoint(v.x, v.y), SKFreePolygon.SegmentType.Line);

            // TODO: onMove dei singoli segmenti
            VisualPoly.OnVertexDragStart += (cp) => VisualPoly.Color = ColorDragging;
			VisualPoly.OnVertexDragEnd += (cp) => VisualPoly.Color = Color;
			VisualPoly.OnVertexMove += OnVertexMove;
            VisualPoly.OnVertexAdd += OnVertexAdd;
            VisualPoly.OnVertexRemove += OnVertexRemove;

            VisualPoly.onKey += OnKey;


            Visual = VisualPoly;

            Visual.onMove += (o, pos) => OnMove(pos);

            if (was_selected)
                Visual.Scene.SelectObject(Visual);

            Visual.onSelected += OnSelected;
            Visual.onUnSelected += OnUnSelected;

            base.CreateVisual();
        }

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            lock (this)
            {
                if (fieldname == nameof(Model.vertices))
                {
                    CreateVisual();

                    return true;
                }


                var v2 = View.Model.position;
                Visual.SetPosition(new SKPoint(v2.x, v2.y));
            }

            return true;
        }

        public void OnMove(SKPoint pos)
        {
            var center = Visual.GetPosition();

            View.CommitBegin();
                View.position.x = center.X;
                View.position.y = center.Y;
            View.CommitEnd();
        }

        public void OnVertexMove(SKControlPoint cp, int index, SKPoint pos)
        {
            // optimize syncing only right segment
            VisualPoly.SyncFromControlPoint(index);

            // special check, model and visual should have the same number of vertices
            if (View.vertices.Count != VisualPoly.Vertices.Count)
            {
                SARGAME.App.logError("PMStructureView.OnVertexMove() different number of vertices");
                return;
            }

            View.CommitBegin();
                View.vertices[index].x = VisualPoly.Vertices[index].X;
                View.vertices[index].y = VisualPoly.Vertices[index].Y;
            View.CommitEnd();

            SelectControlPoint(cp);
        }

        public void OnVertexAdd(int index, SKPoint pos)
        {            
            //View.AddVertex(index, new Vector2f(pos.X - Model.position.x, pos.Y - Model.position.y));

            View.vertices_Add(new Vector2f(pos.X - Model.position.x, pos.Y - Model.position.y), index);

            SelectControlPoint(null);
        }

        public void OnVertexRemove(int index)
        {
            if (Model.vertices.Count <= 3)
                return;

            View.vertices_Remove(index);

            SelectControlPoint(null);
        }

        public void OnSelected()
		{
            SelectControlPoint(null);
        }

        public void OnUnSelected()
		{
            SelectControlPoint(null);
        }        

        private void SelectNextStructure()
        {
            try
            {
                var idx = SARGAME.View.Structures.IndexOf(View);
                SARGAME.iMainWindowUI.SelectedItem = SARGAME.View.Structures[(idx + 1) % SARGAME.View.Structures.Count];

            }
            catch
            { }
         }


        protected override void OnKey(bool is_down, KeyEventArgs args)
		{
            if (is_down)
                switch (args.Key)
			    {             
                    case Key.N:
                        SelectNextStructure();
                        return;
                }

            base.OnKey(is_down, args);   
        }

    }
}
