using System;
using System.Collections.Generic;
using Tabula.SharedObjectMap;

// Wrappers for Tabula.PMCore.Model auto-generated on: 17/04/2025 18:30:43

namespace Tabula.PMCore
{
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public static partial class WrappersExtensions
    {
        public static ModelWrapper getWrapper(this Model m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Model, ModelWrapper>(m);
        public static ScreenWrapper getWrapper(this Screen m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Screen, ScreenWrapper>(m);
        public static SizeIWrapper getWrapper(this SizeI m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<SizeI, SizeIWrapper>(m);
        public static ScreenMarkerWrapper getWrapper(this ScreenMarker m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ScreenMarker, ScreenMarkerWrapper>(m);
        public static Vector2fWrapper getWrapper(this Vector2f m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Vector2f, Vector2fWrapper>(m);
        public static ImageMarkerWrapper getWrapper(this ImageMarker m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ImageMarker, ImageMarkerWrapper>(m);
        public static MediaWrapper getWrapper(this Media m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Media, MediaWrapper>(m);
        public static OutputSurfaceWrapper getWrapper(this OutputSurface m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<OutputSurface, OutputSurfaceWrapper>(m);
        public static Vector2iWrapper getWrapper(this Vector2i m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Vector2i, Vector2iWrapper>(m);
        public static CursorWrapper getWrapper(this Cursor m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Cursor, CursorWrapper>(m);
        public static SizeFWrapper getWrapper(this SizeF m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<SizeF, SizeFWrapper>(m);
        public static PolygonWrapper getWrapper(this Polygon m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Polygon, PolygonWrapper>(m);
        public static EntityWrapper getWrapper(this Entity m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Entity, EntityWrapper>(m);
        public static PresetWrapper getWrapper(this Preset m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Preset, PresetWrapper>(m);
        public static StructureWrapper getWrapper(this Structure m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Structure, StructureWrapper>(m);
        public static StructureParams0Wrapper getWrapper(this StructureParams0 m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<StructureParams0, StructureParams0Wrapper>(m);
        public static StructureParams1Wrapper getWrapper(this StructureParams1 m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<StructureParams1, StructureParams1Wrapper>(m);
        public static StructureEffectWrapper getWrapper(this StructureEffect m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<StructureEffect, StructureEffectWrapper>(m);
        public static ButtonFieldWrapper getWrapper(this ButtonField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ButtonField, ButtonFieldWrapper>(m);
        public static StringFieldWrapper getWrapper(this StringField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<StringField, StringFieldWrapper>(m);
        public static IntegerFieldWrapper getWrapper(this IntegerField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<IntegerField, IntegerFieldWrapper>(m);
        public static FloatFieldWrapper getWrapper(this FloatField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<FloatField, FloatFieldWrapper>(m);
        public static BoolFieldWrapper getWrapper(this BoolField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<BoolField, BoolFieldWrapper>(m);
        public static BoolCardsFieldWrapper getWrapper(this BoolCardsField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<BoolCardsField, BoolCardsFieldWrapper>(m);
        public static SliderFloatFieldWrapper getWrapper(this SliderFloatField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<SliderFloatField, SliderFloatFieldWrapper>(m);
        public static SliderFloatPercentageFieldWrapper getWrapper(this SliderFloatPercentageField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<SliderFloatPercentageField, SliderFloatPercentageFieldWrapper>(m);
        public static ChoiceFieldWrapper getWrapper(this ChoiceField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ChoiceField, ChoiceFieldWrapper>(m);
        public static ChoiceSelectorFieldWrapper getWrapper(this ChoiceSelectorField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ChoiceSelectorField, ChoiceSelectorFieldWrapper>(m);
        public static Position2DFieldWrapper getWrapper(this Position2DField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<Position2DField, Position2DFieldWrapper>(m);
        public static FileFieldWrapper getWrapper(this FileField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<FileField, FileFieldWrapper>(m);
        public static ColorFieldWrapper getWrapper(this ColorField m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<ColorField, ColorFieldWrapper>(m);
    }

    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ModelWrapper : WrapperBase<Model>
    {
        public static implicit operator ModelWrapper(Model m)
        {
            var w = new ModelWrapper(m);
            return w;
        }

        public ModelWrapper() : base(null)
        {}

        public ModelWrapper(Model m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(OutputSurfaces): _OutputSurfaces=null; return true;
               case nameof(Templates): _Templates=null; return true;
               case nameof(Structures): _Structures=null; return true;
               case nameof(Entities): _Entities=null; return true;
               case null:
                   _OutputSurfaces=null;
                   _Templates=null;
                   _Structures=null;
                   _Entities=null;
                   return true;
            }

            return false;
        }

        public System.Int32 Version
        {
            get => _Model.Version;
                   
            set
            {
                _Model.Version = value;
                _Model.CommitUpdate(nameof(Version));
            }
        }


        public System.Int32 TemplateVersion
        {
            get => _Model.TemplateVersion;
                   
            set
            {
                _Model.TemplateVersion = value;
                _Model.CommitUpdate(nameof(TemplateVersion));
            }
        }


        public System.String ProjectName
        {
            get => _Model.ProjectName;
                   
            set
            {
                _Model.ProjectName = value;
                _Model.CommitUpdate(nameof(ProjectName));
            }
        }


        public Tabula.PMCore.Model.Flags ProjectFlags
        {
            get => _Model.ProjectFlags;
                   
            set
            {
                _Model.ProjectFlags = value;
                _Model.CommitUpdate(nameof(ProjectFlags));
            }
        }


        public System.String ScenarioId
        {
            get => _Model.ScenarioId;
                   
            set
            {
                _Model.ScenarioId = value;
                _Model.CommitUpdate(nameof(ScenarioId));
            }
        }


        public System.Boolean ModuleFocus
        {
            get => _Model.ModuleFocus;
                   
            set
            {
                _Model.ModuleFocus = value;
                _Model.CommitUpdate(nameof(ModuleFocus));
            }
        }


        public System.String BackgroundVideoPath
        {
            get => _Model.BackgroundVideoPath;
                   
            set
            {
                _Model.BackgroundVideoPath = value;
                _Model.CommitUpdate(nameof(BackgroundVideoPath));
            }
        }


        private Tabula.PMCore.ScreenWrapper _Screen;
        public Tabula.PMCore.ScreenWrapper Screen
        {
            get
            {
                if (_Screen == null)
                    _Screen = new Tabula.PMCore.ScreenWrapper(_Model.Screen);

                return _Screen;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.Screen != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Screen);

                    _Screen = null;
                    _Model.Screen = null;
                    _Model.CommitUpdate(nameof(Screen));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _Screen = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Screen);
                    _Model.Screen = _Screen._Model;
                    _Model.Screen.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(Screen));
                }
            }
        }

        private Tabula.PMCore.CursorWrapper _Cursor;
        public Tabula.PMCore.CursorWrapper Cursor
        {
            get
            {
                if (_Cursor == null)
                    _Cursor = new Tabula.PMCore.CursorWrapper(_Model.Cursor);

                return _Cursor;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.Cursor != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Cursor);

                    _Cursor = null;
                    _Model.Cursor = null;
                    _Model.CommitUpdate(nameof(Cursor));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _Cursor = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Cursor);
                    _Model.Cursor = _Cursor._Model;
                    _Model.Cursor.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(Cursor));
                }
            }
        }

        private Tabula.PMCore.PolygonWrapper _Polygon;
        public Tabula.PMCore.PolygonWrapper Polygon
        {
            get
            {
                if (_Polygon == null)
                    _Polygon = new Tabula.PMCore.PolygonWrapper(_Model.Polygon);

                return _Polygon;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.Polygon != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Polygon);

                    _Polygon = null;
                    _Model.Polygon = null;
                    _Model.CommitUpdate(nameof(Polygon));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _Polygon = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Polygon);
                    _Model.Polygon = _Polygon._Model;
                    _Model.Polygon.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(Polygon));
                }
            }
        }

        private WrapperGuidObjectList<Tabula.PMCore.OutputSurface, Tabula.PMCore.OutputSurfaceWrapper> _OutputSurfaces;
        public WrapperGuidObjectList<Tabula.PMCore.OutputSurface, Tabula.PMCore.OutputSurfaceWrapper> OutputSurfaces
        {
            get
            {
                if (_OutputSurfaces == null)                                    
                    _OutputSurfaces = new WrapperGuidObjectList<Tabula.PMCore.OutputSurface, Tabula.PMCore.OutputSurfaceWrapper>(_Model.OutputSurfaces, _Model, "OutputSurfaces");                

                return _OutputSurfaces;
            }
        }

        public void OutputSurfaces_Set(List<Tabula.PMCore.OutputSurface> list)
		{
            CommitBegin();
            OutputSurfaces.Clear();
            foreach (var i in list)
                OutputSurfaces.Add(i);
            CommitEnd();
		}

         public void OutputSurfaces_Add(Tabula.PMCore.OutputSurface o)
        {
	        CommitBegin();
            if (_Model.OutputSurfaces == null)
                _Model.OutputSurfaces = new List<Tabula.PMCore.OutputSurface>();

            OutputSurfaces.Add(o);
	        CommitEnd();
        }

        public void OutputSurfaces_RemoveAt(int index)
        {
            if (_Model.OutputSurfaces == null)
                return;
            if (index >= _Model.OutputSurfaces.Count)
                return;

	        CommitBegin();

            var o = _Model.OutputSurfaces[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            OutputSurfaces.RemoveAt(index);
	        CommitEnd();
        }

        public void OutputSurfaces_Clear()
        {
	        if (_Model.OutputSurfaces == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.OutputSurfaces.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.OutputSurfaces[i]);

            _Model.OutputSurfaces.Clear();
            OutputSurfaces.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> _Templates;
        public WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> Templates
        {
            get
            {
                if (_Templates == null)                                    
                    _Templates = new WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper>(_Model.Templates, _Model, "Templates");                

                return _Templates;
            }
        }

        public void Templates_Set(List<Tabula.PMCore.Entity> list)
		{
            CommitBegin();
            Templates.Clear();
            foreach (var i in list)
                Templates.Add(i);
            CommitEnd();
		}

         public void Templates_Add(Tabula.PMCore.Entity o)
        {
	        CommitBegin();
            if (_Model.Templates == null)
                _Model.Templates = new List<Tabula.PMCore.Entity>();

            Templates.Add(o);
	        CommitEnd();
        }

        public void Templates_RemoveAt(int index)
        {
            if (_Model.Templates == null)
                return;
            if (index >= _Model.Templates.Count)
                return;

	        CommitBegin();

            var o = _Model.Templates[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            Templates.RemoveAt(index);
	        CommitEnd();
        }

        public void Templates_Clear()
        {
	        if (_Model.Templates == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.Templates.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Templates[i]);

            _Model.Templates.Clear();
            Templates.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Structure, Tabula.PMCore.StructureWrapper> _Structures;
        public WrapperGuidObjectList<Tabula.PMCore.Structure, Tabula.PMCore.StructureWrapper> Structures
        {
            get
            {
                if (_Structures == null)                                    
                    _Structures = new WrapperGuidObjectList<Tabula.PMCore.Structure, Tabula.PMCore.StructureWrapper>(_Model.Structures, _Model, "Structures");                

                return _Structures;
            }
        }

        public void Structures_Set(List<Tabula.PMCore.Structure> list)
		{
            CommitBegin();
            Structures.Clear();
            foreach (var i in list)
                Structures.Add(i);
            CommitEnd();
		}

         public void Structures_Add(Tabula.PMCore.Structure o)
        {
	        CommitBegin();
            if (_Model.Structures == null)
                _Model.Structures = new List<Tabula.PMCore.Structure>();

            Structures.Add(o);
	        CommitEnd();
        }

        public void Structures_RemoveAt(int index)
        {
            if (_Model.Structures == null)
                return;
            if (index >= _Model.Structures.Count)
                return;

	        CommitBegin();

            var o = _Model.Structures[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            Structures.RemoveAt(index);
	        CommitEnd();
        }

        public void Structures_Clear()
        {
	        if (_Model.Structures == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.Structures.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Structures[i]);

            _Model.Structures.Clear();
            Structures.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> _Entities;
        public WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> Entities
        {
            get
            {
                if (_Entities == null)                                    
                    _Entities = new WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper>(_Model.Entities, _Model, "Entities");                

                return _Entities;
            }
        }

        public void Entities_Set(List<Tabula.PMCore.Entity> list)
		{
            CommitBegin();
            Entities.Clear();
            foreach (var i in list)
                Entities.Add(i);
            CommitEnd();
		}

         public void Entities_Add(Tabula.PMCore.Entity o)
        {
	        CommitBegin();
            if (_Model.Entities == null)
                _Model.Entities = new List<Tabula.PMCore.Entity>();

            Entities.Add(o);
	        CommitEnd();
        }

        public void Entities_RemoveAt(int index)
        {
            if (_Model.Entities == null)
                return;
            if (index >= _Model.Entities.Count)
                return;

	        CommitBegin();

            var o = _Model.Entities[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            Entities.RemoveAt(index);
	        CommitEnd();
        }

        public void Entities_Clear()
        {
	        if (_Model.Entities == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.Entities.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.Entities[i]);

            _Model.Entities.Clear();
            Entities.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ScreenWrapper : WrapperBase<Screen>
    {
        public static implicit operator ScreenWrapper(Screen m)
        {
            var w = new ScreenWrapper(m);
            return w;
        }

        public ScreenWrapper() : base(null)
        {}

        public ScreenWrapper(Screen m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(screen_markers): _screen_markers=null; return true;
               case nameof(image_markers): _image_markers=null; return true;
               case null:
                   _screen_markers=null;
                   _image_markers=null;
                   return true;
            }

            return false;
        }

        public System.Single calibration_image_opacity
        {
            get => _Model.calibration_image_opacity;
                   
            set
            {
                _Model.calibration_image_opacity = value;
                _Model.CommitUpdate(nameof(calibration_image_opacity));
            }
        }


        public System.Single background_mask_opacity
        {
            get => _Model.background_mask_opacity;
                   
            set
            {
                _Model.background_mask_opacity = value;
                _Model.CommitUpdate(nameof(background_mask_opacity));
            }
        }


        private Tabula.PMCore.SizeIWrapper _size;
        public Tabula.PMCore.SizeIWrapper size
        {
            get
            {
                if (_size == null)
                    _size = new Tabula.PMCore.SizeIWrapper(_Model.size);

                return _size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);

                    _size = null;
                    _Model.size = null;
                    _Model.CommitUpdate(nameof(size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);
                    _Model.size = _size._Model;
                    _Model.size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(size));
                }
            }
        }

        private Tabula.PMCore.MediaWrapper _calibration_image;
        public Tabula.PMCore.MediaWrapper calibration_image
        {
            get
            {
                if (_calibration_image == null)
                    _calibration_image = new Tabula.PMCore.MediaWrapper(_Model.calibration_image);

                return _calibration_image;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.calibration_image != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.calibration_image);

                    _calibration_image = null;
                    _Model.calibration_image = null;
                    _Model.CommitUpdate(nameof(calibration_image));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _calibration_image = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.calibration_image);
                    _Model.calibration_image = _calibration_image._Model;
                    _Model.calibration_image.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(calibration_image));
                }
            }
        }

        private WrapperGuidObjectList<Tabula.PMCore.ScreenMarker, Tabula.PMCore.ScreenMarkerWrapper> _screen_markers;
        public WrapperGuidObjectList<Tabula.PMCore.ScreenMarker, Tabula.PMCore.ScreenMarkerWrapper> screen_markers
        {
            get
            {
                if (_screen_markers == null)                                    
                    _screen_markers = new WrapperGuidObjectList<Tabula.PMCore.ScreenMarker, Tabula.PMCore.ScreenMarkerWrapper>(_Model.screen_markers, _Model, "screen_markers");                

                return _screen_markers;
            }
        }

        public void screen_markers_Set(List<Tabula.PMCore.ScreenMarker> list)
		{
            CommitBegin();
            screen_markers.Clear();
            foreach (var i in list)
                screen_markers.Add(i);
            CommitEnd();
		}

         public void screen_markers_Add(Tabula.PMCore.ScreenMarker o)
        {
	        CommitBegin();
            if (_Model.screen_markers == null)
                _Model.screen_markers = new List<Tabula.PMCore.ScreenMarker>();

            screen_markers.Add(o);
	        CommitEnd();
        }

        public void screen_markers_RemoveAt(int index)
        {
            if (_Model.screen_markers == null)
                return;
            if (index >= _Model.screen_markers.Count)
                return;

	        CommitBegin();

            var o = _Model.screen_markers[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            screen_markers.RemoveAt(index);
	        CommitEnd();
        }

        public void screen_markers_Clear()
        {
	        if (_Model.screen_markers == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.screen_markers.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.screen_markers[i]);

            _Model.screen_markers.Clear();
            screen_markers.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.ImageMarker, Tabula.PMCore.ImageMarkerWrapper> _image_markers;
        public WrapperGuidObjectList<Tabula.PMCore.ImageMarker, Tabula.PMCore.ImageMarkerWrapper> image_markers
        {
            get
            {
                if (_image_markers == null)                                    
                    _image_markers = new WrapperGuidObjectList<Tabula.PMCore.ImageMarker, Tabula.PMCore.ImageMarkerWrapper>(_Model.image_markers, _Model, "image_markers");                

                return _image_markers;
            }
        }

        public void image_markers_Set(List<Tabula.PMCore.ImageMarker> list)
		{
            CommitBegin();
            image_markers.Clear();
            foreach (var i in list)
                image_markers.Add(i);
            CommitEnd();
		}

         public void image_markers_Add(Tabula.PMCore.ImageMarker o)
        {
	        CommitBegin();
            if (_Model.image_markers == null)
                _Model.image_markers = new List<Tabula.PMCore.ImageMarker>();

            image_markers.Add(o);
	        CommitEnd();
        }

        public void image_markers_RemoveAt(int index)
        {
            if (_Model.image_markers == null)
                return;
            if (index >= _Model.image_markers.Count)
                return;

	        CommitBegin();

            var o = _Model.image_markers[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            image_markers.RemoveAt(index);
	        CommitEnd();
        }

        public void image_markers_Clear()
        {
	        if (_Model.image_markers == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.image_markers.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.image_markers[i]);

            _Model.image_markers.Clear();
            image_markers.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class SizeIWrapper : WrapperBase<SizeI>
    {
        public static implicit operator SizeIWrapper(SizeI m)
        {
            var w = new SizeIWrapper(m);
            return w;
        }

        public SizeIWrapper() : base(null)
        {}

        public SizeIWrapper(SizeI m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Int32 width
        {
            get => _Model.width;
                   
            set
            {
                _Model.width = value;
                _Model.CommitUpdate(nameof(width));
            }
        }


        public System.Int32 height
        {
            get => _Model.height;
                   
            set
            {
                _Model.height = value;
                _Model.CommitUpdate(nameof(height));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ScreenMarkerWrapper : WrapperBase<ScreenMarker>
    {
        public static implicit operator ScreenMarkerWrapper(ScreenMarker m)
        {
            var w = new ScreenMarkerWrapper(m);
            return w;
        }

        public ScreenMarkerWrapper() : base(null)
        {}

        public ScreenMarkerWrapper(ScreenMarker m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class Vector2fWrapper : WrapperBase<Vector2f>
    {
        public static implicit operator Vector2fWrapper(Vector2f m)
        {
            var w = new Vector2fWrapper(m);
            return w;
        }

        public Vector2fWrapper() : base(null)
        {}

        public Vector2fWrapper(Vector2f m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Single x
        {
            get => _Model.x;
                   
            set
            {
                _Model.x = value;
                _Model.CommitUpdate(nameof(x));
            }
        }


        public System.Single y
        {
            get => _Model.y;
                   
            set
            {
                _Model.y = value;
                _Model.CommitUpdate(nameof(y));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ImageMarkerWrapper : WrapperBase<ImageMarker>
    {
        public static implicit operator ImageMarkerWrapper(ImageMarker m)
        {
            var w = new ImageMarkerWrapper(m);
            return w;
        }

        public ImageMarkerWrapper() : base(null)
        {}

        public ImageMarkerWrapper(ImageMarker m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Boolean enabled
        {
            get => _Model.enabled;
                   
            set
            {
                _Model.enabled = value;
                _Model.CommitUpdate(nameof(enabled));
            }
        }


        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class MediaWrapper : WrapperBase<Media>
    {
        public static implicit operator MediaWrapper(Media m)
        {
            var w = new MediaWrapper(m);
            return w;
        }

        public MediaWrapper() : base(null)
        {}

        public MediaWrapper(Media m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.String file
        {
            get => _Model.file;
                   
            set
            {
                _Model.file = value;
                _Model.CommitUpdate(nameof(file));
            }
        }


        public System.DateTime time
        {
            get => _Model.time;
                   
            set
            {
                _Model.time = value;
                _Model.CommitUpdate(nameof(time));
            }
        }


        private Tabula.PMCore.SizeIWrapper _image_size;
        public Tabula.PMCore.SizeIWrapper image_size
        {
            get
            {
                if (_image_size == null)
                    _image_size = new Tabula.PMCore.SizeIWrapper(_Model.image_size);

                return _image_size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.image_size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.image_size);

                    _image_size = null;
                    _Model.image_size = null;
                    _Model.CommitUpdate(nameof(image_size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _image_size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.image_size);
                    _Model.image_size = _image_size._Model;
                    _Model.image_size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(image_size));
                }
            }
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class OutputSurfaceWrapper : WrapperBase<OutputSurface>
    {
        public static implicit operator OutputSurfaceWrapper(OutputSurface m)
        {
            var w = new OutputSurfaceWrapper(m);
            return w;
        }

        public OutputSurfaceWrapper() : base(null)
        {}

        public OutputSurfaceWrapper(OutputSurface m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(vertices): _vertices=null; return true;
               case null:
                   _vertices=null;
                   return true;
            }

            return false;
        }

        public System.Single aspect
        {
            get => _Model.aspect;
                   
            set
            {
                _Model.aspect = value;
                _Model.CommitUpdate(nameof(aspect));
            }
        }


        public System.Single x_tiling
        {
            get => _Model.x_tiling;
                   
            set
            {
                _Model.x_tiling = value;
                _Model.CommitUpdate(nameof(x_tiling));
            }
        }


        public System.Single x_offset
        {
            get => _Model.x_offset;
                   
            set
            {
                _Model.x_offset = value;
                _Model.CommitUpdate(nameof(x_offset));
            }
        }


        public System.Single y_tiling
        {
            get => _Model.y_tiling;
                   
            set
            {
                _Model.y_tiling = value;
                _Model.CommitUpdate(nameof(y_tiling));
            }
        }


        public System.Single y_offset
        {
            get => _Model.y_offset;
                   
            set
            {
                _Model.y_offset = value;
                _Model.CommitUpdate(nameof(y_offset));
            }
        }


        private WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper> _vertices;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper> vertices
        {
            get
            {
                if (_vertices == null)                                    
                    _vertices = new WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper>(_Model.vertices, _Model, "vertices");                

                return _vertices;
            }
        }

        public void vertices_Set(List<Tabula.PMCore.Vector2i> list)
		{
            CommitBegin();
            vertices.Clear();
            foreach (var i in list)
                vertices.Add(i);
            CommitEnd();
		}

         public void vertices_Add(Tabula.PMCore.Vector2i o)
        {
	        CommitBegin();
            if (_Model.vertices == null)
                _Model.vertices = new List<Tabula.PMCore.Vector2i>();

            vertices.Add(o);
	        CommitEnd();
        }

        public void vertices_RemoveAt(int index)
        {
            if (_Model.vertices == null)
                return;
            if (index >= _Model.vertices.Count)
                return;

	        CommitBegin();

            var o = _Model.vertices[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            vertices.RemoveAt(index);
	        CommitEnd();
        }

        public void vertices_Clear()
        {
	        if (_Model.vertices == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.vertices.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.vertices[i]);

            _Model.vertices.Clear();
            vertices.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class Vector2iWrapper : WrapperBase<Vector2i>
    {
        public static implicit operator Vector2iWrapper(Vector2i m)
        {
            var w = new Vector2iWrapper(m);
            return w;
        }

        public Vector2iWrapper() : base(null)
        {}

        public Vector2iWrapper(Vector2i m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Int32 x
        {
            get => _Model.x;
                   
            set
            {
                _Model.x = value;
                _Model.CommitUpdate(nameof(x));
            }
        }


        public System.Int32 y
        {
            get => _Model.y;
                   
            set
            {
                _Model.y = value;
                _Model.CommitUpdate(nameof(y));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class CursorWrapper : WrapperBase<Cursor>
    {
        public static implicit operator CursorWrapper(Cursor m)
        {
            var w = new CursorWrapper(m);
            return w;
        }

        public CursorWrapper() : base(null)
        {}

        public CursorWrapper(Cursor m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(points): _points=null; return true;
               case null:
                   _points=null;
                   return true;
            }

            return false;
        }

        public System.Boolean draw
        {
            get => _Model.draw;
                   
            set
            {
                _Model.draw = value;
                _Model.CommitUpdate(nameof(draw));
            }
        }


        public System.Single scale
        {
            get => _Model.scale;
                   
            set
            {
                _Model.scale = value;
                _Model.CommitUpdate(nameof(scale));
            }
        }


        public System.Single rotation
        {
            get => _Model.rotation;
                   
            set
            {
                _Model.rotation = value;
                _Model.CommitUpdate(nameof(rotation));
            }
        }


        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }

        private Tabula.PMCore.SizeFWrapper _size;
        public Tabula.PMCore.SizeFWrapper size
        {
            get
            {
                if (_size == null)
                    _size = new Tabula.PMCore.SizeFWrapper(_Model.size);

                return _size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);

                    _size = null;
                    _Model.size = null;
                    _Model.CommitUpdate(nameof(size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);
                    _Model.size = _size._Model;
                    _Model.size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(size));
                }
            }
        }

        private Tabula.PMCore.Vector2fWrapper _offset;
        public Tabula.PMCore.Vector2fWrapper offset
        {
            get
            {
                if (_offset == null)
                    _offset = new Tabula.PMCore.Vector2fWrapper(_Model.offset);

                return _offset;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.offset != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);

                    _offset = null;
                    _Model.offset = null;
                    _Model.CommitUpdate(nameof(offset));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _offset = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);
                    _Model.offset = _offset._Model;
                    _Model.offset.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(offset));
                }
            }
        }

        private WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> _points;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> points
        {
            get
            {
                if (_points == null)                                    
                    _points = new WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper>(_Model.points, _Model, "points");                

                return _points;
            }
        }

        public void points_Set(List<Tabula.PMCore.Vector2f> list)
		{
            CommitBegin();
            points.Clear();
            foreach (var i in list)
                points.Add(i);
            CommitEnd();
		}

         public void points_Add(Tabula.PMCore.Vector2f o)
        {
	        CommitBegin();
            if (_Model.points == null)
                _Model.points = new List<Tabula.PMCore.Vector2f>();

            points.Add(o);
	        CommitEnd();
        }

        public void points_RemoveAt(int index)
        {
            if (_Model.points == null)
                return;
            if (index >= _Model.points.Count)
                return;

	        CommitBegin();

            var o = _Model.points[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            points.RemoveAt(index);
	        CommitEnd();
        }

        public void points_Clear()
        {
	        if (_Model.points == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.points.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.points[i]);

            _Model.points.Clear();
            points.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class SizeFWrapper : WrapperBase<SizeF>
    {
        public static implicit operator SizeFWrapper(SizeF m)
        {
            var w = new SizeFWrapper(m);
            return w;
        }

        public SizeFWrapper() : base(null)
        {}

        public SizeFWrapper(SizeF m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Single width
        {
            get => _Model.width;
                   
            set
            {
                _Model.width = value;
                _Model.CommitUpdate(nameof(width));
            }
        }


        public System.Single height
        {
            get => _Model.height;
                   
            set
            {
                _Model.height = value;
                _Model.CommitUpdate(nameof(height));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class PolygonWrapper : WrapperBase<Polygon>
    {
        public static implicit operator PolygonWrapper(Polygon m)
        {
            var w = new PolygonWrapper(m);
            return w;
        }

        public PolygonWrapper() : base(null)
        {}

        public PolygonWrapper(Polygon m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(vertices): _vertices=null; return true;
               case null:
                   _vertices=null;
                   return true;
            }

            return false;
        }

        public System.Boolean enabled
        {
            get => _Model.enabled;
                   
            set
            {
                _Model.enabled = value;
                _Model.CommitUpdate(nameof(enabled));
            }
        }


        private WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper> _vertices;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper> vertices
        {
            get
            {
                if (_vertices == null)                                    
                    _vertices = new WrapperGuidObjectList<Tabula.PMCore.Vector2i, Tabula.PMCore.Vector2iWrapper>(_Model.vertices, _Model, "vertices");                

                return _vertices;
            }
        }

        public void vertices_Set(List<Tabula.PMCore.Vector2i> list)
		{
            CommitBegin();
            vertices.Clear();
            foreach (var i in list)
                vertices.Add(i);
            CommitEnd();
		}

         public void vertices_Add(Tabula.PMCore.Vector2i o)
        {
	        CommitBegin();
            if (_Model.vertices == null)
                _Model.vertices = new List<Tabula.PMCore.Vector2i>();

            vertices.Add(o);
	        CommitEnd();
        }

        public void vertices_RemoveAt(int index)
        {
            if (_Model.vertices == null)
                return;
            if (index >= _Model.vertices.Count)
                return;

	        CommitBegin();

            var o = _Model.vertices[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            vertices.RemoveAt(index);
	        CommitEnd();
        }

        public void vertices_Clear()
        {
	        if (_Model.vertices == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.vertices.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.vertices[i]);

            _Model.vertices.Clear();
            vertices.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class EntityWrapper : WrapperBase<Entity>
    {
        public static implicit operator EntityWrapper(Entity m)
        {
            var w = new EntityWrapper(m);
            return w;
        }

        public EntityWrapper() : base(null)
        {}

        public EntityWrapper(Entity m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(fields): _fields=null; return true;
               case nameof(fields_shown): _fields_shown=null; return true;
               case nameof(props): _props=null; return true;
               case nameof(items): _items=null; return true;
               case nameof(presets): _presets=null; return true;
               case nameof(points): _points=null; return true;
               case null:
                   _fields=null;
                   _fields_shown=null;
                   _props=null;
                   _items=null;
                   _presets=null;
                   _points=null;
                   return true;
            }

            return false;
        }

        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String type
        {
            get => _Model.type;
                   
            set
            {
                _Model.type = value;
                _Model.CommitUpdate(nameof(type));
            }
        }


        public System.String container
        {
            get => _Model.container;
                   
            set
            {
                _Model.container = value;
                _Model.CommitUpdate(nameof(container));
            }
        }


        public System.String icon
        {
            get => _Model.icon;
                   
            set
            {
                _Model.icon = value;
                _Model.CommitUpdate(nameof(icon));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Boolean active
        {
            get => _Model.active;
                   
            set
            {
                _Model.active = value;
                _Model.CommitUpdate(nameof(active));
            }
        }


        public System.Boolean sync_fields
        {
            get => _Model.sync_fields;
                   
            set
            {
                _Model.sync_fields = value;
                _Model.CommitUpdate(nameof(sync_fields));
            }
        }


        public Tabula.PMCore.Entity.Flags flags
        {
            get => _Model.flags;
                   
            set
            {
                _Model.flags = value;
                _Model.CommitUpdate(nameof(flags));
            }
        }


        public System.String preset
        {
            get => _Model.preset;
                   
            set
            {
                _Model.preset = value;
                _Model.CommitUpdate(nameof(preset));
            }
        }


        public System.Single scale
        {
            get => _Model.scale;
                   
            set
            {
                _Model.scale = value;
                _Model.CommitUpdate(nameof(scale));
            }
        }


        public System.Single rotation
        {
            get => _Model.rotation;
                   
            set
            {
                _Model.rotation = value;
                _Model.CommitUpdate(nameof(rotation));
            }
        }


        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }

        private Tabula.PMCore.SizeFWrapper _size;
        public Tabula.PMCore.SizeFWrapper size
        {
            get
            {
                if (_size == null)
                    _size = new Tabula.PMCore.SizeFWrapper(_Model.size);

                return _size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);

                    _size = null;
                    _Model.size = null;
                    _Model.CommitUpdate(nameof(size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);
                    _Model.size = _size._Model;
                    _Model.size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(size));
                }
            }
        }

        private Tabula.PMCore.Vector2fWrapper _offset;
        public Tabula.PMCore.Vector2fWrapper offset
        {
            get
            {
                if (_offset == null)
                    _offset = new Tabula.PMCore.Vector2fWrapper(_Model.offset);

                return _offset;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.offset != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);

                    _offset = null;
                    _Model.offset = null;
                    _Model.CommitUpdate(nameof(offset));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _offset = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);
                    _Model.offset = _offset._Model;
                    _Model.offset.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(offset));
                }
            }
        }

        private WrapperList<System.Object> _fields;
        public WrapperList<System.Object> fields
        {
            get
            {
                if (_fields == null)
                    _fields = new  WrapperList<System.Object>(_Model.fields, _Model, "fields");

                return _fields;
            }
        }

        public void fields_Set(List<System.Object> list)
		{
            CommitBegin();
            fields.Clear();
            foreach (var i in list)
                fields.Add(i);
            CommitEnd();
		}


        public void fields_Add(System.Object o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields == null)
                _Model.fields = new List<System.Object>();

            fields.Add(o);
	        CommitEnd();
        }

        public void fields_RemoveAt(int index)
        {
            if (_Model.fields == null)
                return;
            if (index >= _Model.fields.Count)
                return;

	        CommitBegin();

            var o = _Model.fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_Clear()
        {
	        if (_Model.fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields.Count; i++)
                if (_Model.fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields[i]);

            _Model.fields = new List<System.Object>();
            fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _fields_shown;
        public WrapperList<System.String> fields_shown
        {
            get
            {
                if (_fields_shown == null)
                    _fields_shown = new  WrapperList<System.String>(_Model.fields_shown, _Model, "fields_shown");

                return _fields_shown;
            }
        }

        public void fields_shown_Set(List<System.String> list)
		{
            CommitBegin();
            fields_shown.Clear();
            foreach (var i in list)
                fields_shown.Add(i);
            CommitEnd();
		}


        public void fields_shown_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields_shown == null)
                _Model.fields_shown = new List<System.String>();

            fields_shown.Add(o);
	        CommitEnd();
        }

        public void fields_shown_RemoveAt(int index)
        {
            if (_Model.fields_shown == null)
                return;
            if (index >= _Model.fields_shown.Count)
                return;

	        CommitBegin();

            var o = _Model.fields_shown[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields_shown.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_shown_Clear()
        {
	        if (_Model.fields_shown == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields_shown.Count; i++)
                if (_Model.fields_shown[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields_shown[i]);

            _Model.fields_shown = new List<System.String>();
            fields_shown.Clear();

	        CommitEnd();
        }

        private WrapperDictionary<System.String,System.Object> _props;
        public WrapperDictionary<System.String,System.Object> props
        {
            get
            {
                if (_props == null)
                    _props = new  WrapperDictionary<System.String,System.Object>(_Model.props, _Model, "props");

                return _props;
            }
        }  

        public void props_Set(Dictionary<System.String,System.Object> dict)
        {
            CommitBegin();

            if (_Model.props == null)
                _Model.props = new Dictionary<System.String,System.Object>();
            else
            {
                foreach (var kvp in _Model.props)
                {
                    if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				        SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(kvp.Value);
                }

                _Model.props.Clear();
            }

            foreach (var kvp in dict)
            {
                if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				    SharedObjectMap.SharedObjectMap.AddAllGuidObjects(kvp.Value);

                props.Add(kvp.Key, kvp.Value);
            }

            CommitEnd();
        }

        // TODO: Add, Remove, Clear

        private WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> _items;
        public WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> items
        {
            get
            {
                if (_items == null)                                    
                    _items = new WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper>(_Model.items, _Model, "items");                

                return _items;
            }
        }

        public void items_Set(List<Tabula.PMCore.Entity> list)
		{
            CommitBegin();
            items.Clear();
            foreach (var i in list)
                items.Add(i);
            CommitEnd();
		}

         public void items_Add(Tabula.PMCore.Entity o)
        {
	        CommitBegin();
            if (_Model.items == null)
                _Model.items = new List<Tabula.PMCore.Entity>();

            items.Add(o);
	        CommitEnd();
        }

        public void items_RemoveAt(int index)
        {
            if (_Model.items == null)
                return;
            if (index >= _Model.items.Count)
                return;

	        CommitBegin();

            var o = _Model.items[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            items.RemoveAt(index);
	        CommitEnd();
        }

        public void items_Clear()
        {
	        if (_Model.items == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.items.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.items[i]);

            _Model.items.Clear();
            items.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> _presets;
        public WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> presets
        {
            get
            {
                if (_presets == null)                                    
                    _presets = new WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper>(_Model.presets, _Model, "presets");                

                return _presets;
            }
        }

        public void presets_Set(List<Tabula.PMCore.Preset> list)
		{
            CommitBegin();
            presets.Clear();
            foreach (var i in list)
                presets.Add(i);
            CommitEnd();
		}

         public void presets_Add(Tabula.PMCore.Preset o)
        {
	        CommitBegin();
            if (_Model.presets == null)
                _Model.presets = new List<Tabula.PMCore.Preset>();

            presets.Add(o);
	        CommitEnd();
        }

        public void presets_RemoveAt(int index)
        {
            if (_Model.presets == null)
                return;
            if (index >= _Model.presets.Count)
                return;

	        CommitBegin();

            var o = _Model.presets[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            presets.RemoveAt(index);
	        CommitEnd();
        }

        public void presets_Clear()
        {
	        if (_Model.presets == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.presets.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.presets[i]);

            _Model.presets.Clear();
            presets.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> _points;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> points
        {
            get
            {
                if (_points == null)                                    
                    _points = new WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper>(_Model.points, _Model, "points");                

                return _points;
            }
        }

        public void points_Set(List<Tabula.PMCore.Vector2f> list)
		{
            CommitBegin();
            points.Clear();
            foreach (var i in list)
                points.Add(i);
            CommitEnd();
		}

         public void points_Add(Tabula.PMCore.Vector2f o)
        {
	        CommitBegin();
            if (_Model.points == null)
                _Model.points = new List<Tabula.PMCore.Vector2f>();

            points.Add(o);
	        CommitEnd();
        }

        public void points_RemoveAt(int index)
        {
            if (_Model.points == null)
                return;
            if (index >= _Model.points.Count)
                return;

	        CommitBegin();

            var o = _Model.points[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            points.RemoveAt(index);
	        CommitEnd();
        }

        public void points_Clear()
        {
	        if (_Model.points == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.points.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.points[i]);

            _Model.points.Clear();
            points.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class PresetWrapper : WrapperBase<Preset>
    {
        public static implicit operator PresetWrapper(Preset m)
        {
            var w = new PresetWrapper(m);
            return w;
        }

        public PresetWrapper() : base(null)
        {}

        public PresetWrapper(Preset m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(fields): _fields=null; return true;
               case null:
                   _fields=null;
                   return true;
            }

            return false;
        }

        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.Boolean is_readonly
        {
            get => _Model.is_readonly;
                   
            set
            {
                _Model.is_readonly = value;
                _Model.CommitUpdate(nameof(is_readonly));
            }
        }


        private WrapperDictionary<System.String,System.Object> _fields;
        public WrapperDictionary<System.String,System.Object> fields
        {
            get
            {
                if (_fields == null)
                    _fields = new  WrapperDictionary<System.String,System.Object>(_Model.fields, _Model, "fields");

                return _fields;
            }
        }  

        public void fields_Set(Dictionary<System.String,System.Object> dict)
        {
            CommitBegin();

            if (_Model.fields == null)
                _Model.fields = new Dictionary<System.String,System.Object>();
            else
            {
                foreach (var kvp in _Model.fields)
                {
                    if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				        SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(kvp.Value);
                }

                _Model.fields.Clear();
            }

            foreach (var kvp in dict)
            {
                if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				    SharedObjectMap.SharedObjectMap.AddAllGuidObjects(kvp.Value);

                fields.Add(kvp.Key, kvp.Value);
            }

            CommitEnd();
        }

        // TODO: Add, Remove, Clear
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class StructureWrapper : WrapperBase<Structure>
    {
        public static implicit operator StructureWrapper(Structure m)
        {
            var w = new StructureWrapper(m);
            return w;
        }

        public StructureWrapper() : base(null)
        {}

        public StructureWrapper(Structure m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(fields): _fields=null; return true;
               case nameof(fields_shown): _fields_shown=null; return true;
               case nameof(props): _props=null; return true;
               case nameof(vertices): _vertices=null; return true;
               case nameof(effects): _effects=null; return true;
               case nameof(items): _items=null; return true;
               case nameof(presets): _presets=null; return true;
               case nameof(points): _points=null; return true;
               case null:
                   _fields=null;
                   _fields_shown=null;
                   _props=null;
                   _vertices=null;
                   _effects=null;
                   _items=null;
                   _presets=null;
                   _points=null;
                   return true;
            }

            return false;
        }

        public System.Boolean is_physical
        {
            get => _Model.is_physical;
                   
            set
            {
                _Model.is_physical = value;
                _Model.CommitUpdate(nameof(is_physical));
            }
        }


        public System.Boolean is_mask
        {
            get => _Model.is_mask;
                   
            set
            {
                _Model.is_mask = value;
                _Model.CommitUpdate(nameof(is_mask));
            }
        }


        public System.Boolean selected_for_editing
        {
            get => _Model.selected_for_editing;
                   
            set
            {
                _Model.selected_for_editing = value;
                _Model.CommitUpdate(nameof(selected_for_editing));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String type
        {
            get => _Model.type;
                   
            set
            {
                _Model.type = value;
                _Model.CommitUpdate(nameof(type));
            }
        }


        public System.String container
        {
            get => _Model.container;
                   
            set
            {
                _Model.container = value;
                _Model.CommitUpdate(nameof(container));
            }
        }


        public System.String icon
        {
            get => _Model.icon;
                   
            set
            {
                _Model.icon = value;
                _Model.CommitUpdate(nameof(icon));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Boolean active
        {
            get => _Model.active;
                   
            set
            {
                _Model.active = value;
                _Model.CommitUpdate(nameof(active));
            }
        }


        public System.Boolean sync_fields
        {
            get => _Model.sync_fields;
                   
            set
            {
                _Model.sync_fields = value;
                _Model.CommitUpdate(nameof(sync_fields));
            }
        }


        public Tabula.PMCore.Entity.Flags flags
        {
            get => _Model.flags;
                   
            set
            {
                _Model.flags = value;
                _Model.CommitUpdate(nameof(flags));
            }
        }


        public System.String preset
        {
            get => _Model.preset;
                   
            set
            {
                _Model.preset = value;
                _Model.CommitUpdate(nameof(preset));
            }
        }


        public System.Single scale
        {
            get => _Model.scale;
                   
            set
            {
                _Model.scale = value;
                _Model.CommitUpdate(nameof(scale));
            }
        }


        public System.Single rotation
        {
            get => _Model.rotation;
                   
            set
            {
                _Model.rotation = value;
                _Model.CommitUpdate(nameof(rotation));
            }
        }


        private Tabula.PMCore.StructureParams0Wrapper _params0;
        public Tabula.PMCore.StructureParams0Wrapper params0
        {
            get
            {
                if (_params0 == null)
                    _params0 = new Tabula.PMCore.StructureParams0Wrapper(_Model.params0);

                return _params0;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.params0 != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.params0);

                    _params0 = null;
                    _Model.params0 = null;
                    _Model.CommitUpdate(nameof(params0));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _params0 = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.params0);
                    _Model.params0 = _params0._Model;
                    _Model.params0.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(params0));
                }
            }
        }

        private Tabula.PMCore.StructureParams1Wrapper _params1;
        public Tabula.PMCore.StructureParams1Wrapper params1
        {
            get
            {
                if (_params1 == null)
                    _params1 = new Tabula.PMCore.StructureParams1Wrapper(_Model.params1);

                return _params1;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.params1 != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.params1);

                    _params1 = null;
                    _Model.params1 = null;
                    _Model.CommitUpdate(nameof(params1));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _params1 = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.params1);
                    _Model.params1 = _params1._Model;
                    _Model.params1.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(params1));
                }
            }
        }

        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }

        private Tabula.PMCore.SizeFWrapper _size;
        public Tabula.PMCore.SizeFWrapper size
        {
            get
            {
                if (_size == null)
                    _size = new Tabula.PMCore.SizeFWrapper(_Model.size);

                return _size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);

                    _size = null;
                    _Model.size = null;
                    _Model.CommitUpdate(nameof(size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);
                    _Model.size = _size._Model;
                    _Model.size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(size));
                }
            }
        }

        private Tabula.PMCore.Vector2fWrapper _offset;
        public Tabula.PMCore.Vector2fWrapper offset
        {
            get
            {
                if (_offset == null)
                    _offset = new Tabula.PMCore.Vector2fWrapper(_Model.offset);

                return _offset;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.offset != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);

                    _offset = null;
                    _Model.offset = null;
                    _Model.CommitUpdate(nameof(offset));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _offset = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);
                    _Model.offset = _offset._Model;
                    _Model.offset.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(offset));
                }
            }
        }

        private WrapperList<System.Object> _fields;
        public WrapperList<System.Object> fields
        {
            get
            {
                if (_fields == null)
                    _fields = new  WrapperList<System.Object>(_Model.fields, _Model, "fields");

                return _fields;
            }
        }

        public void fields_Set(List<System.Object> list)
		{
            CommitBegin();
            fields.Clear();
            foreach (var i in list)
                fields.Add(i);
            CommitEnd();
		}


        public void fields_Add(System.Object o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields == null)
                _Model.fields = new List<System.Object>();

            fields.Add(o);
	        CommitEnd();
        }

        public void fields_RemoveAt(int index)
        {
            if (_Model.fields == null)
                return;
            if (index >= _Model.fields.Count)
                return;

	        CommitBegin();

            var o = _Model.fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_Clear()
        {
	        if (_Model.fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields.Count; i++)
                if (_Model.fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields[i]);

            _Model.fields = new List<System.Object>();
            fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _fields_shown;
        public WrapperList<System.String> fields_shown
        {
            get
            {
                if (_fields_shown == null)
                    _fields_shown = new  WrapperList<System.String>(_Model.fields_shown, _Model, "fields_shown");

                return _fields_shown;
            }
        }

        public void fields_shown_Set(List<System.String> list)
		{
            CommitBegin();
            fields_shown.Clear();
            foreach (var i in list)
                fields_shown.Add(i);
            CommitEnd();
		}


        public void fields_shown_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields_shown == null)
                _Model.fields_shown = new List<System.String>();

            fields_shown.Add(o);
	        CommitEnd();
        }

        public void fields_shown_RemoveAt(int index)
        {
            if (_Model.fields_shown == null)
                return;
            if (index >= _Model.fields_shown.Count)
                return;

	        CommitBegin();

            var o = _Model.fields_shown[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields_shown.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_shown_Clear()
        {
	        if (_Model.fields_shown == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields_shown.Count; i++)
                if (_Model.fields_shown[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields_shown[i]);

            _Model.fields_shown = new List<System.String>();
            fields_shown.Clear();

	        CommitEnd();
        }

        private WrapperDictionary<System.String,System.Object> _props;
        public WrapperDictionary<System.String,System.Object> props
        {
            get
            {
                if (_props == null)
                    _props = new  WrapperDictionary<System.String,System.Object>(_Model.props, _Model, "props");

                return _props;
            }
        }  

        public void props_Set(Dictionary<System.String,System.Object> dict)
        {
            CommitBegin();

            if (_Model.props == null)
                _Model.props = new Dictionary<System.String,System.Object>();
            else
            {
                foreach (var kvp in _Model.props)
                {
                    if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				        SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(kvp.Value);
                }

                _Model.props.Clear();
            }

            foreach (var kvp in dict)
            {
                if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				    SharedObjectMap.SharedObjectMap.AddAllGuidObjects(kvp.Value);

                props.Add(kvp.Key, kvp.Value);
            }

            CommitEnd();
        }

        // TODO: Add, Remove, Clear

        private WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> _vertices;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> vertices
        {
            get
            {
                if (_vertices == null)                                    
                    _vertices = new WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper>(_Model.vertices, _Model, "vertices");                

                return _vertices;
            }
        }

        public void vertices_Set(List<Tabula.PMCore.Vector2f> list)
		{
            CommitBegin();
            vertices.Clear();
            foreach (var i in list)
                vertices.Add(i);
            CommitEnd();
		}

         public void vertices_Add(Tabula.PMCore.Vector2f o)
        {
	        CommitBegin();
            if (_Model.vertices == null)
                _Model.vertices = new List<Tabula.PMCore.Vector2f>();

            vertices.Add(o);
	        CommitEnd();
        }

        public void vertices_RemoveAt(int index)
        {
            if (_Model.vertices == null)
                return;
            if (index >= _Model.vertices.Count)
                return;

	        CommitBegin();

            var o = _Model.vertices[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            vertices.RemoveAt(index);
	        CommitEnd();
        }

        public void vertices_Clear()
        {
	        if (_Model.vertices == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.vertices.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.vertices[i]);

            _Model.vertices.Clear();
            vertices.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.StructureEffect, Tabula.PMCore.StructureEffectWrapper> _effects;
        public WrapperGuidObjectList<Tabula.PMCore.StructureEffect, Tabula.PMCore.StructureEffectWrapper> effects
        {
            get
            {
                if (_effects == null)                                    
                    _effects = new WrapperGuidObjectList<Tabula.PMCore.StructureEffect, Tabula.PMCore.StructureEffectWrapper>(_Model.effects, _Model, "effects");                

                return _effects;
            }
        }

        public void effects_Set(List<Tabula.PMCore.StructureEffect> list)
		{
            CommitBegin();
            effects.Clear();
            foreach (var i in list)
                effects.Add(i);
            CommitEnd();
		}

         public void effects_Add(Tabula.PMCore.StructureEffect o)
        {
	        CommitBegin();
            if (_Model.effects == null)
                _Model.effects = new List<Tabula.PMCore.StructureEffect>();

            effects.Add(o);
	        CommitEnd();
        }

        public void effects_RemoveAt(int index)
        {
            if (_Model.effects == null)
                return;
            if (index >= _Model.effects.Count)
                return;

	        CommitBegin();

            var o = _Model.effects[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            effects.RemoveAt(index);
	        CommitEnd();
        }

        public void effects_Clear()
        {
	        if (_Model.effects == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.effects.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.effects[i]);

            _Model.effects.Clear();
            effects.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> _items;
        public WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> items
        {
            get
            {
                if (_items == null)                                    
                    _items = new WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper>(_Model.items, _Model, "items");                

                return _items;
            }
        }

        public void items_Set(List<Tabula.PMCore.Entity> list)
		{
            CommitBegin();
            items.Clear();
            foreach (var i in list)
                items.Add(i);
            CommitEnd();
		}

         public void items_Add(Tabula.PMCore.Entity o)
        {
	        CommitBegin();
            if (_Model.items == null)
                _Model.items = new List<Tabula.PMCore.Entity>();

            items.Add(o);
	        CommitEnd();
        }

        public void items_RemoveAt(int index)
        {
            if (_Model.items == null)
                return;
            if (index >= _Model.items.Count)
                return;

	        CommitBegin();

            var o = _Model.items[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            items.RemoveAt(index);
	        CommitEnd();
        }

        public void items_Clear()
        {
	        if (_Model.items == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.items.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.items[i]);

            _Model.items.Clear();
            items.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> _presets;
        public WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> presets
        {
            get
            {
                if (_presets == null)                                    
                    _presets = new WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper>(_Model.presets, _Model, "presets");                

                return _presets;
            }
        }

        public void presets_Set(List<Tabula.PMCore.Preset> list)
		{
            CommitBegin();
            presets.Clear();
            foreach (var i in list)
                presets.Add(i);
            CommitEnd();
		}

         public void presets_Add(Tabula.PMCore.Preset o)
        {
	        CommitBegin();
            if (_Model.presets == null)
                _Model.presets = new List<Tabula.PMCore.Preset>();

            presets.Add(o);
	        CommitEnd();
        }

        public void presets_RemoveAt(int index)
        {
            if (_Model.presets == null)
                return;
            if (index >= _Model.presets.Count)
                return;

	        CommitBegin();

            var o = _Model.presets[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            presets.RemoveAt(index);
	        CommitEnd();
        }

        public void presets_Clear()
        {
	        if (_Model.presets == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.presets.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.presets[i]);

            _Model.presets.Clear();
            presets.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> _points;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> points
        {
            get
            {
                if (_points == null)                                    
                    _points = new WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper>(_Model.points, _Model, "points");                

                return _points;
            }
        }

        public void points_Set(List<Tabula.PMCore.Vector2f> list)
		{
            CommitBegin();
            points.Clear();
            foreach (var i in list)
                points.Add(i);
            CommitEnd();
		}

         public void points_Add(Tabula.PMCore.Vector2f o)
        {
	        CommitBegin();
            if (_Model.points == null)
                _Model.points = new List<Tabula.PMCore.Vector2f>();

            points.Add(o);
	        CommitEnd();
        }

        public void points_RemoveAt(int index)
        {
            if (_Model.points == null)
                return;
            if (index >= _Model.points.Count)
                return;

	        CommitBegin();

            var o = _Model.points[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            points.RemoveAt(index);
	        CommitEnd();
        }

        public void points_Clear()
        {
	        if (_Model.points == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.points.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.points[i]);

            _Model.points.Clear();
            points.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class StructureParams0Wrapper : WrapperBase<StructureParams0>
    {
        public static implicit operator StructureParams0Wrapper(StructureParams0 m)
        {
            var w = new StructureParams0Wrapper(m);
            return w;
        }

        public StructureParams0Wrapper() : base(null)
        {}

        public StructureParams0Wrapper(StructureParams0 m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Boolean texture_enable
        {
            get => _Model.texture_enable;
                   
            set
            {
                _Model.texture_enable = value;
                _Model.CommitUpdate(nameof(texture_enable));
            }
        }


        public System.String texture
        {
            get => _Model.texture;
                   
            set
            {
                _Model.texture = value;
                _Model.CommitUpdate(nameof(texture));
            }
        }


        public System.Boolean texture_mask_enable
        {
            get => _Model.texture_mask_enable;
                   
            set
            {
                _Model.texture_mask_enable = value;
                _Model.CommitUpdate(nameof(texture_mask_enable));
            }
        }


        public System.String texture_mask
        {
            get => _Model.texture_mask;
                   
            set
            {
                _Model.texture_mask = value;
                _Model.CommitUpdate(nameof(texture_mask));
            }
        }


        public System.Boolean effect_enable
        {
            get => _Model.effect_enable;
                   
            set
            {
                _Model.effect_enable = value;
                _Model.CommitUpdate(nameof(effect_enable));
            }
        }


        public System.Int32 effect
        {
            get => _Model.effect;
                   
            set
            {
                _Model.effect = value;
                _Model.CommitUpdate(nameof(effect));
            }
        }


        public System.Single effect_scale
        {
            get => _Model.effect_scale;
                   
            set
            {
                _Model.effect_scale = value;
                _Model.CommitUpdate(nameof(effect_scale));
            }
        }


        public System.Boolean effect2_enable
        {
            get => _Model.effect2_enable;
                   
            set
            {
                _Model.effect2_enable = value;
                _Model.CommitUpdate(nameof(effect2_enable));
            }
        }


        public System.Int32 effect2
        {
            get => _Model.effect2;
                   
            set
            {
                _Model.effect2 = value;
                _Model.CommitUpdate(nameof(effect2));
            }
        }


        public System.Single effect2_scale
        {
            get => _Model.effect2_scale;
                   
            set
            {
                _Model.effect2_scale = value;
                _Model.CommitUpdate(nameof(effect2_scale));
            }
        }


        public System.Boolean contour_enable
        {
            get => _Model.contour_enable;
                   
            set
            {
                _Model.contour_enable = value;
                _Model.CommitUpdate(nameof(contour_enable));
            }
        }


        public System.Int32 contour
        {
            get => _Model.contour;
                   
            set
            {
                _Model.contour = value;
                _Model.CommitUpdate(nameof(contour));
            }
        }


        public System.Boolean color_animation_enable
        {
            get => _Model.color_animation_enable;
                   
            set
            {
                _Model.color_animation_enable = value;
                _Model.CommitUpdate(nameof(color_animation_enable));
            }
        }


        public System.Int32 color_animation
        {
            get => _Model.color_animation;
                   
            set
            {
                _Model.color_animation = value;
                _Model.CommitUpdate(nameof(color_animation));
            }
        }


        public System.Single color_animation_speed
        {
            get => _Model.color_animation_speed;
                   
            set
            {
                _Model.color_animation_speed = value;
                _Model.CommitUpdate(nameof(color_animation_speed));
            }
        }


        public System.Boolean color_enable
        {
            get => _Model.color_enable;
                   
            set
            {
                _Model.color_enable = value;
                _Model.CommitUpdate(nameof(color_enable));
            }
        }


        public System.String color
        {
            get => _Model.color;
                   
            set
            {
                _Model.color = value;
                _Model.CommitUpdate(nameof(color));
            }
        }


        public System.Boolean material_enable
        {
            get => _Model.material_enable;
                   
            set
            {
                _Model.material_enable = value;
                _Model.CommitUpdate(nameof(material_enable));
            }
        }


        public System.Int32 material
        {
            get => _Model.material;
                   
            set
            {
                _Model.material = value;
                _Model.CommitUpdate(nameof(material));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class StructureParams1Wrapper : WrapperBase<StructureParams1>
    {
        public static implicit operator StructureParams1Wrapper(StructureParams1 m)
        {
            var w = new StructureParams1Wrapper(m);
            return w;
        }

        public StructureParams1Wrapper() : base(null)
        {}

        public StructureParams1Wrapper(StructureParams1 m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case null:
                   return true;
            }

            return false;
        }

        public System.Single rotation
        {
            get => _Model.rotation;
                   
            set
            {
                _Model.rotation = value;
                _Model.CommitUpdate(nameof(rotation));
            }
        }


        public System.Boolean texture_mask_invert
        {
            get => _Model.texture_mask_invert;
                   
            set
            {
                _Model.texture_mask_invert = value;
                _Model.CommitUpdate(nameof(texture_mask_invert));
            }
        }


        public System.Boolean texture_screenspace
        {
            get => _Model.texture_screenspace;
                   
            set
            {
                _Model.texture_screenspace = value;
                _Model.CommitUpdate(nameof(texture_screenspace));
            }
        }


        public System.Single opacity
        {
            get => _Model.opacity;
                   
            set
            {
                _Model.opacity = value;
                _Model.CommitUpdate(nameof(opacity));
            }
        }

 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class StructureEffectWrapper : WrapperBase<StructureEffect>
    {
        public static implicit operator StructureEffectWrapper(StructureEffect m)
        {
            var w = new StructureEffectWrapper(m);
            return w;
        }

        public StructureEffectWrapper() : base(null)
        {}

        public StructureEffectWrapper(StructureEffect m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(fields): _fields=null; return true;
               case nameof(fields_shown): _fields_shown=null; return true;
               case nameof(props): _props=null; return true;
               case nameof(items): _items=null; return true;
               case nameof(presets): _presets=null; return true;
               case nameof(points): _points=null; return true;
               case null:
                   _fields=null;
                   _fields_shown=null;
                   _props=null;
                   _items=null;
                   _presets=null;
                   _points=null;
                   return true;
            }

            return false;
        }

        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String type
        {
            get => _Model.type;
                   
            set
            {
                _Model.type = value;
                _Model.CommitUpdate(nameof(type));
            }
        }


        public System.String container
        {
            get => _Model.container;
                   
            set
            {
                _Model.container = value;
                _Model.CommitUpdate(nameof(container));
            }
        }


        public System.String icon
        {
            get => _Model.icon;
                   
            set
            {
                _Model.icon = value;
                _Model.CommitUpdate(nameof(icon));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Boolean active
        {
            get => _Model.active;
                   
            set
            {
                _Model.active = value;
                _Model.CommitUpdate(nameof(active));
            }
        }


        public System.Boolean sync_fields
        {
            get => _Model.sync_fields;
                   
            set
            {
                _Model.sync_fields = value;
                _Model.CommitUpdate(nameof(sync_fields));
            }
        }


        public Tabula.PMCore.Entity.Flags flags
        {
            get => _Model.flags;
                   
            set
            {
                _Model.flags = value;
                _Model.CommitUpdate(nameof(flags));
            }
        }


        public System.String preset
        {
            get => _Model.preset;
                   
            set
            {
                _Model.preset = value;
                _Model.CommitUpdate(nameof(preset));
            }
        }


        public System.Single scale
        {
            get => _Model.scale;
                   
            set
            {
                _Model.scale = value;
                _Model.CommitUpdate(nameof(scale));
            }
        }


        public System.Single rotation
        {
            get => _Model.rotation;
                   
            set
            {
                _Model.rotation = value;
                _Model.CommitUpdate(nameof(rotation));
            }
        }


        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }

        private Tabula.PMCore.SizeFWrapper _size;
        public Tabula.PMCore.SizeFWrapper size
        {
            get
            {
                if (_size == null)
                    _size = new Tabula.PMCore.SizeFWrapper(_Model.size);

                return _size;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.size != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);

                    _size = null;
                    _Model.size = null;
                    _Model.CommitUpdate(nameof(size));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _size = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.size);
                    _Model.size = _size._Model;
                    _Model.size.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(size));
                }
            }
        }

        private Tabula.PMCore.Vector2fWrapper _offset;
        public Tabula.PMCore.Vector2fWrapper offset
        {
            get
            {
                if (_offset == null)
                    _offset = new Tabula.PMCore.Vector2fWrapper(_Model.offset);

                return _offset;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.offset != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);

                    _offset = null;
                    _Model.offset = null;
                    _Model.CommitUpdate(nameof(offset));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _offset = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.offset);
                    _Model.offset = _offset._Model;
                    _Model.offset.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(offset));
                }
            }
        }

        private WrapperList<System.Object> _fields;
        public WrapperList<System.Object> fields
        {
            get
            {
                if (_fields == null)
                    _fields = new  WrapperList<System.Object>(_Model.fields, _Model, "fields");

                return _fields;
            }
        }

        public void fields_Set(List<System.Object> list)
		{
            CommitBegin();
            fields.Clear();
            foreach (var i in list)
                fields.Add(i);
            CommitEnd();
		}


        public void fields_Add(System.Object o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields == null)
                _Model.fields = new List<System.Object>();

            fields.Add(o);
	        CommitEnd();
        }

        public void fields_RemoveAt(int index)
        {
            if (_Model.fields == null)
                return;
            if (index >= _Model.fields.Count)
                return;

	        CommitBegin();

            var o = _Model.fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_Clear()
        {
	        if (_Model.fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields.Count; i++)
                if (_Model.fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields[i]);

            _Model.fields = new List<System.Object>();
            fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _fields_shown;
        public WrapperList<System.String> fields_shown
        {
            get
            {
                if (_fields_shown == null)
                    _fields_shown = new  WrapperList<System.String>(_Model.fields_shown, _Model, "fields_shown");

                return _fields_shown;
            }
        }

        public void fields_shown_Set(List<System.String> list)
		{
            CommitBegin();
            fields_shown.Clear();
            foreach (var i in list)
                fields_shown.Add(i);
            CommitEnd();
		}


        public void fields_shown_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.fields_shown == null)
                _Model.fields_shown = new List<System.String>();

            fields_shown.Add(o);
	        CommitEnd();
        }

        public void fields_shown_RemoveAt(int index)
        {
            if (_Model.fields_shown == null)
                return;
            if (index >= _Model.fields_shown.Count)
                return;

	        CommitBegin();

            var o = _Model.fields_shown[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            fields_shown.RemoveAt(index);    
	        CommitEnd();
        }
        

        public void fields_shown_Clear()
        {
	        if (_Model.fields_shown == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.fields_shown.Count; i++)
                if (_Model.fields_shown[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.fields_shown[i]);

            _Model.fields_shown = new List<System.String>();
            fields_shown.Clear();

	        CommitEnd();
        }

        private WrapperDictionary<System.String,System.Object> _props;
        public WrapperDictionary<System.String,System.Object> props
        {
            get
            {
                if (_props == null)
                    _props = new  WrapperDictionary<System.String,System.Object>(_Model.props, _Model, "props");

                return _props;
            }
        }  

        public void props_Set(Dictionary<System.String,System.Object> dict)
        {
            CommitBegin();

            if (_Model.props == null)
                _Model.props = new Dictionary<System.String,System.Object>();
            else
            {
                foreach (var kvp in _Model.props)
                {
                    if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				        SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(kvp.Value);
                }

                _Model.props.Clear();
            }

            foreach (var kvp in dict)
            {
                if (kvp.Value is Tabula.SharedObjectMap.GuidObject)
				    SharedObjectMap.SharedObjectMap.AddAllGuidObjects(kvp.Value);

                props.Add(kvp.Key, kvp.Value);
            }

            CommitEnd();
        }

        // TODO: Add, Remove, Clear

        private WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> _items;
        public WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper> items
        {
            get
            {
                if (_items == null)                                    
                    _items = new WrapperGuidObjectList<Tabula.PMCore.Entity, Tabula.PMCore.EntityWrapper>(_Model.items, _Model, "items");                

                return _items;
            }
        }

        public void items_Set(List<Tabula.PMCore.Entity> list)
		{
            CommitBegin();
            items.Clear();
            foreach (var i in list)
                items.Add(i);
            CommitEnd();
		}

         public void items_Add(Tabula.PMCore.Entity o)
        {
	        CommitBegin();
            if (_Model.items == null)
                _Model.items = new List<Tabula.PMCore.Entity>();

            items.Add(o);
	        CommitEnd();
        }

        public void items_RemoveAt(int index)
        {
            if (_Model.items == null)
                return;
            if (index >= _Model.items.Count)
                return;

	        CommitBegin();

            var o = _Model.items[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            items.RemoveAt(index);
	        CommitEnd();
        }

        public void items_Clear()
        {
	        if (_Model.items == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.items.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.items[i]);

            _Model.items.Clear();
            items.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> _presets;
        public WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper> presets
        {
            get
            {
                if (_presets == null)                                    
                    _presets = new WrapperGuidObjectList<Tabula.PMCore.Preset, Tabula.PMCore.PresetWrapper>(_Model.presets, _Model, "presets");                

                return _presets;
            }
        }

        public void presets_Set(List<Tabula.PMCore.Preset> list)
		{
            CommitBegin();
            presets.Clear();
            foreach (var i in list)
                presets.Add(i);
            CommitEnd();
		}

         public void presets_Add(Tabula.PMCore.Preset o)
        {
	        CommitBegin();
            if (_Model.presets == null)
                _Model.presets = new List<Tabula.PMCore.Preset>();

            presets.Add(o);
	        CommitEnd();
        }

        public void presets_RemoveAt(int index)
        {
            if (_Model.presets == null)
                return;
            if (index >= _Model.presets.Count)
                return;

	        CommitBegin();

            var o = _Model.presets[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            presets.RemoveAt(index);
	        CommitEnd();
        }

        public void presets_Clear()
        {
	        if (_Model.presets == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.presets.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.presets[i]);

            _Model.presets.Clear();
            presets.Clear();

	        CommitEnd();
        }

        private WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> _points;
        public WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper> points
        {
            get
            {
                if (_points == null)                                    
                    _points = new WrapperGuidObjectList<Tabula.PMCore.Vector2f, Tabula.PMCore.Vector2fWrapper>(_Model.points, _Model, "points");                

                return _points;
            }
        }

        public void points_Set(List<Tabula.PMCore.Vector2f> list)
		{
            CommitBegin();
            points.Clear();
            foreach (var i in list)
                points.Add(i);
            CommitEnd();
		}

         public void points_Add(Tabula.PMCore.Vector2f o)
        {
	        CommitBegin();
            if (_Model.points == null)
                _Model.points = new List<Tabula.PMCore.Vector2f>();

            points.Add(o);
	        CommitEnd();
        }

        public void points_RemoveAt(int index)
        {
            if (_Model.points == null)
                return;
            if (index >= _Model.points.Count)
                return;

	        CommitBegin();

            var o = _Model.points[index];

            SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);
            points.RemoveAt(index);
	        CommitEnd();
        }

        public void points_Clear()
        {
	        if (_Model.points == null)
		        return;

	        CommitBegin();

	        for (int i=0; i< _Model.points.Count; i++)
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.points[i]);

            _Model.points.Clear();
            points.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ButtonFieldWrapper : WrapperBase<ButtonField>
    {
        public static implicit operator ButtonFieldWrapper(ButtonField m)
        {
            var w = new ButtonFieldWrapper(m);
            return w;
        }

        public ButtonFieldWrapper() : base(null)
        {}

        public ButtonFieldWrapper(ButtonField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String action
        {
            get => _Model.action;
                   
            set
            {
                _Model.action = value;
                _Model.CommitUpdate(nameof(action));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class StringFieldWrapper : WrapperBase<StringField>
    {
        public static implicit operator StringFieldWrapper(StringField m)
        {
            var w = new StringFieldWrapper(m);
            return w;
        }

        public StringFieldWrapper() : base(null)
        {}

        public StringFieldWrapper(StringField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class IntegerFieldWrapper : WrapperBase<IntegerField>
    {
        public static implicit operator IntegerFieldWrapper(IntegerField m)
        {
            var w = new IntegerFieldWrapper(m);
            return w;
        }

        public IntegerFieldWrapper() : base(null)
        {}

        public IntegerFieldWrapper(IntegerField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.Int32 value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.Int32 min
        {
            get => _Model.min;
                   
            set
            {
                _Model.min = value;
                _Model.CommitUpdate(nameof(min));
            }
        }


        public System.Int32 max
        {
            get => _Model.max;
                   
            set
            {
                _Model.max = value;
                _Model.CommitUpdate(nameof(max));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class FloatFieldWrapper : WrapperBase<FloatField>
    {
        public static implicit operator FloatFieldWrapper(FloatField m)
        {
            var w = new FloatFieldWrapper(m);
            return w;
        }

        public FloatFieldWrapper() : base(null)
        {}

        public FloatFieldWrapper(FloatField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.Single value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.Single min
        {
            get => _Model.min;
                   
            set
            {
                _Model.min = value;
                _Model.CommitUpdate(nameof(min));
            }
        }


        public System.Single max
        {
            get => _Model.max;
                   
            set
            {
                _Model.max = value;
                _Model.CommitUpdate(nameof(max));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class BoolFieldWrapper : WrapperBase<BoolField>
    {
        public static implicit operator BoolFieldWrapper(BoolField m)
        {
            var w = new BoolFieldWrapper(m);
            return w;
        }

        public BoolFieldWrapper() : base(null)
        {}

        public BoolFieldWrapper(BoolField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.Boolean value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class BoolCardsFieldWrapper : WrapperBase<BoolCardsField>
    {
        public static implicit operator BoolCardsFieldWrapper(BoolCardsField m)
        {
            var w = new BoolCardsFieldWrapper(m);
            return w;
        }

        public BoolCardsFieldWrapper() : base(null)
        {}

        public BoolCardsFieldWrapper(BoolCardsField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String true_label
        {
            get => _Model.true_label;
                   
            set
            {
                _Model.true_label = value;
                _Model.CommitUpdate(nameof(true_label));
            }
        }


        public System.String false_label
        {
            get => _Model.false_label;
                   
            set
            {
                _Model.false_label = value;
                _Model.CommitUpdate(nameof(false_label));
            }
        }


        public System.Double width
        {
            get => _Model.width;
                   
            set
            {
                _Model.width = value;
                _Model.CommitUpdate(nameof(width));
            }
        }


        public System.Double height
        {
            get => _Model.height;
                   
            set
            {
                _Model.height = value;
                _Model.CommitUpdate(nameof(height));
            }
        }


        public System.Boolean value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class SliderFloatFieldWrapper : WrapperBase<SliderFloatField>
    {
        public static implicit operator SliderFloatFieldWrapper(SliderFloatField m)
        {
            var w = new SliderFloatFieldWrapper(m);
            return w;
        }

        public SliderFloatFieldWrapper() : base(null)
        {}

        public SliderFloatFieldWrapper(SliderFloatField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.Single value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.Single min
        {
            get => _Model.min;
                   
            set
            {
                _Model.min = value;
                _Model.CommitUpdate(nameof(min));
            }
        }


        public System.Single max
        {
            get => _Model.max;
                   
            set
            {
                _Model.max = value;
                _Model.CommitUpdate(nameof(max));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class SliderFloatPercentageFieldWrapper : WrapperBase<SliderFloatPercentageField>
    {
        public static implicit operator SliderFloatPercentageFieldWrapper(SliderFloatPercentageField m)
        {
            var w = new SliderFloatPercentageFieldWrapper(m);
            return w;
        }

        public SliderFloatPercentageFieldWrapper() : base(null)
        {}

        public SliderFloatPercentageFieldWrapper(SliderFloatPercentageField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.Single percentage_factor
        {
            get => _Model.percentage_factor;
                   
            set
            {
                _Model.percentage_factor = value;
                _Model.CommitUpdate(nameof(percentage_factor));
            }
        }


        public System.Single value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.Single min
        {
            get => _Model.min;
                   
            set
            {
                _Model.min = value;
                _Model.CommitUpdate(nameof(min));
            }
        }


        public System.Single max
        {
            get => _Model.max;
                   
            set
            {
                _Model.max = value;
                _Model.CommitUpdate(nameof(max));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ChoiceFieldWrapper : WrapperBase<ChoiceField>
    {
        public static implicit operator ChoiceFieldWrapper(ChoiceField m)
        {
            var w = new ChoiceFieldWrapper(m);
            return w;
        }

        public ChoiceFieldWrapper() : base(null)
        {}

        public ChoiceFieldWrapper(ChoiceField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(choice_keys): _choice_keys=null; return true;
               case nameof(choice_values): _choice_values=null; return true;
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _choice_keys=null;
                   _choice_values=null;
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _choice_keys;
        public WrapperList<System.String> choice_keys
        {
            get
            {
                if (_choice_keys == null)
                    _choice_keys = new  WrapperList<System.String>(_Model.choice_keys, _Model, "choice_keys");

                return _choice_keys;
            }
        }

        public void choice_keys_Set(List<System.String> list)
		{
            CommitBegin();
            choice_keys.Clear();
            foreach (var i in list)
                choice_keys.Add(i);
            CommitEnd();
		}


        public void choice_keys_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.choice_keys == null)
                _Model.choice_keys = new System.String[0];

            int len = _Model.choice_keys.Length;
			_Model.choice_keys = new System.String[len+1];

            choice_keys.Add(o);
	        CommitEnd();
        }

        public void choice_keys_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.choice_keys == null)
                return;
            if (index >= _Model.choice_keys.Length)
                return;

	        CommitBegin();

            var o = _Model.choice_keys[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            choice_keys.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void choice_keys_Clear()
        {
	        if (_Model.choice_keys == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.choice_keys.Length; i++)
                if (_Model.choice_keys[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.choice_keys[i]);

            _Model.choice_keys = new System.String[0];
            choice_keys.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _choice_values;
        public WrapperList<System.String> choice_values
        {
            get
            {
                if (_choice_values == null)
                    _choice_values = new  WrapperList<System.String>(_Model.choice_values, _Model, "choice_values");

                return _choice_values;
            }
        }

        public void choice_values_Set(List<System.String> list)
		{
            CommitBegin();
            choice_values.Clear();
            foreach (var i in list)
                choice_values.Add(i);
            CommitEnd();
		}


        public void choice_values_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.choice_values == null)
                _Model.choice_values = new System.String[0];

            int len = _Model.choice_values.Length;
			_Model.choice_values = new System.String[len+1];

            choice_values.Add(o);
	        CommitEnd();
        }

        public void choice_values_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.choice_values == null)
                return;
            if (index >= _Model.choice_values.Length)
                return;

	        CommitBegin();

            var o = _Model.choice_values[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            choice_values.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void choice_values_Clear()
        {
	        if (_Model.choice_values == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.choice_values.Length; i++)
                if (_Model.choice_values[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.choice_values[i]);

            _Model.choice_values = new System.String[0];
            choice_values.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ChoiceSelectorFieldWrapper : WrapperBase<ChoiceSelectorField>
    {
        public static implicit operator ChoiceSelectorFieldWrapper(ChoiceSelectorField m)
        {
            var w = new ChoiceSelectorFieldWrapper(m);
            return w;
        }

        public ChoiceSelectorFieldWrapper() : base(null)
        {}

        public ChoiceSelectorFieldWrapper(ChoiceSelectorField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(choice_keys): _choice_keys=null; return true;
               case nameof(choice_values): _choice_values=null; return true;
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _choice_keys=null;
                   _choice_values=null;
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _choice_keys;
        public WrapperList<System.String> choice_keys
        {
            get
            {
                if (_choice_keys == null)
                    _choice_keys = new  WrapperList<System.String>(_Model.choice_keys, _Model, "choice_keys");

                return _choice_keys;
            }
        }

        public void choice_keys_Set(List<System.String> list)
		{
            CommitBegin();
            choice_keys.Clear();
            foreach (var i in list)
                choice_keys.Add(i);
            CommitEnd();
		}


        public void choice_keys_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.choice_keys == null)
                _Model.choice_keys = new System.String[0];

            int len = _Model.choice_keys.Length;
			_Model.choice_keys = new System.String[len+1];

            choice_keys.Add(o);
	        CommitEnd();
        }

        public void choice_keys_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.choice_keys == null)
                return;
            if (index >= _Model.choice_keys.Length)
                return;

	        CommitBegin();

            var o = _Model.choice_keys[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            choice_keys.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void choice_keys_Clear()
        {
	        if (_Model.choice_keys == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.choice_keys.Length; i++)
                if (_Model.choice_keys[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.choice_keys[i]);

            _Model.choice_keys = new System.String[0];
            choice_keys.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _choice_values;
        public WrapperList<System.String> choice_values
        {
            get
            {
                if (_choice_values == null)
                    _choice_values = new  WrapperList<System.String>(_Model.choice_values, _Model, "choice_values");

                return _choice_values;
            }
        }

        public void choice_values_Set(List<System.String> list)
		{
            CommitBegin();
            choice_values.Clear();
            foreach (var i in list)
                choice_values.Add(i);
            CommitEnd();
		}


        public void choice_values_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.choice_values == null)
                _Model.choice_values = new System.String[0];

            int len = _Model.choice_values.Length;
			_Model.choice_values = new System.String[len+1];

            choice_values.Add(o);
	        CommitEnd();
        }

        public void choice_values_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.choice_values == null)
                return;
            if (index >= _Model.choice_values.Length)
                return;

	        CommitBegin();

            var o = _Model.choice_values[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            choice_values.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void choice_values_Clear()
        {
	        if (_Model.choice_values == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.choice_values.Length; i++)
                if (_Model.choice_values[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.choice_values[i]);

            _Model.choice_values = new System.String[0];
            choice_values.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class Position2DFieldWrapper : WrapperBase<Position2DField>
    {
        public static implicit operator Position2DFieldWrapper(Position2DField m)
        {
            var w = new Position2DFieldWrapper(m);
            return w;
        }

        public Position2DFieldWrapper() : base(null)
        {}

        public Position2DFieldWrapper(Position2DField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private Tabula.PMCore.Vector2fWrapper _position;
        public Tabula.PMCore.Vector2fWrapper position
        {
            get
            {
                if (_position == null)
                    _position = new Tabula.PMCore.Vector2fWrapper(_Model.position);

                return _position;
            }

            set
            {
                if (value == null)
                {
                    if (_Model.position != null)
                        Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);

                    _position = null;
                    _Model.position = null;
                    _Model.CommitUpdate(nameof(position));
                }
                else
                {
                    Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                    _position = value;
                    Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.position);
                    _Model.position = _position._Model;
                    _Model.position.__guid_parent = this._Model.__guid;
                    _Model.CommitUpdate(nameof(position));
                }
            }
        }

        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class FileFieldWrapper : WrapperBase<FileField>
    {
        public static implicit operator FileFieldWrapper(FileField m)
        {
            var w = new FileFieldWrapper(m);
            return w;
        }

        public FileFieldWrapper() : base(null)
        {}

        public FileFieldWrapper(FileField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(file_types): _file_types=null; return true;
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _file_types=null;
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String title
        {
            get => _Model.title;
                   
            set
            {
                _Model.title = value;
                _Model.CommitUpdate(nameof(title));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _file_types;
        public WrapperList<System.String> file_types
        {
            get
            {
                if (_file_types == null)
                    _file_types = new  WrapperList<System.String>(_Model.file_types, _Model, "file_types");

                return _file_types;
            }
        }

        public void file_types_Set(List<System.String> list)
		{
            CommitBegin();
            file_types.Clear();
            foreach (var i in list)
                file_types.Add(i);
            CommitEnd();
		}


        public void file_types_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.file_types == null)
                _Model.file_types = new System.String[0];

            int len = _Model.file_types.Length;
			_Model.file_types = new System.String[len+1];

            file_types.Add(o);
	        CommitEnd();
        }

        public void file_types_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.file_types == null)
                return;
            if (index >= _Model.file_types.Length)
                return;

	        CommitBegin();

            var o = _Model.file_types[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            file_types.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void file_types_Clear()
        {
	        if (_Model.file_types == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.file_types.Length; i++)
                if (_Model.file_types[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.file_types[i]);

            _Model.file_types = new System.String[0];
            file_types.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class ColorFieldWrapper : WrapperBase<ColorField>
    {
        public static implicit operator ColorFieldWrapper(ColorField m)
        {
            var w = new ColorFieldWrapper(m);
            return w;
        }

        public ColorFieldWrapper() : base(null)
        {}

        public ColorFieldWrapper(ColorField m) : base(m)
        {}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {
            switch(name)
            {
               case nameof(hides_fields): _hides_fields=null; return true;
               case nameof(hides_groups): _hides_groups=null; return true;
               case null:
                   _hides_fields=null;
                   _hides_groups=null;
                   return true;
            }

            return false;
        }

        public System.String value
        {
            get => _Model.value;
                   
            set
            {
                _Model.value = value;
                _Model.CommitUpdate(nameof(value));
            }
        }


        public System.String name
        {
            get => _Model.name;
                   
            set
            {
                _Model.name = value;
                _Model.CommitUpdate(nameof(name));
            }
        }


        public System.String name_desc
        {
            get => _Model.name_desc;
                   
            set
            {
                _Model.name_desc = value;
                _Model.CommitUpdate(nameof(name_desc));
            }
        }


        public System.Int32 field_index
        {
            get => _Model.field_index;
                   
            set
            {
                _Model.field_index = value;
                _Model.CommitUpdate(nameof(field_index));
            }
        }


        public System.String info
        {
            get => _Model.info;
                   
            set
            {
                _Model.info = value;
                _Model.CommitUpdate(nameof(info));
            }
        }


        public System.String tooltip
        {
            get => _Model.tooltip;
                   
            set
            {
                _Model.tooltip = value;
                _Model.CommitUpdate(nameof(tooltip));
            }
        }


        public System.String tag
        {
            get => _Model.tag;
                   
            set
            {
                _Model.tag = value;
                _Model.CommitUpdate(nameof(tag));
            }
        }


        public System.String group
        {
            get => _Model.group;
                   
            set
            {
                _Model.group = value;
                _Model.CommitUpdate(nameof(group));
            }
        }


        public System.String group_desc
        {
            get => _Model.group_desc;
                   
            set
            {
                _Model.group_desc = value;
                _Model.CommitUpdate(nameof(group_desc));
            }
        }


        public System.Int32 group_index
        {
            get => _Model.group_index;
                   
            set
            {
                _Model.group_index = value;
                _Model.CommitUpdate(nameof(group_index));
            }
        }


        public System.String license_feature
        {
            get => _Model.license_feature;
                   
            set
            {
                _Model.license_feature = value;
                _Model.CommitUpdate(nameof(license_feature));
            }
        }


        public System.Boolean show_editor
        {
            get => _Model.show_editor;
                   
            set
            {
                _Model.show_editor = value;
                _Model.CommitUpdate(nameof(show_editor));
            }
        }


        public System.Object hide_value
        {
            get => _Model.hide_value;
                   
            set
            {
                _Model.hide_value = value;
                _Model.CommitUpdate(nameof(hide_value));
            }
        }


        public System.Boolean IsExpanded
        {
            get => _Model.IsExpanded;
                   
            set
            {
                _Model.IsExpanded = value;
                _Model.CommitUpdate(nameof(IsExpanded));
            }
        }


        public System.Boolean IsSelected
        {
            get => _Model.IsSelected;
                   
            set
            {
                _Model.IsSelected = value;
                _Model.CommitUpdate(nameof(IsSelected));
            }
        }


        private WrapperList<System.String> _hides_fields;
        public WrapperList<System.String> hides_fields
        {
            get
            {
                if (_hides_fields == null)
                    _hides_fields = new  WrapperList<System.String>(_Model.hides_fields, _Model, "hides_fields");

                return _hides_fields;
            }
        }

        public void hides_fields_Set(List<System.String> list)
		{
            CommitBegin();
            hides_fields.Clear();
            foreach (var i in list)
                hides_fields.Add(i);
            CommitEnd();
		}


        public void hides_fields_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_fields == null)
                _Model.hides_fields = new System.String[0];

            int len = _Model.hides_fields.Length;
			_Model.hides_fields = new System.String[len+1];

            hides_fields.Add(o);
	        CommitEnd();
        }

        public void hides_fields_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_fields == null)
                return;
            if (index >= _Model.hides_fields.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_fields[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_fields.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_fields_Clear()
        {
	        if (_Model.hides_fields == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_fields.Length; i++)
                if (_Model.hides_fields[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_fields[i]);

            _Model.hides_fields = new System.String[0];
            hides_fields.Clear();

	        CommitEnd();
        }

        private WrapperList<System.String> _hides_groups;
        public WrapperList<System.String> hides_groups
        {
            get
            {
                if (_hides_groups == null)
                    _hides_groups = new  WrapperList<System.String>(_Model.hides_groups, _Model, "hides_groups");

                return _hides_groups;
            }
        }

        public void hides_groups_Set(List<System.String> list)
		{
            CommitBegin();
            hides_groups.Clear();
            foreach (var i in list)
                hides_groups.Add(i);
            CommitEnd();
		}


        public void hides_groups_Add(System.String o)
        {
            // TODO: untested

	        CommitBegin();
            if (_Model.hides_groups == null)
                _Model.hides_groups = new System.String[0];

            int len = _Model.hides_groups.Length;
			_Model.hides_groups = new System.String[len+1];

            hides_groups.Add(o);
	        CommitEnd();
        }

        public void hides_groups_RemoveAt(int index)
        {
            // TODO: unimplemented for arrays
            throw new NotImplementedException();

            if (_Model.hides_groups == null)
                return;
            if (index >= _Model.hides_groups.Length)
                return;

	        CommitBegin();

            var o = _Model.hides_groups[index];
            if (o is Tabula.SharedObjectMap.GuidObject) 
                SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(o);

            hides_groups.RemoveAt(index);    // if array it will throw
	        CommitEnd();
        }
        

        public void hides_groups_Clear()
        {
	        if (_Model.hides_groups == null)
		        return;

	        CommitBegin();

            for (int i=0; i< _Model.hides_groups.Length; i++)
                if (_Model.hides_groups[i] is Tabula.SharedObjectMap.GuidObject)
                    SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.hides_groups[i]);

            _Model.hides_groups = new System.String[0];
            hides_groups.Clear();

	        CommitEnd();
        }
 }


}