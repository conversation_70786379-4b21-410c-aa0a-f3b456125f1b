using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Rendering.SceneGraph;
using Avalonia.Skia;
using Avalonia.Threading;
using ReactiveUI;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Tabula;
using Tabula.PMCore;
using Tabula.SKRenderGraph;
using Tabula.AvaloniaUtils;
using System.Reflection;
using System.IO;
using Avalonia.VisualTree;
using Avalonia.Media.Imaging;

namespace Tabula.PWGClient
{
    // ScreenEditor is the low-level Skia component for Warp/Region/Calibration
    // The UI part is in the ScreenEditorPanel

    public partial class ScreenEditor : SKSceneUserControl
    {
        public static ScreenEditor Instance;

		private SKImage CalibrationImage;  // the original unwarped image for the CalibrateImageMarkers phase       

        //public List<CandidateImageMarker> CandidateImageMarkers = new List<CandidateImageMarker>();

		public override bool IsSceneVisible
        {
            get
            {
				var tab = this.FindAncestor<TabItem>();

				if (tab != null)
                    return tab.IsSelected;
                else
                    return false;
            }
        }

        public ScreenEditor()
        {
            Instance = this;
        }


		protected override void OnInitialized()
        {
			Dispatcher.UIThread.InvokeAsync(async () => await ConfigureScene());

			base.OnInitialized();
        } 

        public override Task ConfigureScene()
        {
            Scene.BeforeDraw += BeforeDraw;
            Scene.AfterDraw += AfterDraw;

			// In CalibrateImageMarkers if we select an ImageMarker we should show the inspector options to delete them
			Scene.OnObjectSelected += (sel) =>
			{
                if (MainWindowUI.Instance.ProjectionSetupMode != MainWindowUI.ProjectionSetupModes.CalibrateImageMarkers)
                    return;

                if (sel == null)
                    return;

                var sel_pmview = PMViewExt.GetPMViewFromVisual(sel);

                if (sel_pmview != null && sel_pmview is PMImageMarkerView)
                {
					Dispatcher.UIThread.Invoke(() => MainWindowUI.Instance.MainSelectedItem = sel_pmview);
				}

			};

            // Create a max number of candidate image markers, just sprites
            /*
            for (int i = 0; i < 10; i++)
            {
                var m = CandidateImageMarker.Create(Scene);
				Scene.Add(m);
				CandidateImageMarkers.Add(m);
            }
            */

			/*
            string resourceID = "Tabula.PMCore.Images.embedded.test.jpg";
            Assembly assembly = GetType().GetTypeInfo().Assembly;

            Stream stream = assembly.GetManifestResourceStream(resourceID);
            var bmp  = SKBitmap.Decode(stream);

            // TEST: WarpQuad
            Scene.Add(new SKWarpQuad(Scene)
			{
                Bitmap = bmp
			});
            */



			return Task.CompletedTask;
        }

		public override void OnCanvasAvailable()
		{
			// Fit
            Dispatcher.UIThread.InvokeAsync(() => MainWindowUI.Instance.Fit());

			base.OnCanvasAvailable();
		}

        string calibration_image_path = null;    // null: no change, empty_string: clear, otherwise load the path
        public void LoadCalibrationImage(string image_path)
        {
            calibration_image_path = image_path;
        }


        void BeforeDraw()
        {
			if (MainWindowUI.Instance.View == null || !App.IsConnected)
				return;

			if (MainWindowUI.Instance.ProjectionSetupMode == MainWindowUI.ProjectionSetupModes.CalibrateImageMarkers)
			{
                // If provided load the calibration image
                if (calibration_image_path == string.Empty)
                {
                    // clear, so it won't draw
                    CalibrationImage?.Dispose();
                    CalibrationImage = null;
                }
                else if (!string.IsNullOrEmpty(calibration_image_path))
                {
					CalibrationImage?.Dispose();
					CalibrationImage = SKImage.FromEncodedData(File.ReadAllBytes(calibration_image_path));
                    calibration_image_path = null;
				}

                if (calibration_image_path == null && CalibrationImage != null)
                {
					Scene.Canvas.DrawImage(CalibrationImage,
					         new SKRect(0, 0, CalibrationImage.Width, CalibrationImage.Height),
							 new SKRect(0, 0, CalibrationImage.Width, CalibrationImage.Height), 
                             new SKPaint() { IsAntialias = true, FilterQuality = SKFilterQuality.High });
				}

                
			}
		}

		void AfterDraw()
        {
            if (MainWindowUI.Instance.View == null || !App.IsConnected)
                return;

            var paint = new SKPaint()             
                { 
                    Color = new SKColor(255, 255, 255), 
                    IsStroke= true,
                    StrokeWidth = 2,
                    IsAntialias = true             
                };

            if (MainWindowUI.Instance.ProjectionSetupMode == MainWindowUI.ProjectionSetupModes.CalibrateScreenMarkers)
            {
				// cropped size

				// Draw the real screen size, depending on the cropping
				// TODO: this is now only for surface #0
				var screen_rect = App.Client.Model.GetVisualScreenSize(0);

                // screen bounds
                var screen_skrect = new SKRect(screen_rect.x, screen_rect.y, screen_rect.x + screen_rect.width, screen_rect.y + screen_rect.height);
				Scene.Canvas.DrawRect(screen_skrect, paint);

				// marker bounds
				var marker_rect = App.Client.Model.GetVisualMarkerSafeArea(0);
				var marker_skrect = new SKRect(marker_rect.x, marker_rect.y, marker_rect.x + marker_rect.width, marker_rect.y + marker_rect.height);

				paint.PathEffect = SKPathEffect.CreateDash(new float[] { 10,10},0);
                paint.Color = SKColors.Yellow;

				Scene.Canvas.DrawRect(marker_skrect, paint);
			}
            else if (MainWindowUI.Instance.ProjectionSetupMode == MainWindowUI.ProjectionSetupModes.CalibrateImageMarkers)
            {
                // TODO:
            }
			else 
            {
				// native screen/projection size
				Scene.Canvas.DrawRect(new SKRect(0, 0, App.Client.Model.Screen.size.width, App.Client.Model.Screen.size.height), paint);
			}

			// output the real region size
            if (MainWindowUI.Instance.ProjectionSetupMode == MainWindowUI.ProjectionSetupModes.Region)
			    try
			    {
				    var visible_screen = App.Client.Model.GetVisualScreenSize(0);

				    string region_text = $"{visible_screen.width} x {visible_screen.height}";

				    SKPoint rcenter = new SKPoint(App.Client.Model.Screen.size.width / 2f, App.Client.Model.Screen.size.height / 2f);

				    Scene.Canvas.DrawText(region_text, rcenter.X, rcenter.Y, new SKPaint()
				    {
					    Color = SKColors.White,
					    TextSize = 18f / Scene.CanvasScale,
					    //FakeBoldText = true,
					    TextAlign = SKTextAlign.Center,
					    IsAntialias = true,
					    FilterQuality = SKFilterQuality.High
				    });
			    }
			    catch { }
		}
      
    }


    /*
	public class CandidateImageMarker : SKSprite
	{
		public CandidateImageMarker(SKScene scene, SKRect rect) : base(scene, rect)
		{}

		public new static CandidateImageMarker CreateFromEmbeddedResource(SKScene scene, SKRect rect, string resource_id)
		{
			var o = new CandidateImageMarker(scene, rect);

			Assembly assembly = Assembly.GetExecutingAssembly();

			Stream stream = assembly.GetManifestResourceStream(resource_id);
			o.Bitmap = SKImage.FromEncodedData(stream);

			return o;
		}


		public static CandidateImageMarker Create(SKScene scene)
        {
			var o = CreateFromEmbeddedResource(scene, new SKRect(0, 0, 100, 100), $"Tabula.PMCore.Images.embedded.calibration_point_1.png");

            return (CandidateImageMarker) o;
		}
	}
    */
}
