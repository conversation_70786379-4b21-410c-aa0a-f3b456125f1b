//TABULA_GUID:{4FF5E913-24D7-4BF7-9460-61BFC4A6057E}
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using NobleConnect;
using NobleConnect.Ice;
using NobleConnectLib;
using NobleConnectLib.MatchUpDotNet;
using Tabula.Log;

// General P2P Class

namespace Tabula.P2P
{
	public static class P2P
	{
		private const string NC_GameId = "****************************************************************************************************************************************************";
		private const string NC_MatchName = "S_ARGAME";

		private static NobleConnectClient? client;
		private static NobleConnectServer? server;

		public static int	UpdateTimerRate = 100;
		public static bool	ForceRelay = false;
		public static int	ServerMaxClients = 10000;


		#region Server

		private static Matchmaker hostMatchmakerInstance;
		public class ServerData
		{
			public enum State
			{
				None = 0,
				ConnectedAndPublished = 1,

				ServerFatalError = -1,
				ServerOfferFailed = -2,
				MatchUpLostConnection = -3,
				MatchUpCreationFailed = -4


			}

			public State	status;

			public string	ip;			// public ip
			public int		port;		// public port
			public Match	match;      // match data

			public string	join_code;
		}

		public static void StartAndPublishServer(int local_server_port, string join_code, string server_type, Action<ServerData> OnResult=null)
		{
			Task.Run(() => RunServer(
				gameId: NC_GameId, 
				forceRelay: ForceRelay, 
				logLevel: NobleConnect.Logger.Level.Info,
				updateTimerRateMs: UpdateTimerRate, 
				localServerPort: local_server_port,
				OnServerPrepared: (public_ip, public_port) =>
				{
						// Starting MatchUp
						// TODO: add server type
					_ = Task.Run(() => RunMatchUpHost(
						public_ip,
						(int) public_port,
						NC_MatchName,
						join_code,
						OnMatchResult: (success, match) => 
						{
							if (success)
							{
								OnResult?.Invoke(new ServerData()
								{
									status = ServerData.State.ConnectedAndPublished,
									ip = public_ip,
									port = public_port,
									match = match,
									join_code = join_code
								});
							}
							else
							{
								OnResult?.Invoke(new ServerData()
								{
									status = ServerData.State.MatchUpCreationFailed,
									ip = null,
									port = -1,
									match = match
								});
							}

						},
						OnLostConnection: (ex) =>
						{
							OnResult?.Invoke(new ServerData()
							{
								status = ServerData.State.MatchUpLostConnection,
								ip = null,
								port = -1,
								match = null
							});
						}));
				}, 
				OnFatalError: (s) =>
					{
						OnResult?.Invoke(new ServerData()
						{
							status = ServerData.State.ServerFatalError,
							ip = null,
							port = -1,
							match = null
						});
					}, 
				OnOfferFailed: () =>
					{
						OnResult?.Invoke(new ServerData()
						{
							status = ServerData.State.ServerOfferFailed,
							ip = null,
							port = -1,
							match = null
						});
					}
				));
		}

		private static CancellationTokenSource? _server_tokensrc;

		private static async Task RunServer(string gameId, 
			bool forceRelay, 
			NobleConnect.Logger.Level logLevel, 
			int updateTimerRateMs, 
			int localServerPort,
			Action<string, ushort> OnServerPrepared,
			Action<string> OnFatalError,
			Action OnOfferFailed
			)
		{
#if DEBUG
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect account: {NobleConnectHelper.ParseGameID(gameId).username}");
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect gameId: {gameId}");
#endif

			_server_tokensrc = new CancellationTokenSource();

			var iceConfig = new IceConfig
			{
				iceServerAddress = RegionURL.FromRegion(GeographicRegion.AUTO),
				icePort = 3478,
				username = NobleConnectHelper.ParseGameID(gameId).username,
				password = NobleConnectHelper.ParseGameID(gameId).password,
				origin = NobleConnectHelper.ParseGameID(gameId).origin,
				useSimpleAddressGathering = true,
				onFatalError = (s) =>
					{
						Log.Logger.DefaultLog.WriteLine($"[OnFatalError] {s} - Ending session");
						OnFatalError?.Invoke(s);
						StopServer();
					},
				onOfferFailed = () =>
					{
						Log.Logger.DefaultLog.WriteLine("[OnOfferFailed] Initialize Peer FAILED - Ending session");
						OnOfferFailed?.Invoke();
						StopServer();
					},
				forceRelayOnly = forceRelay,
				enableIPv6 = false
			};

			server = new NobleConnectServer(localServerPort, iceConfig, logLevel, updateTimerRateMs);

			server.Initialize(_server_tokensrc.Token, (ip, port) =>
			{
				Log.Logger.DefaultLog.WriteLine($"[SERVER] Peer: Ready To Host");
				Log.Logger.DefaultLog.WriteLine($"[SERVER] Bridge EndPoint: {ip}, port: {port}");
				OnServerPrepared?.Invoke(ip, port);
			});
		}

		public static void StopServer()
		{
			_server_tokensrc?.Cancel();
			hostMatchmakerInstance?.DestroyMatch();
			server?.peer.DestroyBridges();
			server?.peer.CleanUpEverything();
		}

		public static void RunMatchUpHost(
			string hostAddress, 
			int hostPort, 
			string matchName, 
			string join_code,
			Action<bool, Match> OnMatchResult, 
			Action<Exception> OnLostConnection)
		{
#if DEBUG
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Start hosting a match..");
#endif

			hostMatchmakerInstance = new Matchmaker();
			hostMatchmakerInstance.onLostConnectionToMatchmakingServer += delegate (Exception e)
			{
				Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Lost Connection To Matchmaker Server: {e}");
				OnLostConnection?.Invoke(e);
			};

			// TODO:
			var matchData = new Dictionary<string, MatchData>() {
			{ "Match name", matchName },
			{ "Host Address", hostAddress },
			{ "Host Port", hostPort },
			{ "DeviceID", join_code}	// TODO
		};

			// Create the Match with the associated MatchData
			hostMatchmakerInstance.CreateMatch(ServerMaxClients, matchData, 
				(bool success, Match match) =>
				{
					if (success)
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Match created with data:" +
										  $"\n- ID: {match.id.ToString()}" +
										  $"\n- Name: {match.matchData["Match name"]}" +
										  $"\n- Host Unique ID: {match.matchData["DeviceID"]}");

						OnMatchResult?.Invoke(true, match);
					}
					else
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker host] Match creation failed");

						OnMatchResult?.Invoke(false, match);
					}

				});
		}

		#endregion

		#region Client

		public class ClientData
		{
			public enum State
			{
				None = 0,
				Connected = 1,

				ClientFatalError = -1,
				ClientOfferFailed = -2,

				MatchUpLostConnection = -3,
				MatchUpCreationFailed = -4,

				// extra
				Timeout = -5
			}

			public State status;

			public string	ip;           // public ip
			public int		port;        // public port
			public ConnectionType	connection_type;
			public Match	match;     // match data
			public Matchmaker matchMakerInstance;
		}

		// Will keep track of current connection
		public static ClientData CurrentConnection { get; private set; }

		public static void StartClient(string join_code, Action<ClientData> OnResult=null)
		{
			Matchmaker clientMatchmakerInstance = new Matchmaker();
			clientMatchmakerInstance.onLostConnectionToMatchmakingServer += delegate (Exception e)
			{
				Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Lost Connection To Matchmaker Server: {e}");
			};

			// Save Game ID in PlayerPrefs
			//PlayerPrefs.SetString("lastUsedHostDeviceUniqueID", clientGameIDInputField.text);

			JoinMatchWithDeviceUniqueID(
				clientMatchmakerInstance,
				join_code,
				OnMatchJoined: (match) =>
				{
					if (match == null)
					{
						// no match found, return a null result
						OnResult?.Invoke(null);
						return;
					}

					Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Client has joined match with ID: {match.id}");

					_ = Task.Run(() => RunClient(NC_GameId,
						forceRelay: ForceRelay,
						match.matchData["Host Address"],
						match.matchData["Host Port"],
						NobleConnect.Logger.Level.Info,
						(string ip, int port, ConnectionType connection_type) =>
							{
								Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] Client connection succesful: Bridge EndPoint: {ip}, port: {port}");

								CurrentConnection = new ClientData()
								{
									status = ClientData.State.Connected,
									ip = ip,
									port = port,
									connection_type = connection_type,
									match = match,
									matchMakerInstance = clientMatchmakerInstance
								};

								OnResult?.Invoke(CurrentConnection);
							},
						OnFatalError: (s) =>
							{
								CurrentConnection = new ClientData()
								{
									status = ClientData.State.ClientFatalError,
									ip = null,
									port = -1,
									match = null,
									matchMakerInstance = null
								};

								OnResult?.Invoke(CurrentConnection);
							},
						OnOfferFailed: () =>
						{
							CurrentConnection = new ClientData()
							{
								status = ClientData.State.ClientFatalError,
								ip = null,
								port = -1,
								match = null,
								matchMakerInstance = null
							};

							OnResult?.Invoke(CurrentConnection);
						}));
				});
		}

		public static void JoinMatchWithDeviceUniqueID(Matchmaker clientMatchmakerInstance, string join_code, Action<Match> OnMatchJoined)
		{
			var filters = new List<MatchFilter>()
			{
				new MatchFilter("DeviceID", join_code)
			};

			clientMatchmakerInstance.GetMatchList(delegate (bool success, Match[] matches)
			{
				if (success)
				{
					if (matches.Length > 0)
					{
						var lastMatch = matches.OrderByDescending(x => x.id).First();
						clientMatchmakerInstance.JoinMatch(lastMatch,
							(b, match) =>
							{
								Log.Logger.DefaultLog.WriteLine(
									$"[Matchmaker client] Successfully joined match with ID: {match.id.ToString()}");
								OnMatchJoined.Invoke(match);
							});
					}
					else
					{
						Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] No match found!");
						OnMatchJoined?.Invoke(null);
					}
				}
				else
				{
					Log.Logger.DefaultLog.WriteLine($"[Matchmaker client] cannot retrieve current match list");
					OnMatchJoined?.Invoke(null);
				}
			}, 0, 100, filters);
		}

		public static async Task RunClient(
			string gameId, 
			bool forceRelay, 
			string ip, 
			int port, 
			NobleConnect.Logger.Level logLevel,
			Action<string, int, ConnectionType> OnReadyToConnect,
			Action<string> OnFatalError,
			Action OnOfferFailed)
		{

#if DEBUG
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect account: {NobleConnectHelper.ParseGameID(gameId).username}");
			Log.Logger.DefaultLog.WriteLine($"[SANDBOX] Using NobleConnect gameId: {gameId}");
#endif

			var _iceConfig = new IceConfig
			{
				iceServerAddress = RegionURL.FromRegion(GeographicRegion.AUTO),
				icePort = 3478,
				username = NobleConnectHelper.ParseGameID(gameId).username,
				password = NobleConnectHelper.ParseGameID(gameId).password,
				origin = NobleConnectHelper.ParseGameID(gameId).origin,
				useSimpleAddressGathering = true,
				onFatalError = (s) =>
					{
						Log.Logger.DefaultLog.WriteLine($"[OnFatalError] {s} - Ending session");
						OnFatalError?.Invoke(s);
						StopClient();
					},
				onOfferFailed = () =>
					{
						Log.Logger.DefaultLog.WriteLine("[OnOfferFailed] Initialize Peer FAILED - Ending session");
						OnOfferFailed?.Invoke();
						StopClient();
					},
				forceRelayOnly = forceRelay,
				enableIPv6 = false
			};

			client = new NobleConnectClient(_iceConfig, logLevel);

			// Address and Port of the server
			IPEndPoint serverEndPoint = new IPEndPoint(IPAddress.Parse(ip), port);

			client.Initialize(serverEndPoint, endPoint =>
			{
				Log.Logger.DefaultLog.WriteLine($"[CLIENT] Peer: Ready To Connect (connection type {client.peer.latestConnectionType})" +
						  $" on Local EndPoint: {endPoint.Address.ToString()}, port: {endPoint.Port}");

				OnReadyToConnect.Invoke(endPoint.Address.ToString(), endPoint.Port, client.peer.latestConnectionType);
			});
		}

		public static void StopClient(Matchmaker matchmakerInstance = null)
		{
			if (matchmakerInstance != null)
			{
				matchmakerInstance.LeaveMatch();
			}
			client?.peer.DestroyBridges();
			client?.peer.CleanUpEverything();
		}

		#endregion
	}
}