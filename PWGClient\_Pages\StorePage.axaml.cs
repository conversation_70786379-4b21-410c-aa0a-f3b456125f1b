using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using DialogHostAvalonia;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class StorePage : UserControl
	{
		public StorePage()
		{
			InitializeComponent();
		}

		async void bt_download_Click(object sender, RoutedEventArgs args)
		{
			if (!await MainView.Instance.AskLicenseActivation())
				return;

			StorePackage store_package = (sender as Control).DataContext as StorePackage;

			if (store_package == null)
			{
				SARGAME.App.logError("StorePage.bt_download_Click() package is null");
				return;
			}

			// check features
			// NOTE: in store.json the features are high level, so let's check for partial match of either "pack" or "pack/"
			/*
			if (string.IsNullOrEmpty(package.license_feature) || !App.Local.CheckLicenseFeature(package.license_feature, partial_match: true))
			{
				SARGAME.App.logError($"StorePage.bt_download_Click() license has not package feature: {package.license_feature}");
				return; 
			}*/

			await DownloadAndInstallPackage(store_package, show_ui: true);
		}

		async void bt_remove_Click(object sender, RoutedEventArgs args)
		{
			StorePackage package = (sender as Control).DataContext as StorePackage;

			try
			{
				if (!package.IsInstalled)
					return;

				var choice = await ProgressDialog.ShowChoicesAsync("Remove the Package?", new string[] { "Yes", "No" });

				if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
					return;

				if (OperatingSystem.IsMacOS())
				{
					// Let's open finder in the /Users/<USER>
					System.Diagnostics.Process p = System.Diagnostics.Process.Start("open", $"\"/Users/<USER>"");

					await ProgressDialog.ShowMessageAsync("Please delete the package manually from the /Users/<USER>");

					return;
				}

			
				var uninstall_ret = await package.Uninstall();

				if (uninstall_ret >= 0)
					SARGAME.App.log($"Ununstalled package {package.id} ret={uninstall_ret}");
				else
					SARGAME.App.logError($"ERROR: Ununstalled package {package.id} ret={uninstall_ret}");

				if (OperatingSystem.IsWindows())
					await ProgressDialog.ShowMessageAsync("Package uninstalled");
				else if (OperatingSystem.IsMacOS())
					await ProgressDialog.ShowMessageAsync("Please restart the Editor");
			}
			catch (Exception ex) 			
			{
				await ProgressDialog.ShowMessageAsync("Error uninstalling package");			
			}

			// Refresh store
			await SARGAME.Instance.GetStore();
		}
	
	
		public static async Task<SARGAME.StoreOperationResult> DownloadAndInstallPackage(StorePackage store_package, bool show_ui)
		{
			if (store_package.IsInstalled && !store_package.IsUpdateAvailable)
				return SARGAME.StoreOperationResult.AlreadyInstalled;

			var cts = new CancellationTokenSource();

			ProgressDialog msgbox = null;

			if (show_ui)
			{
				msgbox = ProgressDialog.ShowMessage(
					message: $"Downloading package...",
					progress: true,
					cancel_button_text: "Cancel",
					cancellation_token: cts.Token,
					on_cancel_button_click: () => cts.Cancel()
					);
			}

			(SARGAME.StoreOperationResult result, string filename) download_result;

			if (OperatingSystem.IsWindows())
			{
				// Sometimes this also fails to set the progress..
				/*
				Progress<double> progress = new Progress<double>(percent =>
					Dispatcher.UIThread.Invoke(() => msgbox.SetProgress(percent)));
				*/

				download_result = await SARGAME.Instance.DownloadPackageFromStore(store_package, editor_version: App.Instance.GetVersion(), progress: null /*show_ui ? progress : null*/, token: cts.Token);
			}
			else
			{
				// FIX: on Mac this seems to crash at the end, and generally not too reliable, let's make it undeterminate
				download_result = await SARGAME.Instance.DownloadPackageFromStore(store_package, editor_version: App.Instance.GetVersion(), progress: null, token: cts.Token);
			}

			

			if (show_ui)
				msgbox.Close();

			switch (download_result.result)
			{
				case SARGAME.StoreOperationResult.NoUpdates:
					if (show_ui)
						await ProgressDialog.ShowMessageAsync("No updates available");
					return download_result.result;

				case SARGAME.StoreOperationResult.DownloadError:
					if (show_ui)
						await ProgressDialog.ShowMessageAsync("Error in download");
					return download_result.result;

				case SARGAME.StoreOperationResult.DownloadCanceled:
					return download_result.result;
			}

			if (show_ui && OperatingSystem.IsWindows())
				msgbox = ProgressDialog.ShowMessage((store_package.IsUpdateAvailable ? "Updating" : "Installing") + " package...", progress: true);

			try
			{
				var install_ret = await SARGAME.Instance.InstallPackage(download_result.filename);
				if (install_ret >= 0)
					SARGAME.App.log($"Installed package {store_package.id} IsUpdate={store_package.IsUpdateAvailable} ret={install_ret}");
				else
					SARGAME.App.logError($"ERROR: Installed package {store_package.id}  IsUpdate={store_package.IsUpdateAvailable} ret={install_ret}");
			}
			catch (Exception ex)
			{
				SARGAME.App.logException("bt_download_Click, InstallPackage()", ex);
				if (show_ui)
					await ProgressDialog.ShowMessageAsync("An error occurred installing the package", "Continue");
			}

			if (OperatingSystem.IsMacOS())
			{
				msgbox = null;
				SARGAME.App.log("download_1.1");
				// await ProgressDialog.ShowMessageAsync("Please install the package manually from the opened folder, then continue", "Continue");
				if (show_ui)
					await ProgressDialog.ShowMessageAsync("Please install the package, then continue", "Continue");
				SARGAME.App.log("download_1.2");
			}

			SARGAME.App.log("download_2");

			// Refresh packages
			//SARGAME.Instance.LoadInstalledPackagesAndModules();

			SARGAME.App.log("download_3");

			if (show_ui && OperatingSystem.IsWindows())
				msgbox?.Close();

			SARGAME.App.log("download_4");

			// Refresh store
			await SARGAME.Instance.GetStore();

			SARGAME.App.log("download_5");

			// TODO: On mac check?
			if (show_ui)
			{
				if (OperatingSystem.IsWindows())
					await ProgressDialog.ShowMessageAsync("Package installed successfully!");
				else if (OperatingSystem.IsMacOS())
					await ProgressDialog.ShowMessageAsync("Please restart the Editor");
			}

			SARGAME.App.log("download_6");

			return SARGAME.StoreOperationResult.DownloadSuccesful;
		}
	}

	

}
