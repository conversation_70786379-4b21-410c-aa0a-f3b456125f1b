d:
cd "D:\OneDrive\TABULA_Builds\S-ARGAME"

REM Create the installer (in D:\OneDrive\_DEPLOY\s-argame\...)

REM Creates the .ini file with the version in the .txt
set /p version=<"D:\OneDrive\TABULA_Builds\S-ARGAME\starterpack\app\version.txt"
(echo [Version]) > "D:\OneDrive\TABULA_Builds\S-ARGAME\starterpack\app\version.ini"
(echo Number=%version%) >> "D:\OneDrive\TABULA_Builds\S-ARGAME\starterpack\app\version.ini"

REM Remove debug folder
rmdir /S /Q "D:\OneDrive\TABULA_Builds\S-ARGAME\starterpack\app\S-ARGAME_StarterPack_BurstDebugInformation_DoNotShip"

REM Compiles

iscc.exe starterpack.iss

REM outputs S-ARGAME_StarterPack.exe we need to rename
REM set /p version=<"D:\OneDrive\TABULA_Builds\S-ARGAME\starterpack\app\version.txt"
REM del "S-ARGAME_StarterPack-%version%.exe"
REM ren "D:\OneDrive\_DEPLOY\s-argame\win_x64\packages\starterpack\S-ARGAME_StarterPack.exe" "S-ARGAME_StarterPack-%version%.exe"

REM Copy the changelogs 
REM xcopy installer\changelogs "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor\changelogs\" /Y

REM  Generate NetSparkle appcast in _DEPLOY

netsparkle.exe -a "D:\OneDrive\_DEPLOY\s-argame\win_x64\packages\starterpack" -e exe -b "D:\OneDrive\_DEPLOY\s-argame\win_x64\packages\starterpack" -n "S-ARGAME Starter Pack" -u "https://sargame-cdn.tabulatouch.com/win_x64/packages/starterpack" -p "D:\OneDrive\_DEPLOY\s-argame\win_x64\packages\starterpack\changelogs" -l "https://sargame-cdn.tabulatouch.com/win_x64/packages\starterpack/changelogs" --file-extract-version --overwrite-old-items --human-readable





