<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:local="clr-namespace:Tabula.PWGClient"
        xmlns:pmcore="clr-namespace:Tabula.PMCore"
		xmlns:dialogHost="clr-namespace:DialogHost;assembly=DialogHost.Avalonia"
        xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
        xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="Tabula.PWGClient.RoomCalibrationWindow"
        Title="RoomCalibrationWindow">
	<Grid Margin="10" RowDefinitions="*,Auto">
		
		<local:ImageCalibrationEditor x:Name="image_calibration" Grid.Row="0" Background="Transparent"/>

		<WrapPanel Grid.Row="1" HorizontalAlignment="Center">
			<Button Margin="10" Click="bt_calibrate">
				<TextBlock Text="Calibrate"/>
			</Button>
		</WrapPanel>
	</Grid>
</Window>
