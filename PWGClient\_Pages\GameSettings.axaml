<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:av="clr-namespace:TheArtOfDev.HtmlRenderer.Avalonia;assembly=Avalonia.HtmlRenderer"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:pmcore="clr-namespace:Tabula.PMCore;assembly=PWGClient_Core"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.GameSettings">

	<UserControl.DataTemplates>

		<!-- IntegerFieldView -->
		<DataTemplate DataType="{x:Type pmcore:IntegerFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock  Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" FontWeight="Bold"
								ToolTip.Tip="{Binding tooltip}"
								ToolTip.ShowDelay="0"
								VerticalAlignment="Center" 
								HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>					
				</StackPanel>
				<StackPanel  Grid.Column="1" Orientation="Horizontal">
					<TextBox Classes="property" VerticalAlignment="Center" 
							 HorizontalAlignment="Left" Width="100" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}">
						<Interaction.Behaviors>
							<local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource IntConverter}}"/>
						</Interaction.Behaviors>
					</TextBox>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- FloatFieldView -->
		<DataTemplate DataType="{x:Type pmcore:FloatFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock  Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
							   HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>	
				</StackPanel>

				<StackPanel Grid.Column="1" Orientation="Horizontal"> 
					<TextBox  Classes="property" VerticalAlignment="Center"
							  HorizontalAlignment="Left" Width="100" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}">
						<Interaction.Behaviors>
							<local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource FloatConverter2}}"/>
						</Interaction.Behaviors>
					</TextBox>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- SliderFloatFieldView -->
		<DataTemplate DataType="{x:Type pmcore:SliderFloatFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
								HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
				</StackPanel>
				
				<StackPanel Grid.Column="1" Orientation="Horizontal">
					<TextBox Classes="property" VerticalAlignment="Center"
						  HorizontalAlignment="Left" Width="100" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}">
						<Interaction.Behaviors>
							<local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource FloatConverter2}}"/>
						</Interaction.Behaviors>
					</TextBox>
					<Slider Minimum="{Binding min}" Maximum="{Binding max}" Value="{Binding value, Mode = TwoWay }" VerticalAlignment="Center" Width="200" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"/>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- SliderFloatPercentageFieldView -->
		<DataTemplate DataType="{x:Type pmcore:SliderFloatPercentageFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
								HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
				</StackPanel>
				
				<StackPanel Grid.Column="1" Orientation="Horizontal">
					<TextBox Classes="property" VerticalAlignment="Center"
						  HorizontalAlignment="Left" Width="100" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}">
						<Interaction.Behaviors>
							<local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource FloatToPercentageConverter}, ConverterParameter={Binding percentage_factor}}"/>
						</Interaction.Behaviors>
					</TextBox>
					<Slider Minimum="{Binding min}" Maximum="{Binding max}" Value="{Binding value, Mode = TwoWay }" VerticalAlignment="Center" Width="200" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"/>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- BoolFieldView -->
		<DataTemplate DataType="{x:Type pmcore:BoolFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
							   HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
				</StackPanel>
				<StackPanel Grid.Column="1" Orientation="Horizontal">					
					<CheckBox IsChecked="{Binding value}" VerticalAlignment="Center" 
							   HorizontalAlignment="Left" Width="100" Margin="15,0,0,0"
							   IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"
							  />
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- StringFieldView -->
		<DataTemplate DataType="{x:Type pmcore:StringFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
							   HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
				</StackPanel>

				<StackPanel Grid.Column="1" Orientation="Horizontal">
					<TextBox Classes="property" Text="{Binding value}" 
						 VerticalAlignment="Center" HorizontalAlignment="Left" Width="200" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"/>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- ChoiceFieldView -->
		<DataTemplate DataType="{x:Type pmcore:ChoiceFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" FontWeight="Bold"
							   HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
				</StackPanel>
				<StackPanel Grid.Column="1" Orientation="Horizontal">
					<ComboBox Classes="property" IsTextSearchEnabled="True"
							  HorizontalAlignment="Left" VerticalAlignment="Center" Width="200"
							  ItemsSource="{Binding Choices}" 
							  SelectedValue="{Binding value}" SelectedValueBinding="{Binding Key}" DisplayMemberBinding="{Binding Value}" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"/>
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>

		<!-- ButtonFieldView -->
		<DataTemplate DataType="{x:Type pmcore:ButtonFieldView}">
			<Grid ColumnDefinitions="3*,7*">
				<StackPanel Orientation="Horizontal" Grid.Column="0">
					<materialIcons:MaterialIcon Kind="Info" Margin="0,5,5,5" ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0" Opacity="{Binding HasTooltip, Converter={StaticResource BoolToOpacityConverter}}"/>
					<TextBlock  Classes="property" Text="{Binding, Converter={StaticResource LabelConverter}}" FontWeight="Bold"
								ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0"
								VerticalAlignment="Center"
								HorizontalAlignment="Left" TextAlignment="Left" Padding="20" FontSize="14" Width="200" MinWidth="200"/>
					</StackPanel>
				<StackPanel  Grid.Column="1" Orientation="Horizontal">

					<Button Width="100"
							Content="{materialIcons:MaterialIconExt Kind=PlayArrow}"
						ToolTip.Tip="{Binding tooltip}" ToolTip.ShowDelay="0"
						HorizontalAlignment="Left"
						VerticalAlignment="Center"
						HorizontalContentAlignment="Center"
						VerticalContentAlignment="Center"
						Click="bt_entity_field_button" IsEnabled="{Binding IsRuleAndSelectedPresetEditable}"/>				
					
					<TextBlock Text="{Binding info}" VerticalAlignment="Center" FontSize="12"/>
				</StackPanel>
			</Grid>
		</DataTemplate>
		
		<!-- EntityView -->
		<!-- This is used for all tabs, with optional sections for Rules tab -->
		<DataTemplate DataType="{x:Type pmcore:EntityView}">
			<Grid RowDefinitions="Auto,*" ColumnDefinitions="*">
				<StackPanel Orientation="Vertical">

					<!-- Preset 
					<Grid ColumnDefinitions="3*,5*,2*" IsVisible="{Binding HasPresets}" Background="DarkBlue" Margin="5,0,0,0">
						<TextBlock Grid.Column="0" Classes="property" Text="{Binding PresetLabel}" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="20" FontSize="16" FontWeight="Bold" Width="200" MinWidth="200"/>
						<ComboBox Grid.Column="1" Classes="property" IsTextSearchEnabled="True" HorizontalAlignment="Left" VerticalAlignment="Center" Width="200"
							  ItemsSource="{Binding Presets}"
							  SelectedValue="{Binding SelectedPreset}" SelectionChanged="preset_selectionchanged"/>
						<StackPanel  Grid.Column="2" HorizontalAlignment="Left" Orientation="Horizontal">
							<Button VerticalAlignment="Center" Classes="Primary"  Margin="0" Padding="5" ToolTip.Tip="Save and Apply" Click="bt_preset_apply_Click">
								<TextBlock Text="Apply"/>
							</Button>
						</StackPanel>
					</Grid>
					-->
					
					<!-- Fields IsEnabled="{Binding IsSelectedPresetEditable}" -->
					
					<ItemsControl ItemsSource="{Binding NormalFieldsShown}" Background="Transparent"  Grid.Row="4" IsVisible="{Binding HasFields}">
						<ItemsControl.ItemTemplate>
							<DataTemplate>
								<ContentControl Content="{Binding}"/>
							</DataTemplate>
						</ItemsControl.ItemTemplate>
					</ItemsControl>

					<!-- Advanced Fields -->
					<Expander Header="Advanced" IsVisible="{Binding HasAdvancedFieldsShown}" Margin="5,0,0,0">
						<ItemsControl ItemsSource="{Binding AdvancedFieldsShown}" Background="Transparent" Grid.Row="4" IsVisible="{Binding HasFields}">
							<ItemsControl.ItemTemplate>
								<DataTemplate>
									<ContentControl Content="{Binding}"/>
								</DataTemplate>
							</ItemsControl.ItemTemplate>
						</ItemsControl>
					</Expander>
					
				</StackPanel>
			</Grid>
		</DataTemplate>

	</UserControl.DataTemplates>
	
	<Border Classes="Card" Margin="7,7,-5,8">
		<TabControl Grid.Row="1" Grid.Column="1"
				Focusable="False" KeyboardNavigation.TabNavigation="None"
				SelectionChanged="" 
				TabStripPlacement="Right">

			<!-- Info -->
			<TabItem Name="tab_info" Header="Info" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None">
				<Grid RowDefinitions="Auto,*">

					<Grid Grid.Row="0" RowDefinitions="Auto,1">
						<TextBlock Grid.Row="0" Text="Game Info" Classes="h4" Margin="5,0,0,10" TextAlignment="Left"/>
						<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
					</Grid>

					<ScrollViewer Grid.Row="1" Margin="5,5,0,0">
						<ScrollViewer.Background>
							<SolidColorBrush Color="Black" Opacity="0.4"/>
						</ScrollViewer.Background>

						<av:HtmlPanel x:Name="html_panel"/>
						
					</ScrollViewer>

				</Grid>
			</TabItem>

			<!-- Settings -->
			<TabItem Name="tab_settings" Header="Settings" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None">
				<Grid RowDefinitions="Auto,*">

					<Grid Grid.Row="0" RowDefinitions="Auto,1">
						<TextBlock Grid.Row="0" Text="General Settings" Classes="h4" Margin="5,0,0,10" TextAlignment="Left"/>
						<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
					</Grid>

					<ScrollViewer Grid.Row="1" Margin="5,5,0,0">
						<ScrollViewer.Background>
							<SolidColorBrush Color="Black" Opacity="0.4"/>
						</ScrollViewer.Background>
						<ContentControl Content="{Binding GeneralSettingsEntity}"/>
					</ScrollViewer>
					
				</Grid>
			</TabItem>

			<!-- Rules -->
			<TabItem Name="tab_rules" Header="Rules" IsVisible="{Binding HasRulesSettings}"
					 HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
					<Grid RowDefinitions="Auto,Auto,*">

						<!-- Header -->
						<Grid Grid.Row="0" RowDefinitions="Auto,1,*">
							<TextBlock Grid.Row="0" Text="Rules" Classes="h4" Margin="5,0,0,10" TextAlignment="Left"/>
							<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
						</Grid>

						<!-- Preset -->
						<Grid Grid.Row="1" ColumnDefinitions="3*,5*,2*" DataContext="{Binding RulesSettingsEntity}" IsVisible="{Binding HasPresets}" Background="DarkBlue" Margin="5,0,0,0">
							<TextBlock Grid.Column="0" Classes="property" Text="{Binding PresetLabel}" HorizontalAlignment="Left" VerticalAlignment="Center" Padding="20" FontSize="16" FontWeight="Bold" Width="200" MinWidth="200"/>
							<ComboBox Grid.Column="1" Classes="property" IsTextSearchEnabled="True" HorizontalAlignment="Left" VerticalAlignment="Center" Width="200"
									ItemsSource="{Binding Presets}"
									SelectedValue="{Binding SelectedPreset}" SelectionChanged="preset_selectionchanged"/>
							<StackPanel  Grid.Column="2" HorizontalAlignment="Left" Orientation="Horizontal">
								<Button VerticalAlignment="Center" Classes="Primary"  Margin="0" Padding="5" ToolTip.Tip="Save and Apply" Click="bt_preset_apply_Click">
									<TextBlock Text="Apply"/>
								</Button>
								<Button Name="bt_preset_copy_popup" VerticalAlignment="Center" Classes="Primary"  Margin="0" Padding="5" ToolTip.Tip="Save and Apply" Click="bt_preset_copy_popup_Click">
									<TextBlock Text="Copy"/>
								</Button>
							</StackPanel>
						</Grid>

						<!--Popup -->
						<Popup Name="preset_copy_popup" PlacementTarget="{Binding #bt_preset_copy_popup}" DataContext="{Binding RulesSettingsEntity}">
							<Border Background="{DynamicResource ExpanderBackground}" Padding="10" CornerRadius="10">

								<StackPanel Orientation="Vertical">

									<TextBlock Text="Choose custom preset" HorizontalAlignment="Center" TextAlignment="Center" Margin="10"/>

									<ComboBox Classes="property"
										IsTextSearchEnabled="True"
										HorizontalAlignment="Center"
										VerticalAlignment="Center"
										Width="200"
										Margin="10"
										ItemsSource="{Binding CustomPresets}"
										SelectedValue="{Binding CustomPresetToOverwrite}" />

									<Button Content="Copy" Click="bt_preset_copy_Click" Margin="10"/>

									<Button Content="Cancel" Click="bt_preset_cancel_Click" Margin="10"/>

								</StackPanel>
							</Border>
						</Popup>

						<!-- Fields -->
						<ScrollViewer Grid.Row="2" Margin="5,5,0,0">
							<ScrollViewer.Background>
								<SolidColorBrush Color="Black" Opacity="0.4"/>
							</ScrollViewer.Background>
							<ContentControl Content="{Binding RulesSettingsEntity}"/>
						</ScrollViewer>

					</Grid>

			</TabItem>
			
			<!-- Visual -->
			<TabItem Name="tab_visual" Header="Visual" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<Grid RowDefinitions="Auto,*">

					<Grid Grid.Row="0" RowDefinitions="Auto,1">
						<TextBlock Grid.Row="0" Text="Visual Settings" Classes="h4" Margin="5,0,0,10" TextAlignment="Left"/>
						<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
					</Grid>

					<ScrollViewer Grid.Row="1" Margin="5,5,0,0">
						<ScrollViewer.Background>
							<SolidColorBrush Color="Black" Opacity="0.4"/>
						</ScrollViewer.Background>
						<ContentControl Content="{Binding VisualSettingsEntity}"/>
					</ScrollViewer>

				</Grid>
			</TabItem>

		</TabControl>
	</Border>

</UserControl>
