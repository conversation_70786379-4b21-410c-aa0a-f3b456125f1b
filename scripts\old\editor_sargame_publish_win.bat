c:
cd "C:\G\Codice\TABULA.PMCORE\PWGClient_xplat"

REM Main dotnet compile, as a single file (only some dlls are external)

dotnet publish PWGClient.Desktop -c Release /p:PublishProfile=PWGClient.Desktop\Properties\PublishProfiles\Windows.pubxml

REM Protect

"C:\Program Files (x86)\Eziriz\.NET Reactor\dotNET_Reactor.Console.exe" -project "Reactor_S-ARGAME_Editor_dotnet_win.nrproj"

REM Copy important Dlls not in the single file

xcopy PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\*.dll deploy\win-x64\ /Y

REM Remove unwanted dlls

del deploy\win-x64\S-ARGAME_Editor.dll

REM Copy the deploy (exe and dll) also to OneDrive build folder (for devteam testing)

xcopy deploy\win-x64\S-ARGAME_Editor.exe "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_latest" /Y
xcopy deploy\win-x64\*.dll "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_latest" /Y

REM Create the installer (in D:\OneDrive\_DEPLOY\s-argame\editor)

iscc.exe S-ARGAME_Editor.iss

REM Copy the changelogs to _DEPLOY\s-argame\editor

xcopy installer\changelogs "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor\changelogs\" /Y

REM  Generate NetSparkle appcast in _DEPLOY
REM TODO: now suspended, must be done manually

REM REM netsparkle.exe -a "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor" -e exe -b "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor" -n "S-ARGAME Editor" -u "https://sargame-cdn.tabulatouch.com/win_x64/editor" -p "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor\changelogs" -l "https://sargame-cdn.tabulatouch.com/win_x64/editor/changelogs" --overwrite-old-items --human-readable

REM Sync to Wasabi bucket (only /editor)
REM rclone sync "D:\OneDrive\_DEPLOY\s-argame\editor" wasabi:sargame-cdn/editor -P --log-level=INFO

REM RSync to KeyCDN using WSL and private key (PLEASE DO MANUALLY ON THE WHOLE FOLDER)




