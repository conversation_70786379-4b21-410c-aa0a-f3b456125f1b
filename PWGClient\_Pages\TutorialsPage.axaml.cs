using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using Tabula.Licensing.LicenseActivator;
using Sentry;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class TutorialsPage : UserControl
	{
		public TutorialsPage()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			TutorialEntry.DownloadTutorials();

			base.OnLoaded(e);
		}

		async void bt_tutorial_click(object sender, RoutedEventArgs args)
		{
			try
			{
				var tutorial = (sender as Control).DataContext as TutorialEntry;

				App.OpenURL(tutorial.tutorial_url);
			}
			catch { }
		}

	}

	

}
