﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <Design.PreviewWith>
    <Border Padding="20">
      <TabControl Classes="sidebar">
        <TabItem Header="Item1"/>
        <TabItem Header="Item2"/>
      </TabControl>
    </Border>
  </Design.PreviewWith>
  <Style Selector="TabControl.sidebar">
    <Setter Property="TabStripPlacement" Value="Left"/>
    <Setter Property="Padding" Value="0 0 0 0"/>
    <Setter Property="Background" Value="{x:Null}"/>
    <Setter Property="Template">
      <ControlTemplate>
        <Border
            Margin="{TemplateBinding Margin}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}">
          <DockPanel>
            <ScrollViewer Width="150" Margin="0 0 0 0"
                Name="PART_ScrollViewer"
                HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                Background="{TemplateBinding Background}"
                DockPanel.Dock="Left">
              <ItemsPresenter
                  Name="PART_ItemsPresenter"
                  Items="{TemplateBinding Items}"
                  ItemsPanel="{TemplateBinding ItemsPanel}"
                  ItemTemplate="{TemplateBinding ItemTemplate}">
              </ItemsPresenter>
            </ScrollViewer>
            <ContentControl Content="{TemplateBinding Tag}" HorizontalContentAlignment="Right" DockPanel.Dock="Bottom"/>
            <ScrollViewer Background="{x:Null}"
                HorizontalScrollBarVisibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=SelectedItem.(ScrollViewer.HorizontalScrollBarVisibility)}"
                VerticalScrollBarVisibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=SelectedItem.(ScrollViewer.VerticalScrollBarVisibility)}">
              <ContentPresenter
                      Name="PART_SelectedContentHost"
                      Margin="{TemplateBinding Padding}"
                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                      Content="{TemplateBinding SelectedContent}"
                      ContentTemplate="{TemplateBinding SelectedContentTemplate}">
              </ContentPresenter>
            </ScrollViewer>
          </DockPanel>
        </Border>
      </ControlTemplate>
    </Setter>
  </Style>

  <Style Selector="TabControl.sidebar > TabItem">
    <Setter Property="BorderThickness" Value="0"/>    
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="Margin" Value="0"/>
    <Setter Property="Padding" Value="16"/>    
    <Setter Property="Width" Value="240" />
    <Setter Property="HorizontalContentAlignment" Value="Stretch" />    
    <Setter Property="(ScrollViewer.HorizontalScrollBarVisibility)" Value="Auto"/>
    <Setter Property="(ScrollViewer.VerticalScrollBarVisibility)" Value="Auto"/>
  </Style>
  <Style Selector="TabControl.sidebar > TabItem:selected /template/ Border#PART_SelectedPipe">
    <Setter Property="IsVisible" Value="False" />
  </Style>
  <Style Selector="TabControl.sidebar > TabItem:selected">
    <Setter Property="Background" Value="#1fffffff"/>
  </Style>

  <Style Selector="TabControl.sidebar > TabItem:pointerover /template/ Border#PART_LayoutRoot">
    <Setter Property="Background" Value="#3fffffff"/>    
  </Style>
</Styles>
