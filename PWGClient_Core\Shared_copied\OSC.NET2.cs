﻿//TABULA_GUID:{ACB231CF-1D35-4BD6-ADFA-85A0C25D81CA}
using System;
using System.Collections;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Threading;

// OSC.NET

/** Defines:
 */

/* Version:
 *  1.1    added support for bool, converted to int
 *  1.0.1  internal keywords
 *  1.0
 */

namespace OSC.NET
{
    /// <summary>
    /// OSCTransmitter
    /// </summary>
    internal class OSCTransmitter
    {
        protected UdpClient udpClient;
        protected string remoteHost;
        protected int remotePort;

        public OSCTransmitter(string remoteHost, int remotePort)
        {
            this.remoteHost = remoteHost;
            this.remotePort = remotePort;
            Connect();
        }

        public void Connect()
        {
            if (this.udpClient != null) Close();
            this.udpClient = new UdpClient(this.remoteHost, this.remotePort);
        }

        public void Close()
        {
            this.udpClient.Close();
            this.udpClient = null;
        }

        public int Send(OSCPacket packet)
        {
            int byteNum = 0;
            byte[] data = packet.BinaryData;
            try
            {
                byteNum = this.udpClient.Send(data, data.Length);
            }
            catch (Exception e)
            {
                Tabula.Log.Logger.DefaultLog.logException("OSCPacket.Send", e);
            }

            return byteNum;
        }
    }

    /// <summary>
    /// OSCPacket
    /// </summary>
    abstract internal class OSCPacket
    {
        public static readonly Encoding ASCIIEncoding8Bit;
        public bool ExtendedVVVVMode { get; set; }

        public IPEndPoint client_ip;

        static OSCPacket()
        {
#if UNITY_STANDALONE || UNITY_IOS || UNITY_ANDROID

            // Encoding 1252 requires extra DLL (all I18N dlls from Editor\Data\Mono\lib\mono\unity), switching encoding gets rid of problem
            // https://answers.unity.com/questions/42955/codepage-1252-not-supported-works-in-editor-but-no.html
            // https://forum.unity.com/threads/c-receiving-osc-tuio-data-works-not-after-build.356094/

            ASCIIEncoding8Bit = Encoding.Default;
#else
            // Standard in Windows, no problems
            // ASCIIEncoding8Bit = Encoding.GetEncoding(1252);
            ASCIIEncoding8Bit = Encoding.Default; // problems with NET5 ?
#endif
        }

        public OSCPacket(bool extendedMode = false)
        {
            this.ExtendedVVVVMode = extendedMode;
            this.values = new ArrayList();
        }

        protected static void addBytes(ArrayList data, byte[] bytes)
        {
            foreach (byte b in bytes)
            {
                data.Add(b);
            }
        }

        protected static void padNull(ArrayList data)
        {
            byte zero = 0;
            int pad = 4 - (data.Count % 4);
            for (int i = 0; i < pad; i++)
            {
                data.Add(zero);
            }
        }

        internal static byte[] swapEndian(byte[] data)
        {
            byte[] swapped = new byte[data.Length];
            for (int i = data.Length - 1, j = 0; i >= 0; i--, j++)
            {
                swapped[j] = data[i];
            }
            return swapped;
        }

        protected static byte[] packInt(int value)
        {
            byte[] data = BitConverter.GetBytes(value);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packLong(long value)
        {
            byte[] data = BitConverter.GetBytes(value);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packFloat(float value)
        {
            byte[] data = BitConverter.GetBytes(value);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packDouble(double value)
        {
            byte[] data = BitConverter.GetBytes(value);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packString(string value)
        {
            return ASCIIEncoding8Bit.GetBytes(value);
        }

        protected static byte[] packChar(char value)
        {
            byte[] data = BitConverter.GetBytes(value);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packBlob(Stream value)
        {
            var mem = new MemoryStream();
            value.Seek(0, SeekOrigin.Begin);
            
#if UNITY_2017_1_OR_NEWER
            value.CopyTo(mem);
#elif UNITY_5_3_OR_NEWER
            byte[] buffer = new byte[4096];
            int count;
            while ((count = value.Read(buffer, 0, buffer.Length)) != 0)
                mem.Write(buffer, 0, count);
#else
            value.CopyTo(mem);
#endif

            byte[] valueData = mem.ToArray();

            var lData = new ArrayList();

            var length = packInt(valueData.Length);

            lData.AddRange(length);
            lData.AddRange(valueData);

            return (byte[])lData.ToArray(typeof(byte));
        }

        protected static byte[] packBlob(byte[] value)
        {
            byte[] valueData = value;

            var lData = new ArrayList();

            var length = packInt(valueData.Length);

            lData.AddRange(length);
            lData.AddRange(valueData);

            return (byte[])lData.ToArray(typeof(byte));
        }

        protected static byte[] packTimeTag(DateTime value)
        {
            var tag = new OscTimeTag();
            tag.Set(value);

            return tag.ToByteArray(); ;
        }

        protected static byte[] packObject(object value)
        {
            string serialized_object = OSCMessage.OnObjectSerialize?.Invoke(value);

            return packString(serialized_object);
        }

        /*
        protected static byte[] packColor(RGBAColor value)
        {
            double[] rgba = { value.R, value.G, value.B, value.A };

            byte[] data = new byte[rgba.Length];
            for (int i = 0; i < rgba.Length; i++) data[i] = (byte)Math.Round(rgba[i] * 255);
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return data;
        }

        protected static byte[] packVector2D(Vector2D value)
        {
            int length = 2;
            int compLength = BitConverter.GetBytes(new double()).Length;

            byte[] data = new byte[compLength * length];

            for (int i = 0; i < length; i++)
            {
                byte[] component = packDouble(value[i]);
                component.CopyTo(data, compLength * i);
            }
            return data;
        }

        protected static byte[] packVector3D(Vector3D value)
        {
            int length = 3;
            int compLength = BitConverter.GetBytes(new double()).Length;

            byte[] data = new byte[compLength * length];

            for (int i = 0; i < length; i++)
            {
                byte[] component = packDouble(value[i]);
                component.CopyTo(data, compLength * i);
            }
            return data;
        }


        protected static byte[] packVector4D(Vector4D value)
        {
            int length = 4;
            int compLength = BitConverter.GetBytes(new double()).Length;

            byte[] data = new byte[compLength * length];

            for (int i = 0; i < length; i++)
            {
                byte[] component = packDouble(value[i]);
                component.CopyTo(data, compLength * i);
            }
            return data;
        }

        protected static byte[] packMatrix(Matrix4x4 value)
        {
            int length = 16;
            int compLength = BitConverter.GetBytes(new float()).Length;

            byte[] data = new byte[compLength * length];

            for (int i = 0; i < length; i++)
            {
                byte[] component = packFloat((float)value[i]);
                component.CopyTo(data, compLength * i);
            }
            return data;
        }
        */

        abstract protected void pack();
        protected byte[] binaryData;
        public byte[] BinaryData
        {
            get
            {
                pack();
                return binaryData;
            }
        }

        protected static int unpackInt(byte[] bytes, ref int start)
        {
            byte[] data = new byte[4];
            for (int i = 0; i < 4; i++, start++) data[i] = bytes[start];
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return BitConverter.ToInt32(data, 0);
        }

        protected static long unpackLong(byte[] bytes, ref int start)
        {
            byte[] data = new byte[8];
            for (int i = 0; i < 8; i++, start++) data[i] = bytes[start];
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return BitConverter.ToInt64(data, 0);
        }

        protected static float unpackFloat(byte[] bytes, ref int start)
        {
            byte[] data = new byte[4];
            for (int i = 0; i < 4; i++, start++) data[i] = bytes[start];
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return BitConverter.ToSingle(data, 0);
        }

        protected static double unpackDouble(byte[] bytes, ref int start)
        {
            byte[] data = new byte[8];
            for (int i = 0; i < 8; i++, start++) data[i] = bytes[start];
            if (BitConverter.IsLittleEndian) data = swapEndian(data);
            return BitConverter.ToDouble(data, 0);
        }

        protected static string unpackString(byte[] bytes, ref int start)
        {
            int count = 0;
            for (int index = start; bytes[index] != 0; index++, count++) ;
            string s = ASCIIEncoding8Bit.GetString(bytes, start, count);
            start += count + 1;
            start = (start + 3) / 4 * 4;
            return s;
        }

        protected static char unpackChar(byte[] bytes, ref int start)
        {
            byte[] data = { bytes[start] };
            return BitConverter.ToChar(data, 0);
        }

        protected static Stream unpackBlob(byte[] bytes, ref int start)
        {
            int length = unpackInt(bytes, ref start);

            byte[] buffer = new byte[length];
            Array.Copy(bytes, start, buffer, 0, length);

            start += length;
            start = (start + 3) / 4 * 4;
            return new MemoryStream(buffer);
        }

        protected static object unpackObject(byte[] bytes, ref int start)
        {
            string serialized_string = unpackString(bytes, ref start);

            return OSCMessage.OnObjectDeserialize?.Invoke(serialized_string);
        }

        /*
        protected static RGBAColor unpackColor(byte[] bytes, ref int start)
        {
            byte[] data = new byte[4];
            for (int i = 0; i < 4; i++, start++) data[i] = bytes[start];
            if (BitConverter.IsLittleEndian) data = swapEndian(data);

            var col = new RGBAColor();
            col.R = (double)data[0] / 255.0;
            col.G = (double)data[1] / 255.0;
            col.B = (double)data[2] / 255.0;
            col.A = (double)data[3] / 255.0;

            return col;
        }

        protected static Vector2D unpackVector2D(byte[] bytes, ref int start)
        {
            var v = new Vector2D();
            v.x = unpackDouble(bytes, ref start);
            v.y = unpackDouble(bytes, ref start);
            return v;
        }

        protected static Vector3D unpackVector3D(byte[] bytes, ref int start)
        {
            var v = new Vector3D();
            v.x = unpackDouble(bytes, ref start);
            v.y = unpackDouble(bytes, ref start);
            v.z = unpackDouble(bytes, ref start);
            return v;
        }

        protected static Vector4D unpackVector4D(byte[] bytes, ref int start)
        {
            var v = new Vector4D();
            v.x = unpackDouble(bytes, ref start);
            v.y = unpackDouble(bytes, ref start);
            v.z = unpackDouble(bytes, ref start);
            v.w = unpackDouble(bytes, ref start);
            return v;
        }

        protected static Matrix4x4 unpackMatrix(byte[] bytes, ref int start)
        {
            var m = new Matrix4x4();
            for (int i = 0; i < 16; i++) m[i] = unpackFloat(bytes, ref start);
            return m;
        }
        */

        protected static DateTime unpackTimeTag(byte[] bytes, ref int start)
        {
            byte[] data = new byte[8];
            for (int i = 0; i < 8; i++, start++) data[i] = bytes[start];
            var tag = new OscTimeTag(data);

            return tag.DateTime;
        }

        public static OSCPacket Unpack(byte[] bytes, bool extendedMode = false)
        {
            int start = 0;
            return Unpack(bytes, ref start, bytes.Length, extendedMode);
        }

        public static OSCPacket Unpack(byte[] bytes, ref int start, int end, bool extendedMode = false)
        {
            if (bytes[start] == '#') return OSCBundle.Unpack(bytes, ref start, end, extendedMode);
            else return OSCMessage.Unpack(bytes, ref start, extendedMode);
        }


        protected string address;
        public string Address
        {
            get { return address; }
            set
            {
                // TODO: validate
                address = value;
            }
        }

        protected ArrayList values;
        public ArrayList Values
        {
            get
            {
                return (ArrayList)values.Clone();
            }
        }
        abstract public void Append(object value);

        abstract public bool IsBundle();
    }

    internal class OSCMessage : OSCPacket
    {
        //      These Attributes adhere to the OSC Specs 1.0
        protected const char INTEGER = 'i'; // int32 8byte
        protected const char FLOAT = 'f'; //float32 8byte
        protected const char LONG = 'h';  //int64 16byte
        protected const char DOUBLE = 'd'; // float64 16byte
        protected const char STRING = 's'; // padded by zeros
        protected const char SYMBOL = 'S'; // same as STRING really
        protected const char BLOB = 'b'; // bytestream, starts with an int that tells the total length of th stream
        protected const char TIMETAG = 't'; // fixed point floating number with 32bytes (16bytes for totaldays after 1.1.1900 and 16bytes for fractionOfDay)
        protected const char CHAR = 'c'; // bit
        protected const char COLOR = 'r'; // 4x8bit -> rgba
        protected const char OBJECT = 'O';  // user-implemented serialized object

        //protected const char TRUE	  = 'T';
        //protected const char FALSE = 'F';
        protected const char NIL = 'N';
        //protected const char INFINITUM = 'I';

        //protected const char ALL     = '*';

        //      These Attributes are added for convenience within vvvv. They are NOT part of the OSC Specs, but are VERY useful if you want to make vvvv talk to another instance of vvvv
        //      Using them requires to set the ExtendedVVVVMethod property to true (with the constructor or with the Unpack methods, depending if you want to send or receive)
        protected const char VECTOR2D = 'v'; // synonym to dd
        protected const char VECTOR3D = 'V'; // synonym to ddd
        protected const char QUATERNION = 'q'; // synonym to dddd
        protected const char MATRIX4 = 'M';  // for 4x4 Matrices with float, so synonym to ffffffffffffffff

        // User defined function to serialize/deserialize objects
        public static Func<object, string> OnObjectSerialize;
        public static Func<string, object> OnObjectDeserialize;

        public OSCMessage(string address, bool extendedMode = false)
            : base(extendedMode)
        {
            this.typeTag = ",";
            this.Address = address;
        }
        public OSCMessage(string address, object value, bool extendedMode = false)
            : base(extendedMode)
        {
            this.typeTag = ",";
            this.Address = address;
            Append(value);
        }

        override protected void pack()
        {
            ArrayList data = new ArrayList();

            addBytes(data, packString(this.address));
            padNull(data);
            addBytes(data, packString(this.typeTag));
            padNull(data);

            foreach (object value in this.Values)
            {
                switch (value)
                {
                    case int i: addBytes(data, packInt(i)); break;

                    case long l: addBytes(data, packLong(l)); break;

                    case float f: addBytes(data, packFloat(f)); break;

                    case double d: addBytes(data, packDouble(d)); break;

                    case string s:
                        {
                            addBytes(data, packString(s));
                            padNull(data);
                            break;
                        }

                    case Stream stream:
                        {
                            addBytes(data, packBlob(stream));
                            padNull(data);
                            break;
                        }

                    case byte[] barr:
                        {
                            // panda
                            addBytes(data, packBlob(barr));
                            padNull(data);
                            break;
                        }

                    case char c: addBytes(data, packChar(c)); break;

                    case DateTime dt: addBytes(data, packTimeTag(dt)); break;

                    case object o:
                        {
                            addBytes(data, packObject(o));
                            padNull(data);
                            break;
                        }

                    default:
                        // null field? 
                        padNull(data);
                        break;
                }

                /*
                if (value is int) addBytes(data, packInt((int)value));
                else if (value is long) addBytes(data, packLong((long)value));
                else if (value is float) addBytes(data, packFloat((float)value));
                else if (value is double) addBytes(data, packDouble((double)value));
                else if (value is string)
                {
                    addBytes(data, packString((string)value));
                    padNull(data);
                }
                else if (value is Stream)
                {
                    addBytes(data, packBlob((Stream)value));
                    padNull(data);
                }
                else if (value is byte[])
                {
                    // panda
                    addBytes(data, packBlob((byte[])value));
                    padNull(data);
                }
                //else if (value is RGBAColor) addBytes(data, packColor((RGBAColor)value));
                else if (value is char) addBytes(data, packChar((char)value));
                else if (value is DateTime)
                {
                    addBytes(data, packTimeTag((DateTime)value));
                }
                else if (ExtendedVVVVMode)
                {
                    
                    if (value is Vector2D) addBytes(data, packVector2D((Vector2D)value));
                    else if (value is Vector3D) addBytes(data, packVector3D((Vector3D)value));
                    else if (value is Vector4D) addBytes(data, packVector4D((Vector4D)value));
                    else if (value is Matrix4x4) addBytes(data, packMatrix((Matrix4x4)value));
                     
                }*/
            }

            this.binaryData = (byte[])data.ToArray(typeof(byte));
        }


        public static OSCMessage Unpack(byte[] bytes, ref int start, bool extendedMode = false)
        {
            string address = unpackString(bytes, ref start);
            //Console.WriteLine("address: " + address);
            OSCMessage msg = new OSCMessage(address, extendedMode);

            char[] tags = unpackString(bytes, ref start).ToCharArray();
            //Console.WriteLine("tags: " + new string(tags));
            foreach (char tag in tags)
            {
                //Console.WriteLine("tag: " + tag + " @ "+start);
                switch (tag)
                {
                    case ',': continue;
                    case INTEGER: msg.Append(unpackInt(bytes, ref start)); break;
                    case LONG: msg.Append(unpackLong(bytes, ref start)); break;
                    case DOUBLE: msg.Append(unpackDouble(bytes, ref start)); break;
                    case FLOAT: msg.Append(unpackFloat(bytes, ref start)); break;
                    case STRING:
                    case SYMBOL:
                        msg.Append(unpackString(bytes, ref start));
                        break;
                    case CHAR: msg.Append(unpackChar(bytes, ref start)); break;
                    case BLOB: msg.Append(unpackBlob(bytes, ref start)); break;
                    case TIMETAG: msg.Append(unpackTimeTag(bytes, ref start)); break;
                    //case COLOR: msg.Append(unpackColor(bytes, ref start)); break;

                    case OBJECT: msg.Append(unpackObject(bytes, ref start)); break;

                    default:
                        Tabula.Log.Logger.DefaultLog.logException("OSCMessage.Unpack", new Exception("unknown tag: " + tag));
                        break;
                }

                /*
                if (tag == ',') continue;
                else if (tag == INTEGER) msg.Append(unpackInt(bytes, ref start));
                else if (tag == LONG) msg.Append(unpackLong(bytes, ref start));
                else if (tag == DOUBLE) msg.Append(unpackDouble(bytes, ref start));
                else if (tag == FLOAT) msg.Append(unpackFloat(bytes, ref start));
                else if (tag == STRING || tag == SYMBOL) msg.Append(unpackString(bytes, ref start));

                else if (tag == CHAR) msg.Append(unpackChar(bytes, ref start));
                else if (tag == BLOB) msg.Append(unpackBlob(bytes, ref start));
                //else if (tag == COLOR) msg.Append(unpackColor(bytes, ref start));
                else if (tag == TIMETAG) msg.Append(unpackTimeTag(bytes, ref start));
                

                //              here come the custom vvvv datatypes
                else if (extendedMode)
                {
                    if (tag == VECTOR2D) msg.Append(unpackVector2D(bytes, ref start));
                    else if (tag == VECTOR3D) msg.Append(unpackVector3D(bytes, ref start));
                    else if (tag == QUATERNION) msg.Append(unpackVector4D(bytes, ref start));
                    else if (tag == MATRIX4) msg.Append(unpackMatrix(bytes, ref start));
                }
                else
                    Tabula.Log.Logger.DefaultLog.logException("OSCMessage.Unpack", new Exception("unknown tag: " + tag));
                */
            }
            return msg;
        }

        override public void Append(object value)
        {
            switch (value)
            {
                case bool b:
                    {
                        AppendTag(INTEGER); // bool is promoted to integer
                        values.Add(Convert.ToInt32(b));
                        break;
                    }

                case int i:
                    {
                        AppendTag(INTEGER);
                        values.Add(i);
                        break;
                    }

                case long l:
                    {
                        AppendTag(LONG);
                        values.Add(l);
                        break;
                    }

                case float f:
                    {
                        AppendTag(FLOAT);
                        values.Add(f);
                        break;
                    }

                case double d:
                    {
                        AppendTag(DOUBLE);
                        values.Add(d);
                        break;
                    }

                case string s:
                    {
                        AppendTag(STRING);
                        values.Add(s);
                        break;
                    }

                case char c:
                    {
                        AppendTag(CHAR);
                        values.Add(c);
                        break;
                    }

                case Stream stream:
                    {
                        AppendTag(BLOB);
                        values.Add(stream);
                        break;
                    }

                case DateTime dt:
                    {
                        AppendTag(TIMETAG);
                        values.Add(dt);
                        break;
                    }

                default:

                    // will later attempt to serialize the object
                    if (OnObjectSerialize!=null)
                    {
                        AppendTag(OBJECT);
                        values.Add(value);
                    }
                    else                    
                        Fallback();

                    break;
            }
            
            
            /*else if (value is RGBAColor)
            {
                AppendTag(COLOR);
            }
            else if (ExtendedVVVVMode)
            {
                /*
                if (value is Vector2D)
                {
                    AppendTag(VECTOR2D);
                }
                else if (value is Vector3D)
                {
                    AppendTag(VECTOR3D);
                }
                else if (value is Vector4D)
                {
                    AppendTag(QUATERNION);
                }
                else if (value is Matrix4x4)
                {
                    AppendTag(MATRIX4);
                }
                else
                {
                    Fallback();
                    return;
                }
                 
            }*/
           
            
        }

        private void Fallback()
        {
            AppendTag(NIL);
            //	        values.Add("undefined");
        }

        protected string typeTag;
        protected void AppendTag(char type)
        {
            typeTag += type;
        }

        override public bool IsBundle() { return false; }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(this.Address + " ");
            for (int i = 0; i < values.Count; i++)
                sb.Append(values[i].ToString() + " ");
            return sb.ToString();
        }
    }

    internal class OSCBundle : OSCPacket
    {
        protected const string BUNDLE = "#bundle";
        private DateTime timestamp = new DateTime();

        public OSCBundle(DateTime ts, bool extendedMode = false)
            : base(extendedMode)
        {
            this.address = BUNDLE;
            this.timestamp = ts;
        }

        public OSCBundle(long ts, bool extendedMode = false)
            : base(extendedMode)
        {
            DateTime start = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            timestamp = start.AddMilliseconds(ts).ToLocalTime();
        }


        public OSCBundle(bool extendedMode = false)
            : base(extendedMode)
        {
            this.address = BUNDLE;
            this.timestamp = DateTime.Now;
        }

        override protected void pack()
        {
            ArrayList data = new ArrayList();

            addBytes(data, packString(this.Address));
            padNull(data);
            addBytes(data, packTimeTag(timestamp));  // fixed point, 8 bytes

            foreach (OSCPacket oscPacket in this.Values)
            {
                if (oscPacket != null)
                {
                    byte[] bs = oscPacket.BinaryData;
                    addBytes(data, packInt(bs.Length));
                    addBytes(data, bs);
                }
                else
                {
                    // TODO
                }
            }

            this.binaryData = (byte[])data.ToArray(typeof(byte));
        }

        public static new OSCBundle Unpack(byte[] bytes, ref int start, int end, bool extendedMode = false)
        {

            string address = unpackString(bytes, ref start);
            //Console.WriteLine("bundle: " + address);
            if (!address.Equals(BUNDLE)) return null; // TODO

            DateTime timestamp = unpackTimeTag(bytes, ref start);
            OSCBundle bundle = new OSCBundle(timestamp, extendedMode);

            while (start < end)
            {
                int length = unpackInt(bytes, ref start);
                int sub_end = start + length;
                bundle.Append(OSCPacket.Unpack(bytes, ref start, sub_end, extendedMode));
            }

            return bundle;
        }

        public DateTime getTimeStamp()
        {
            return timestamp;
        }

        override public void Append(object value)
        {
            if (value is OSCPacket)
            {
                values.Add(value);
            }
            else
            {
                // TODO: exception
            }
        }

        override public bool IsBundle() { return true; }
    }

    internal class OscTimeTag
    {
        /// <summary>
        /// Osc Time Epoch (January 1, 1900 00:00:00).
        /// </summary>
        public static readonly DateTime Epoch = new DateTime(1900, 1, 1, 0, 0, 0, 0);

        /// <summary>
        /// Minimum Osc Time Tag.
        /// </summary>
        public static readonly OscTimeTag MinValue = new OscTimeTag(Epoch + TimeSpan.FromMilliseconds(1.0));

        /// <summary>
        /// Gets the first 32 bits of the Osc Time Tag. Specifies the number of seconds since the epoch.
        /// </summary>
        public uint SecondsSinceEpoch
        {
            get
            {
                return (uint)(mTimeStamp - Epoch).TotalSeconds;
            }
        }

        /// <summary>
        /// Gets the last 32 bits of the Osc Time Tag. Specifies the fractional part of a second.
        /// </summary>
        public uint FractionalSecond
        {
            get
            {
                return (uint)((mTimeStamp - Epoch).Milliseconds);
            }
        }

        /// <summary>
        /// Gets the Osc Time Tag as a DateTime value.
        /// </summary>
        public DateTime DateTime
        {
            get
            {
                return mTimeStamp;
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OscTimeTag"/> class.
        /// </summary>
        /// <remarks>Defaults the Osc Time Tag value to DateTime.Now.</remarks>
        public OscTimeTag()
            : this(DateTime.Now)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OscTimeTag"/> class.
        /// </summary>
        /// <param name="timeStamp">The time stamp to use to set the Osc Time Tag.</param>
        public OscTimeTag(DateTime timeStamp)
        {
            Set(timeStamp);
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OscTimeTag"/> class.
        /// </summary>
        /// <param name="data">The time stamp to use to set the Osc Time Tag.</param>
        public OscTimeTag(byte[] data)
        {
            byte[] secondsSinceEpochData = new byte[4];
            Array.Copy(data, 0, secondsSinceEpochData, 0, 4);

            byte[] fractionalSecondData = new byte[4];
            Array.Copy(data, 4, fractionalSecondData, 0, 4);

            if (BitConverter.IsLittleEndian)
            {
                secondsSinceEpochData = OSCPacket.swapEndian(secondsSinceEpochData);
                fractionalSecondData = OSCPacket.swapEndian(fractionalSecondData);
            }

            uint secondsSinceEpoch = BitConverter.ToUInt32(secondsSinceEpochData, 0);
            uint fractionalSecond = BitConverter.ToUInt32(fractionalSecondData, 0);

            DateTime timeStamp = Epoch.AddSeconds(secondsSinceEpoch).AddMilliseconds(fractionalSecond);
            if (!IsValidTime(timeStamp)) throw new Exception("Not a valid OSC Timetag discovered.");
            mTimeStamp = timeStamp;
        }

        /// <summary>
        /// Convert the Osc Time Tag to a byte array.
        /// </summary>
        /// <returns>A byte array containing the Osc Time Tag.</returns>
        public byte[] ToByteArray()
        {
            List<byte> timeStamp = new List<byte>();

            byte[] secondsSinceEpoch = BitConverter.GetBytes(SecondsSinceEpoch);
            byte[] fractionalSecond = BitConverter.GetBytes(FractionalSecond);

            if (BitConverter.IsLittleEndian) // != OscPacket.LittleEndianByteOrder)
            {
                secondsSinceEpoch = OSCPacket.swapEndian(secondsSinceEpoch);
                fractionalSecond = OSCPacket.swapEndian(fractionalSecond);
            }

            timeStamp.AddRange(secondsSinceEpoch);
            timeStamp.AddRange(fractionalSecond);

            return timeStamp.ToArray();
        }

        /// <summary>
        /// Determines whether two specified instances of OscTimeTag are equal.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs and rhs represent the same time tag; otherwise, false.</returns>
        public static bool Equals(OscTimeTag lhs, OscTimeTag rhs)
        {
            return lhs.Equals(rhs);
        }

        /// <summary>
        /// Determines whether two specified instances of OscTimeTag are equal.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs and rhs represent the same time tag; otherwise, false.</returns>
        public static bool operator ==(OscTimeTag lhs, OscTimeTag rhs)
        {
            if (ReferenceEquals(lhs, rhs))
            {
                return true;
            }

            if (((object)lhs == null) || ((object)rhs == null))
            {
                return false;
            }

            return lhs.DateTime == rhs.DateTime;
        }

        /// <summary>
        /// Determines whether two specified instances of OscTimeTag are not equal.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs and rhs do not represent the same time tag; otherwise, false.</returns>
        public static bool operator !=(OscTimeTag lhs, OscTimeTag rhs)
        {
            return !(lhs == rhs);
        }

        /// <summary>
        /// Determines whether one specified <see cref="OscTimeTag"/> is less than another specified <see cref="OscTimeTag"/>.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs is less than rhs; otherwise, false.</returns>        
        public static bool operator <(OscTimeTag lhs, OscTimeTag rhs)
        {
            return lhs.DateTime < rhs.DateTime;
        }

        /// <summary>
        /// Determines whether one specified <see cref="OscTimeTag"/> is less than or equal to another specified <see cref="OscTimeTag"/>.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs is less than or equal to rhs; otherwise, false.</returns>        
        public static bool operator <=(OscTimeTag lhs, OscTimeTag rhs)
        {
            return lhs.DateTime <= rhs.DateTime;
        }

        /// <summary>
        /// Determines whether one specified <see cref="OscTimeTag"/> is greater than another specified <see cref="OscTimeTag"/>.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs is greater than rhs; otherwise, false.</returns>        
        public static bool operator >(OscTimeTag lhs, OscTimeTag rhs)
        {
            return lhs.DateTime > rhs.DateTime;
        }

        /// <summary>
        /// Determines whether one specified <see cref="OscTimeTag"/> is greater than or equal to another specified <see cref="OscTimeTag"/>.
        /// </summary>
        /// <param name="lhs">An OscTimeTag.</param>
        /// <param name="rhs">An OscTimeTag.</param>
        /// <returns>true if lhs is greater than or equal to rhs; otherwise, false.</returns>        
        public static bool operator >=(OscTimeTag lhs, OscTimeTag rhs)
        {
            return lhs.DateTime >= rhs.DateTime;
        }

        /// <summary>
        /// Validates the time stamp for use in an Osc Time Tag.
        /// </summary>
        /// <param name="timeStamp">The time stamp to validate.</param>
        /// <returns>True if the time stamp is a valid Osc Time Tag; false, otherwise.</returns>
        /// <remarks>Time stamps must be greater-than-or-equal to <see cref="OscTimeTag.MinValue"/>.</remarks>
        public static bool IsValidTime(DateTime timeStamp)
        {
            return (timeStamp >= Epoch + TimeSpan.FromMilliseconds(1.0));
        }

        /// <summary>
        /// Sets the value of the Osc Time Tag.
        /// </summary>
        /// <param name="timeStamp">The time stamp to use to set the Osc Time Tag.</param>
        public void Set(DateTime timeStamp)
        {
            timeStamp = new DateTime(timeStamp.Ticks - (timeStamp.Ticks % TimeSpan.TicksPerMillisecond), timeStamp.Kind);

            if (!IsValidTime(timeStamp)) throw new Exception("Not a valid OSC Timetag.");
            mTimeStamp = timeStamp;
        }

        /// <summary>
        /// Returns a value indicating whether this instance is equal to a specified object.
        /// </summary>
        /// <param name="value">An object to compare to this instance.</param>
        /// <returns>true if value is an instance of System.DateTime and equals the value of this instance; otherwise, false.</returns>
        public override bool Equals(object value)
        {
            if (value == null)
            {
                return false;
            }

            OscTimeTag rhs = value as OscTimeTag;
            if (rhs == null)
            {
                return false;
            }

            return mTimeStamp.Equals(rhs.mTimeStamp);
        }

        /// <summary>
        /// Returns a value indicating whether this instance is equal to a specified OscTimeTag instance.
        /// </summary>
        /// <param name="value">An object to compare to this instance.</param>
        /// <returns>true if value is an instance of System.DateTime and equals the value of this instance; otherwise, false.</returns>
        public bool Equals(OscTimeTag value)
        {
            if ((object)value == null)
            {
                return false;
            }

            return mTimeStamp.Equals(value.mTimeStamp);
        }

        /// <summary>
        /// Returns the hash code for this instance.
        /// </summary>
        /// <returns>A 32-bit signed integer hash code.</returns>
        public override int GetHashCode()
        {
            return mTimeStamp.GetHashCode();
        }

        /// <summary>
        /// Converts the value of the current <see cref="OscTimeTag"/> object to its equivalent string representation.
        /// </summary>
        /// <returns>A string representation of the value of the current <see cref="OscTimeTag"/> object.</returns>
        public override string ToString()
        {
            return mTimeStamp.ToString();
        }

        private DateTime mTimeStamp;
    }

    internal class OSCReceiver
    {
        protected UdpClient udpClient;
        protected int localPort;

        public OSCReceiver(int localPort)
        {
            this.localPort = localPort;
            Connect();
        }

        public void Connect()
        {
            if (this.udpClient != null) Close();
            this.udpClient = new UdpClient(this.localPort);
        }

        public void Close()
        {
            if (this.udpClient != null) this.udpClient.Close();
            this.udpClient = null;
        }

        public OSCPacket Receive()
        {
            try
            {
                IPEndPoint ip = null;
                byte[] bytes = this.udpClient.Receive(ref ip);
                if (bytes != null && bytes.Length > 0)
                {
                    var p = OSCPacket.Unpack(bytes);
                    p.client_ip = ip;
                    return p;
                }

            }
            catch (ThreadAbortException)
            {
                // passthrough, usually happens on close
            }
            catch (Exception e)
            {
                Tabula.Log.Logger.DefaultLog.logException("OSCPacket.Receive()", e);
                return null;
            }

            return null;
        }
    }
}
