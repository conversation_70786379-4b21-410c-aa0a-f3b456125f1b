using Avalonia.Controls;
using Avalonia.Interactivity;
using PropertyChanged;
using System;
using System.Linq;
using System.Threading.Tasks;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;
using TheArtOfDev.HtmlRenderer.Avalonia;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class GameSettings : UserControl
	{
		private bool is_page_shown = false;

		public GameSettings()
		{
			InitializeComponent();
			
			/*
			html_panel.Container.ImageLoad += (s, e) =>
			{
				
			};
			*/
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			try
			{
				MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.GeneralSettingsEntity));
				MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.VisualSettingsEntity));

				// disables fields sync for rules on the server, so we can apply presets when wanted
				var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

				rules.sync_fields = false;

				is_page_shown = true;

				// Load html info
				LoadGameInfo();
			}
			catch(Exception ex)
			{
				SARGAME.App.logException("GameSettings.onLoaded()", ex);
			}

			base.OnLoaded(e);
		}

		protected override void OnUnloaded(RoutedEventArgs e)
		{
			is_page_shown = false;

			base.OnUnloaded(e);
		}

		async Task LoadGameInfo()
		{

			TheArtOfDev.HtmlRenderer.Core.Handlers.TabulaExtensions.BaseUri = MainWindowUI.Instance.RunningModule.GuideFolderPath;
			
			try
			{
				//var html = await SARGAME.DownloadText("https://sargame-cdn.tabulatouch.com/win_x64/packages/kinetikpack/guides/kinetikplayground.html");

				var html = File.ReadAllText(MainWindowUI.Instance.RunningModule.GuideHtmlPath);



				html_panel.Text = "";
				html_panel.Text = html;
			}
			catch
			{
				html_panel.Text = "<b>Error downloading Game Info</b>";
			}

			if (string.IsNullOrWhiteSpace(html_panel.GetHtml()))
			{
				html_panel.InvalidateMeasure();
				html_panel.InvalidateVisual();
			}
		}
		

		async void preset_selectionchanged(object sender, SelectionChangedEventArgs args)
		{
			var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

			var previous_preset = (string) (args.RemovedItems.Count > 0 ? args.RemovedItems[0] : null);
			var current_preset = (string) (args.AddedItems.Count > 0 ? args.AddedItems[0] : null);

			if (previous_preset!=null)
				await SavePreset(previous_preset);

			if (current_preset!=null)
				await LoadPreset(current_preset);
		}

		#region Preset Copy to Custom

		void bt_preset_copy_popup_Click(object sender, RoutedEventArgs args)
		{
			var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;
			rules.CustomPresetToOverwrite = rules.CustomPresets.FirstOrDefault();
			preset_copy_popup.IsOpen = true;
		}

		async void bt_preset_copy_Click(object sender, RoutedEventArgs args)
		{
			var rules = (EntityView) MainWindowUI.Instance.RulesSettingsEntity;

			// Copies the selected one to the customselected

			await CopyPreset(rules.SelectedPreset, rules.CustomPresetToOverwrite);
			
			rules.SelectedPreset = rules.CustomPresetToOverwrite;

			preset_copy_popup.IsOpen = false;
		}

		async void bt_preset_cancel_Click(object sender, RoutedEventArgs args)
		{
			preset_copy_popup.IsOpen = false;
		}

		#endregion

		// Loads a preset and its values, but doesn't actualize it
		async Task<bool> LoadPreset(string preset_name=null)
		{
			try
			{
				var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

				if (preset_name == null)
					preset_name = rules.SelectedPreset;

				if (preset_name == null)
					return false;

				// just to make sure
				rules.sync_fields = false;
				await Task.Delay(200);

				var presetview = rules.GetPresetView(preset_name);

				if (presetview == null)
					return false;

				foreach (var kvp in presetview.Model.fields)
				{
					var fname = kvp.Key;
					var fvalue = kvp.Value;

					rules.SetFieldValue(fname, fvalue);
				}

				rules.RefreshAllFields();
			}
			catch
			{
				return false;
			}

			return true;
		}

		// Saves but doesn't actualize
		async Task<bool> SavePreset(string preset_name=null)
		{
			try
			{
				var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

				if (preset_name == null)
					preset_name = rules.SelectedPreset;

				if (preset_name == null)
					return false;

				// just to make sure
				rules.sync_fields = false;
				await Task.Delay(200);

				var presetview = rules.GetPresetView(preset_name);

				if (presetview == null)
					return false;

				// TODO: avoid save if it's a read-only preset?
				if (presetview.is_readonly)
					return true;

				await presetview.fields_ClearAsync();

				foreach (var f in rules.fields)
				{
					dynamic dfield;

					try
					{
						dfield = f;
						var fname = dfield.Model.name;
						var fvalue = dfield.Model.GetValue();

						await presetview.fields_AddAsync(fname, fvalue);

					}
					catch { }
				}

				return true;
			}
			catch
			{
				return false;
			}
		}

		// Saves but doesn't actualize
		async Task<bool> CopyPreset(string copy_from, string copy_to)
		{
			try
			{
				var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

				if (copy_from == null || copy_to== null)
					return false;

				// just to make sure
				rules.sync_fields = false;
				await Task.Delay(200);

				var copy_from_view = rules.GetPresetView(copy_from);
				var copy_to_view = rules.GetPresetView(copy_to);

				if (copy_from_view == null || copy_to_view==null)
					return false;

				// avoid copy if it's not a read-only preset?
				if (copy_to_view.is_readonly)
					return true;

				await copy_to_view.fields_ClearAsync();

				foreach (var f in copy_from_view.fields)
				{
					try
					{
						var fname = f.Key;
						var fvalue = f.Value.__Value;

						await copy_to_view.fields_AddAsync(fname, fvalue);

					}
					catch { }
				}

				return true;
			}
			catch
			{
				return false;
			}
		}

		// enables rules sync and actualizes values 
		async Task<bool> SaveAndApplyPreset(string preset_name = null)
		{
			try
			{
				var rules = (EntityView)MainWindowUI.Instance.RulesSettingsEntity;

				if (preset_name == null)
					preset_name = rules.SelectedPreset;

				if (preset_name == null)
					return false;

				if (!await SavePreset(preset_name))
					return false;

				// TODO: reload?

				// Now this is really actualized
				rules.preset = preset_name;

				// turn on sync
				rules.sync_fields = true;
				await Task.Delay(200);

				foreach (var f in rules.fields)
				{
					dynamic dfield;

					try
					{
						dfield = f;

						dfield.value = dfield.Model.value;
					}
					catch { }
				}

				rules.sync_fields = false;
			}
			catch
			{
				return false;
			}

			return true;
		}

		/*
		async void bt_preset_load_Click(object sender, RoutedEventArgs args)
		{
			await LoadPreset();
		}

		async void bt_preset_save_Click(object sender, RoutedEventArgs args)
		{
			await SavePreset();
		}
		*/

		async void bt_preset_apply_Click(object sender, RoutedEventArgs args)
		{
			await SaveAndApplyPreset();
		}


		// meta action to call a method from an Entity ButtonFieldView
		async void bt_entity_field_button(object sender, RoutedEventArgs args)
		{
			ButtonFieldView field_view = (ButtonFieldView)(sender as Control).DataContext;
			EntityView entity_view = (EntityView)field_view.__Parent;

			// Action must be called with the know method CallMethod
			// using action or name depends if target is a GameEntity (handles it in OnFieldsUpdate) or a PMEntityWrapper (handles it as a GuidObjectCallable
			await entity_view.CallMethod(string.IsNullOrEmpty(field_view.action) ? field_view.name : field_view.action, null);		
		}

	}

	

}
