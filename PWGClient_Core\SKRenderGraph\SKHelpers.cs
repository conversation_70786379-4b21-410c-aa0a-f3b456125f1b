﻿using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Tabula.SKRenderGraph
{
	public static class SKHelpers
	{
		public enum Align
		{
			None = 0,

			Left,
			Top,
			Right,
			Bottom
		}

		public static SKRect FitRect(SKRect in_rect, SKRect out_rect, Align align= Align.None)
		{
			float inAspect = in_rect.Width / in_rect.Height;
			float outAspect = out_rect.Width / out_rect.Height;

			if (inAspect > outAspect)
			{
				// The input rect is wider than the output rect, so we need to adjust its width
				float newWidth = out_rect.Width;
				float newHeight = newWidth / inAspect;

				float offsetY = (out_rect.Height - newHeight) / 2;
				float offset_alignY = 0;

				switch (align)
				{
					case Align.Top:
						offset_alignY = -(out_rect.Height - newHeight)/2;
						break;

					case Align.Bottom:
						offset_alignY = (out_rect.Height - newHeight)/2;
						break;
				}

				var final_rect = new SKRect(out_rect.Left, out_rect.Top + offsetY, out_rect.Right, out_rect.Bottom - offsetY);
				final_rect.Offset(0, offset_alignY);
				return final_rect;
			}
			else
			{
				// The input rect is taller than the output rect, so we need to adjust its height
				float newHeight = out_rect.Height;
				float newWidth = newHeight * inAspect;
				float offsetX = (out_rect.Width - newWidth) / 2;
				float offset_alignX = 0;

				switch (align)
				{
					case Align.Left:
						offset_alignX = -(out_rect.Width - newWidth) / 2;
						break;

					case Align.Right:
						offset_alignX = (out_rect.Width - newWidth) / 2;
						break;
				}

				var final_rect = new SKRect(out_rect.Left + offsetX, out_rect.Top, out_rect.Right - offsetX, out_rect.Bottom);
				final_rect.Offset(offset_alignX, 0);
				return final_rect;
			}
		}


		public static bool IsInside(this SKRect rect, SKRect other)
		{
			// all the corners must be inside
			SKPoint[] points = new SKPoint[] { new SKPoint(rect.Left, rect.Top), new SKPoint(rect.Right, rect.Top), new SKPoint(rect.Right, rect.Bottom), new SKPoint(rect.Left, rect.Bottom) };
			for (int i=0; i<points.Length; i++)
				if (points[i].X < other.Left || points[i].X > other.Right || points[i].Y < other.Top || points[i].Y > other.Bottom)
					return false;

			return true;
		}
	}
}
