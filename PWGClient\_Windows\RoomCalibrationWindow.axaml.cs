using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using PropertyChanged;
using ReactiveUI;
using System.ComponentModel;
using System.Threading.Tasks;

namespace Tabula.PWGClient
{
	public partial class RoomCalibrationWindow : Window
	{
		public RoomCalibrationWindow()
		{
			InitializeComponent();
			/*
#if DEBUG
			this.AttachDevTools();
#endif
			*/
		}

		private void InitializeComponent()
		{
			AvaloniaXamlLoader.Load(this);
		}

		async void bt_calibrate(object sender, RoutedEventArgs args)
		{
			// Get calibration points and update model
			var image_calibration = this.FindControl<ImageCalibrationEditor>("image_calibration");

			var calib_points = image_calibration.GetCalibrationPoints();

			App.Client.View.Screen.CommitBegin();
			for (int i = 0; i < 4; i++)
			{
				App.Client.View.Screen.image_markers[i].position.x = calib_points[i].X;
				App.Client.View.Screen.image_markers[i].position.y = calib_points[i].Y;
			}
			App.Client.View.Screen.CommitEnd();

			await LevelEditor.Instance.RefreshCalibrationImage();

			Close();
		}

		#region INotifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
		{
			((IReactiveObject)this).RaisePropertyChanged(args);
		}

		protected void RaisePropertyChanged(string name)
		{
			((IReactiveObject)this).RaisePropertyChanged(name);
		}

		#endregion
	}
}
