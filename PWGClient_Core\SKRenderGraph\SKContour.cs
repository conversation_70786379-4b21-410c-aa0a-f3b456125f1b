﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

// Contours are stroke overlays not meant to control, but to higlight and select
// They are by default not drawable, it is parent's responsibility to draw them (helper DrawContour())

namespace Tabula.SKRenderGraph
{    
    public class SKContour : SKObject
    {
        public float Size = 30;       // constant size

        protected SKPath Path = new SKPath(); 
        protected SKPath PathCheck = null;   // calculated from path stroke to check for contains

        public SKContour(SKScene scene) : base(scene)
        {
            Layer = SKScene.LayerContours;

            Color = new SKColor(255, 255, 0, 70);
            ColorHover = new SKColor(255, 255, 0, 200);
            IsSelectable = false;

            // parent's responsibility to update and draw
            IsDrawnInScene = false;
            IsUpdatedInScene = false;

            Paint.StrokeCap = SKStrokeCap.Round;            
            Paint.IsStroke = true;            
        }

        public override bool Contains(SKPoint pos) => PathCheck != null ? PathCheck.Contains(pos.X, pos.Y) : false;

        public override SKRect GetBoundingBox() => SKRect.Empty;    // to disable computation in scene bbox

        public override void Update()
        {
            // specialized class must bild builds Path  depending on data from parent/model
            // This creates a "solid" path out of the parametric-stroke, so that we can use it to check for Contains() call
            // https://github.com/mono/SkiaSharp/issues/1033
            // ISSUE: it seems that is must be drawn to work! su use a transparent color...
            if (Path != null)
                PathCheck = Paint.GetFillPath(Path);
        }

        public override void Draw()
        {
            // constant size for stroke
            float stroke_size = Size / Scene.CanvasScale;

            Paint.StrokeWidth = stroke_size;

            Paint.Color = IsMouseOver ? ColorHover : Color;

            Canvas.DrawPath(Path, Paint);
        }
    }
    
}
