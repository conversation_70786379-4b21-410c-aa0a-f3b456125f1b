<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.DashboardPage">
	<Border Classes="Card" Margin="7,7,-5,8">
		<TabControl Grid.Row="1" Grid.Column="1"
				Focusable="False" KeyboardNavigation.TabNavigation="None"
				SelectionChanged="dashboard_tabcontrol_selectionchanged"
			TabStripPlacement="Right">
			
			<TabItem Name="tab_games" x:Name="tab_games" Header="Apps" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None">

				<Border Classes="Card">
					<Grid RowDefinitions="Auto,*">

						<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto">
							<TextBlock Grid.Row="0" Text="Applications" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
							<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
							<TextBlock Grid.Row="2" Margin="5,10,0,10">
								Welcome to <Bold>S-ARGAME</Bold>							</TextBlock>
						</Grid>
						
						<ScrollViewer Grid.Row="1" Margin="0,10,0,0">
							<ItemsControl ItemsSource="{Binding SARGAME.Modules}">

							<ItemsControl.ItemsPanel>
								<ItemsPanelTemplate>
									<WrapPanel/>
								</ItemsPanelTemplate>
							</ItemsControl.ItemsPanel>
						
							<ItemsControl.ItemTemplate>
								<DataTemplate DataType="{x:Type sargame:PackageModule}">
									<Border Classes="Card Hoverable" Margin="10" >
										<Grid RowDefinitions="Auto,Auto" ClipToBounds="True">
											<Button  Grid.Row="0" BorderThickness="0" BorderBrush="Transparent" Width="256" Height="144" Margin="10" Padding="0" Click="bt_start_Click">
												<Grid>
													<Image  Source="{Binding ThumbnailPath, Converter={StaticResource BitmapValueConverter}}" RenderOptions.BitmapInterpolationMode="HighQuality"/>
													<Image Source="/Images/overlay_new.png" RenderOptions.BitmapInterpolationMode="HighQuality" IsVisible="{Binding IsNew}"/>
													<Image Source="/Images/overlay_try.png" RenderOptions.BitmapInterpolationMode="HighQuality" IsVisible="{Binding !IsLicensed}"/>
												</Grid>
											</Button>
											<StackPanel Grid.Row="1" Margin="10" HorizontalAlignment="Center" Orientation="Horizontal">

												<!-- 
												<Label Grid.Column="0" Content="{Binding name}" VerticalAlignment="Center" Margin="10"/>
												-->
												<Button Classes="Basic" VerticalAlignment="Center" ToolTip.Tip="Module Info" Click="bt_info_Click" IsVisible="{Binding HasGuide}">
													<materialIcons:MaterialIcon Kind="Info" Margin="0"/>
												</Button>

												<Button VerticalAlignment="Center" ToolTip.Tip="Start the module" Click="bt_start_Click" IsVisible="{Binding IsInstalled}">
													<materialIcons:MaterialIcon Kind="Play" Margin="0"/>
												</Button>
											
												<Button VerticalAlignment="Center" Classes="Primary" ToolTip.Tip="Install the module" Click="bt_install_Click" IsVisible="{Binding !IsInstalled}">
													<materialIcons:MaterialIcon Kind="CloudDownload" Margin="0"/>
												</Button>
										
											</StackPanel>
										

										</Grid>
									</Border>
								</DataTemplate>
							</ItemsControl.ItemTemplate>
					
							</ItemsControl>
						</ScrollViewer>
					</Grid>
				</Border>
			</TabItem>
			
			<TabItem Name="tab_store" x:Name="tab_store" Header="Packages" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<local:StorePage/>
			</TabItem>
			<TabItem Name="tab_tutorials" x:Name="tab_tutorials" Header="Tutorials" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<local:TutorialsPage/>
			</TabItem>
			<TabItem Name="tab_tools" x:Name="tab_tools" Header="Tools" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<local:ToolsPage/>
			</TabItem>
			<TabItem Name="tab_support" x:Name="tab_support" Header="Support" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<local:SupportPage/>
			</TabItem>
			<TabItem Name="tab_license" x:Name="tab_license" Header="License" HorizontalContentAlignment="Left" Margin="10" Focusable="False" KeyboardNavigation.TabNavigation="None" Background="Black">
				<local:LicensePage/>
			</TabItem>

		</TabControl>
	</Border>

</UserControl>
