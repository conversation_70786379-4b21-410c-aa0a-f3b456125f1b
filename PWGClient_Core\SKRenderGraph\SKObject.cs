﻿using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

// Avalonia dependency for the onKey routing
using Avalonia.Input;
using Avalonia.Media;

// Basic renderable

namespace Tabula.SKRenderGraph
{
    public abstract class SKObject
    {
        // Scene is set from root container when adding the object to the list
        public SKScene Scene;

        public SKCanvas Canvas => Scene.Canvas;

        public SKObject Parent; // if defined this object is a logical/hierarchical child, and must be removed when parent is destroyed

        private string _name;
        public string Name
        {
            get
            {
                if (!string.IsNullOrEmpty(_name))
                    return _name;
                else if (Scene != null)
                    return $"{this.ToString()}[{Scene.Objects.IndexOf(this)}]";
                else
                    return $"{this.ToString()} <no_scene>";
            }

            set => _name = value;
        }

		private int _layer = 0;
		public int Layer
        {
            get => _layer;

            set
            {
				_layer = value;
				Scene?.ReorderObjects();
			}
        }

        private int _order = 0;
        public virtual int     Order
        {
            get => _order;
            set
            {
                _order = value;
                Scene?.ReorderObjects();
            }
        }

        private bool _is_enabled = true;
        public bool IsEnabled   // update and draw
        {
            get => _is_enabled;

            set
            {
                _is_enabled = value;
                if (!_is_enabled)
                    OnDisable();
                else
                    OnEnable();
            }
        }


		public bool IsUpdatedInScene = true;    // the objects' Update() is called in scene, if you need special ordering disable and call somewhere elsew
        public bool IsDrawnInScene = true;      // will be auto drawn in scene (can be nevertheless drawn manually)

        public bool IsHitTestVisible = true;    // the object can be dragged / selected

        public bool IsSelectable = true;        // can have the status of "selected"
        public bool IsDraggable = true;         // can be dragged
        public bool IsSelectableWithLeftClick = false; // can be selected ONLY by clicking

		public SKColor Color = new SKColor(0, 0, 0);
        public SKColor ColorHover = new SKColor(0, 0, 255);
        public SKColor ColorDragging = new SKColor(255, 255, 255);


        protected SKPoint drag_anchor = SKPoint.Empty;     

        // NOTE: do not share!
        public SKPaint Paint = new SKPaint() { Color = new SKColor(0, 0, 255), IsAntialias = true };

        // bounds for hit testing with mouse, if empty input is disabled
        public virtual bool Contains(SKPoint pos) => false;


        public bool IsMouseOver => Scene!=null ? Scene.IsMouseOver(this) : false;

        public bool IsLeftMousePressedOver => Scene != null ? Scene.IsMousePressedOver(SKScene.MouseLeftButton, this) : false;
		public bool IsMiddleMousePressedOver => Scene != null ? Scene.IsMousePressedOver(SKScene.MouseMiddleButton, this) : false;
		public bool IsRightMousePressedOver => Scene != null ? Scene.IsMousePressedOver(SKScene.MouseRightButton, this) : false;

        public bool IsLeftMouseStartDragging => !Scene.IsDragging() && IsLeftMousePressedOver;
		public bool IsMiddleMouseStartDragging => !Scene.IsDragging() && IsMiddleMousePressedOver;
		public bool IsRightMouseStartDragging => !Scene.IsDragging() && IsRightMousePressedOver;

		public bool IsDragging => Scene != null ? Scene.IsDragging(this) : false;

        public bool IsSelected => Scene != null ? Scene.IsSelected(this) : false;

        public bool IsMouseLeftPressed => Scene != null ? Scene.MouseLeftPressed : false;
        public bool IsMouseMiddlePressed => Scene != null ? Scene.MouseMiddlePressed : false;
        public bool IsMouseRightPressed => Scene != null ? Scene.MouseRightPressed : false;

        public bool IsMouseLeftClicked => Scene != null ? Scene.IsMouseLeftClicked(this) : false;
        public bool IsMouseMiddleClicked => Scene != null ? Scene.IsMouseMiddleClicked(this) : false;
        public bool IsMouseRightClicked => Scene != null ? Scene.IsMouseRightClicked(this) : false;

        // Events 

        public Func<SKObject, Menu>         onMenu;

        public Action                       onSelected, onUnSelected;

        public Action<bool, KeyEventArgs>   onKey; //<is_down, keyargs>

        // Objects must specify a belonging scene at creation time
        public SKObject(SKScene scene)
        {
            Scene = scene;
        }

        public Action<SKObject, SKScene> onAddToScene;
        public virtual void AddToScene(SKScene scene)
        {
            onAddToScene?.Invoke(this, scene);
        }

        public Action<SKObject, SKScene> onRemoveFromScene;
        public virtual void RemoveFromScene(SKScene scene)
        {
            onRemoveFromScene?.Invoke(this, scene);
        }

		public Action<SKObject> onEnable;
		public virtual void OnEnable()
        {
			onEnable?.Invoke(this);
		}

		public Action<SKObject> onDisable;
		public virtual void OnDisable()
        {
			onDisable?.Invoke(this);
		}

        // this method is called ALWAYS also for not enabled ones
        public Action<SKObject> onBeforeUpdate;
        public virtual void BeforeUpdate()
        {
            onBeforeUpdate?.Invoke(this);
        }

        // Called only if enabled
        public Action<SKObject> onUpdate;
        public Action<SKObject, int> OnMouseClicked;
        public virtual void Update()
        {
			// forward click events
			if (IsMouseLeftClicked)
				OnMouseClicked?.Invoke(this, SKScene.MouseLeftButton);
			if (IsMouseMiddleClicked)
				OnMouseClicked?.Invoke(this, SKScene.MouseMiddleButton);
			if (IsMouseRightClicked)
                OnMouseClicked?.Invoke(this, SKScene.MouseRightButton);

		    // updates state, processes input
		    onUpdate?.Invoke(this);
        }

        public Action<SKObject> onDraw;
        public virtual void Draw()
        {
            // Implement drawing depending on order/layer
            onDraw?.Invoke(this);
        }

        public Action<SKObject> onLateDraw;
        public virtual void LateDraw()
        {
            // drawing called after all ordered drawing
            onLateDraw?.Invoke(this);

			// Bounding box
			/*
            var bbox = GetBoundingBox();
			Canvas.DrawRect(bbox, new SKPaint()
            {
                Color = SKColors.Magenta,

				Style = SKPaintStyle.Stroke,
			});
            */
		}

		#region Selection

        public void Select()
        {
            if (Scene == null)
                return;

            if (IsSelectable)
                Scene.SelectObject(this);
        }

		#endregion


		#region Position and Size

		public virtual void SetPosition(SKPoint pos)
        {
            // implemented in subclasses only            
            if (IsDrawnInScene)
                Scene.Refresh();
        }

        public virtual SKPoint GetPosition() => SKPoint.Empty;

        public virtual void SetSize(SKSize size)
        {
            // implemented in subclasses only
            if (IsDrawnInScene)
                Scene.Refresh();
        }

        public virtual SKSize GetSize() => SKSize.Empty;

        public virtual SKRect GetBoundingBox() => SKRect.Empty;
        /*
		{

            // standard implementation relies on position and size
            var pos = GetPosition();
            var size = GetSize();
            return new SKRect(pos.X - size.Width/2, pos.Y-size.Height/2, pos.X+size.Width/2, pos.Y+size.Height/2);
		}*/

        #endregion

        // moves by offset, calls handler
        public Action<SKObject, SKPoint> onMove;
        public virtual void Move(SKPoint pos_rel)
        {
            if (IsDrawnInScene) 
                Scene.Refresh();
            onMove?.Invoke(this, pos_rel);
        }

        public Action<SKObject> onDragStart;
        public void DragStart()
        {
            if (drag_anchor.IsEmpty && !Scene.IsDraggingObject)
            {
                if (Scene.DragObject(this))
                {
                    drag_anchor = Scene.MousePosition;
                    onDragStart?.Invoke(this);
                    // TODO: global scene dragstart?
                    if (IsDrawnInScene) 
                        Scene.Refresh();
                }
            }            
        }

        public Action<SKObject> onDragMove;
        public void DragMove()
        {
            Move(Scene.MousePosition - drag_anchor);
            drag_anchor = Scene.MousePosition;
            onDragMove?.Invoke(this);
            // TODO: global scene dragmove?
            if (IsDrawnInScene) 
                Scene.Refresh();
        }

        public Action<SKObject> onDragEnd;
        public void DragEnd()
        {
            Move(Scene.MousePosition - drag_anchor);
            drag_anchor = SKPoint.Empty;
            Scene.DragObject(null);
            onDragEnd?.Invoke(this);
            // TODO: global scene dragend?
            if (IsDrawnInScene) 
                Scene.Refresh();
        }

        // Actively destroys the object
        public virtual void Destroy()
		{
            // TODO: Scene.remove(this); ??
		}

        public virtual Menu GetMenu() => onMenu?.Invoke(this);

        // Control points: special interactive objects used to modify the curve

        public List<SKControlPoint> ControlPoints = new List<SKControlPoint>();

        // Contours: special non-interactive objects used to identify contour and enable menu actions

        public List<SKObject> Contours = new List<SKObject>();

        // Invoked when adding to Scene

        // deprecate
        public virtual bool CreateControlPoints() => false;

        // deprecate
        public virtual bool CreateContours() => false;

        // deprecate
        public virtual void DrawContours()
        {
            for (int i = 0; i < Contours.Count; i++)
                Contours[i].Draw();
        }

        // deprecate
        public virtual void UpdateContours()
        {
            for (int i = 0; i < Contours.Count; i++)
                Contours[i].Update();
        }

        // ControlPoints -> data
        public virtual void SyncFromControlPoints()
        { }

        // data -> ControlPoints
        public virtual void SyncToControlPoints()
        { }

        // generic function to get childer of type
        public List<T> GetChildren<T>()
        {
            return (from o in Scene.Objects where (o.Parent == this && o is T) select o).Cast<T>().ToList();
        }

        // to deprecate
        public virtual void ToggleControlPoints(bool enabled)
        {
            foreach (var cp in ControlPoints)
                cp.IsEnabled = enabled;
        }

        // to deprecate
        public virtual void ToggleControlPointsDraw(bool draw)
        {
            foreach (var cp in ControlPoints)
                cp.IsDrawnInScene = draw;
        }

        // to deprecate
        public virtual void DeleteControlPoints()
        {
            foreach (var cp in ControlPoints)
                Scene.Remove(cp);
        }

		#region Debug

        public void DrawBoundingBox()
        {
            Canvas.DrawRect(GetBoundingBox(), new SKPaint() { Color = SKColors.Magenta.WithAlpha(80) });
		}

		#endregion


		#region Menu

		public class Menu
        {
            public class MenuItem
            {
                public string Text;
                public Action Action;
            }

            public List<MenuItem> Items;
        }

        #endregion
    }


}
