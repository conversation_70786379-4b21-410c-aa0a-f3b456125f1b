﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net7.0-android</TargetFramework>
    <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
    <Nullable>enable</Nullable>
    <ApplicationId>com.CompanyName.AvaloniaXPlat2</ApplicationId>
    <ApplicationVersion>1</ApplicationVersion>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <AndroidEnableProfiledAot>False</AndroidEnableProfiledAot>
    <Platforms>AnyCPU;ARM64</Platforms>
    <Configurations>Debug;Release;Beta Release</Configurations>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants></DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <DefineConstants />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants></DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|AnyCPU'">
    <DefineConstants />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <DefineConstants />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|ARM64'">
    <DefineConstants />
  </PropertyGroup>

  <ItemGroup>
    <AndroidResource Include="Icon.png">
      <Link>Resources\drawable\Icon.png</Link>
    </AndroidResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia.Android" Version="11.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PWGClient\PWGClient.csproj" />
  </ItemGroup>
</Project>
