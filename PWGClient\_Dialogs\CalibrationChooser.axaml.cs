using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Interactivity;
using PropertyChanged;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.Unity;

namespace Tabula.PWGClient
{
	// NOTE: now not used
	[DoNotNotify]
	public partial class CalibrationChooser : UserControl
	{
		public string dialog_identifier = "calibrationchooser_dialog";

		public enum CalibrationMode
		{
			Manual,
			Automatic
		}

		public CalibrationChooser()
		{
			InitializeComponent();
		}		
		
		public static async Task<CalibrationMode> Show()
		{
			var ret = await DialogHostAvalonia.DialogHost.Show( new CalibrationChooser());

			return (CalibrationMode) ret;
		}

		protected async override void OnLoaded(RoutedEventArgs e)
		{
			base.OnLoaded(e);			
		}

	
		void bt_manual_Click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", CalibrationMode.Manual);
		}

		void bt_automatic_Click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", CalibrationMode.Automatic);
		}

	}
}
