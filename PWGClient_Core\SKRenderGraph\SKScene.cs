﻿using Avalonia.Controls;
using Avalonia.Input;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tabula.SKRenderGraph.Tween;

namespace Tabula.SKRenderGraph
{
    public class SKScene
    {
        public static SKScene Instance;

        // must be created in order to have an offscreen surface buffer
        private SKSurface _surface;
        public SKSurface Surface
        {
            get => _surface;

            set
            {
                // Retain current matrix
                if (_surface != null)
                {
                    var m = _surface.Canvas.TotalMatrix;

                    // Dispose old
                    _surface.Dispose();

                    _surface = value;
                    _surface.Canvas.SetMatrix(m);
                }
                else
                    _surface = value;
            }
        }

        private SKCanvas _canvas;   // can be set externally (Avalonia)
        public SKCanvas Canvas
        {
            get
            {
                if (_canvas != null)
                    return _canvas;
                else
                    return Surface?.Canvas;

            }

            set => _canvas = value;
        }

        public float CanvasScale => Canvas != null ? Canvas.TotalMatrix.ScaleX : 1;


        public ulong Frame { get; private set; }

        // Input
        public const int    MouseLeftButton = 0;
        public const int    MouseMiddleButton = 1;
        public const int    MouseRightButton = 2;


        public Action       OnGetMouseState;

        public SKPoint      NativeMousePosition = SKPoint.Empty,    // always the one returned
							MousePosition = SKPoint.Empty,          // the used one, can keep last position for consistency
                            MousePositionUnscaled = SKPoint.Empty,
                            LastValidMousePosition = SKPoint.Empty;

        //public bool       IsMouseInScene => MousePositionUnscaled.X != -1 && MousePositionUnscaled.Y != -1;
        public bool         IsMouseInScene => NativeMousePosition.X!=-1 && NativeMousePosition.Y!=-1;

        public bool[]       MousePressed = new bool[3] { false, false, false };         // left, middle, right
        public bool[]       LastMousePressed = new bool[3] { false, false, false }; 

		// older
		public bool         MouseLeftPressed => MousePressed[MouseLeftButton];
        public bool         MouseMiddlePressed => MousePressed[MouseMiddleButton];
        public bool         MouseRightPressed => MousePressed[MouseRightButton];

        public bool         KeyboardPanPressed = false;

        // rule for panning scene / background
        public bool         IsPanPressed =>
                                (MousePressed[MouseLeftButton] && KeyboardPanPressed)||
                                 MousePressed[MouseRightButton] ||
                                 MousePressed[MouseMiddleButton];

        public Action<bool, KeyEventArgs> OnKey; //<is_down, keyargs>

        // Scene zoom & pan

        public SKSize CanvasSize = SKSize.Empty;

        public float DeltaScale = 1f;
        //public SKPoint CanvasTranslation = new SKPoint(0, 0);

        public static double ClickTime = 250;

        // Click detection
        public class Click
        {
            public enum  ClickStatus { None, Pressed, Released }

            public DateTime     time = DateTime.MinValue;
            public ClickStatus  status = ClickStatus.None;
            public SKObject     obj;

            public bool CheckClick(bool button_pressed)
            {
                if (button_pressed)
                {
                    if (status == ClickStatus.Pressed)
                    {
                        // was already pressed, do not update time
                    }
                    else
                    {
                        // new press, update time
                        status = ClickStatus.Pressed;
                        time = DateTime.Now;
                    }
                }
                else
                {
                    if (status == ClickStatus.Pressed)
                    {
                        // released after press, check time and signal click
                        if ((DateTime.Now - time).TotalMilliseconds < ClickTime)
                        {
                            // there is a click!
                            time = DateTime.MinValue;
                            status = ClickStatus.None;

                            return true;
                        }
                        else
                        {
                            // no click
                            time = DateTime.MinValue;
                            status = ClickStatus.None;
                        }
                    }
                    else
                    {
                        time = DateTime.MinValue;
                        status = ClickStatus.None;
                    }
                }

                return false;
            }

            public bool CheckClick(bool button_pressed, SKObject target_obj)
            {
                if (button_pressed)  
                {
                    if (obj == target_obj)
                    {
                        if (status == ClickStatus.Pressed)
                        {
                            // was already pressed, do not update time
                        }
                        else
                        {
                            // new press, update time
                            status = ClickStatus.Pressed;
                            time = DateTime.Now;
                        }
                    }                        
                    else
                    {
                        // new candidate
                        obj = target_obj;
                        status = ClickStatus.Pressed;
                        time = DateTime.Now;
                    }
                }
                else
                {
                    if (obj == target_obj)
                    {
                        if (status == ClickStatus.Pressed)
                        {
                            // released after press, check time and signal click
                            if ((DateTime.Now - time).TotalMilliseconds < ClickTime)
                            {
                                // there is a click!
                                time = DateTime.MinValue;
                                status = ClickStatus.None;
                                obj = null;

                                return true;
                            }
                            else
                            {
                                // no click
                                time = DateTime.MinValue;
                                status = ClickStatus.None;
                                obj = null;
                            }
                        }
                        else
                        {
                            time = DateTime.MinValue;
                            status = ClickStatus.None;
                            obj = null;
                        }
                    }
                }

                return false;
            }




        }

        public Click[] MouseClick = new Click[] { new Click(), new Click(), new Click() };

        public SKObject ObjectClicked = null;   // valid for a single frame
        public int      ObjectClickedButton = -1;

        // Scene properties and look

        public SKColor BackgroundColor = new SKColor(130, 130, 130);

		// last mouse event
		public int                  MouseEventButton;
        public Click.ClickStatus    MouseEventStatus = Click.ClickStatus.None;
		public SKPoint              MouseEventPosition = default;


        // Dragging

        public bool IsDraggingScene { get; private set; } = false;
        public bool IsDraggingObject => ObjectDragged != null;
        private SKPoint mouse_drag = new SKPoint(0, 0);

        // Scene exclusive click capture (to create objects etc..)
        public bool IsWaitingClick { get; private set; } = false;
        private Action<int,SKPoint> OnClickCaptured;    // button,point


        // Objects to render, ordered back to front

        public List<SKObject> Objects = new List<SKObject>();

        public const int LayerBackground = -1000;
		public const int LayerStructures = LayerBackground + 10;
		public const int LayerLockedObjects = LayerStructures - 1;
		public const int LayerControlPoints = 1000;
        public const int LayerContours = 500;

        // this is the first object evaluating IsMouseOver true, decided frame-by-frame
        private SKObject _last_object_under_mouse = null;
        public SKObject ObjectUnderMouse { get; protected set; } = null;
        public SKObject ObjectDragged { get; protected set; } = null;
        public SKObject ObjectSelected { get; protected set; } = null;   // last touched object

        // Animations
        private List<ITweener> Animations = new List<ITweener>();

        private DateTime AnimationStartTime = DateTime.MinValue,
                         LastTime = DateTime.MinValue;       

        // Standard Colors
        public static SKColor ColorWhite = new SKColor(255, 255, 255);
        public static SKColor ColorBlack = new SKColor(0, 0, 0);

        // Scene drawing events
        public Action BeforeDraw, AfterDraw;

        // Object selection events
        public Action<SKObject> OnObjectSelected, OnObjectUnSelected;

        // Debug 
        public static bool Debug = false;

        public virtual void GetMouseState()
        {
            OnGetMouseState?.Invoke();
        }

        public SKScene(SKCanvas _canvas = null, SKSurface _surface = null)
        {
            Instance = this;
            this._canvas = _canvas;
            this._surface = _surface;
        }

        public bool CreateSurfaceIfNeeded(int width, int height, bool force = false)
        {
            if (Surface != null && !force)
            {
                if (CanvasSize.Width == width && CanvasSize.Height == height)
                    return false;
            }

            // setter will retain matrix and dispose old

            Surface = SKSurface.Create(new SKImageInfo()
            {
                Width = width,
                Height = height,
                AlphaType = SKAlphaType.Opaque,
                ColorType = SKColorType.Bgra8888
            });

            CanvasSize = new SKSize(width, height);

            return true;
        }

        // Locks the usual routing of events to get a single left/right mouse press on the scene, reporting the point
        public void BeginClickCapture(Action<int, SKPoint> onClickCaptured)
		{
            lock (this)
            {
                IsWaitingClick = true;
                OnClickCaptured = onClickCaptured;
            }
		}

        public void EndClickCapture()
		{
            lock (this)
			{
                IsWaitingClick = false;
                OnClickCaptured = null;
            }
		}

        public void SetLastInLayer(SKObject obj)
        {
            // get all other objects in this layer
            var sorted_layer = (from o in Objects where o.Layer == obj.Layer select o).ToList();
            if (sorted_layer.Count > 0)
            {
                sorted_layer = sorted_layer.OrderBy(o => o.Order).ToList();
                int least_order = sorted_layer[0].Order;

                obj.Order = least_order - 1; // will retrigger reorder
            }
        }

        public void ReorderObjects()
        {
            lock (this)
            {
                // order per-layer

                var layers = (from o in Objects orderby o.Layer select o.Layer).Distinct();

                var ordered_objects = new List<SKObject>();
                foreach (var l in layers)
                {
                    var sorted_layer = (from o in Objects where o.Layer == l select o).ToList();
                    sorted_layer.Sort((p, q) => p.Order.CompareTo(q.Order));

                    ordered_objects.AddRange(sorted_layer);
                }

                Objects = ordered_objects;

                // Objects.Sort((p, q) => p.Order.CompareTo(q.Order));
            }

            Refresh();
        }

        public void Add(SKObject obj, SKObject parent_object = null)
        {
            lock (this)
            {
                if (parent_object != null)
                    obj.Parent = parent_object;

                if (Objects.Contains(obj))
                    return;

                obj.Scene = this;
                Objects.Add(obj);

                // hook
                obj.AddToScene(this);

                // DEPRECATE!
                // The interactive overlay points to control                
                if (obj.CreateControlPoints())
                    foreach (var cp in obj.ControlPoints)
                        Add(cp);

                // DEPRECATE!
                // overlay objects used for collision
                if (obj.CreateContours())
                    foreach (var ct in obj.Contours)
                        Add(ct);

                ReorderObjects();

                Refresh();
            }
        }

        public void Remove(SKObject obj)
        {
            lock (this)
            {                
                if (!Objects.Contains(obj))
                    return;

                Objects.Remove(obj);

                // hook
                obj.RemoveFromScene(this);

                // find child objects and remove them too
                var children = (from o in Objects where o.Parent == obj select o).ToList();
                foreach (var c in children)
                    Remove(c);


                // Also removes dependend control points and contours

                // DEPRECATE!
                foreach (var cp in obj.ControlPoints)
                    Remove(cp);

                // DEPRECATE!
                foreach (var ct in obj.Contours)
                    Remove(ct);

                ReorderObjects();

                Refresh();
            }
        }

        public bool IsMouseOver(SKObject obj) => obj.IsHitTestVisible && ObjectUnderMouse == obj;

        public bool IsMousePressedOver(int mouse_button, SKObject obj)
        {
            if (!obj.IsHitTestVisible)
                return false;

            if (MousePressed[mouse_button])
                return (obj.Contains(MouseEventPosition));
            else
                return false;
        }

        public bool IsDragging() => ObjectDragged != null;

        public bool IsDragging(SKObject obj) => obj.IsHitTestVisible && ObjectDragged == obj;

        public bool IsSelected(SKObject obj) => ObjectSelected == obj;

        public bool IsMouseLeftClicked(SKObject obj) => obj.IsHitTestVisible && ObjectClicked == obj && ObjectClickedButton == MouseLeftButton;
        public bool IsMouseMiddleClicked(SKObject obj) => obj.IsHitTestVisible && ObjectClicked == obj && ObjectClickedButton == MouseMiddleButton;
        public bool IsMouseRightClicked(SKObject obj) => obj.IsHitTestVisible && ObjectClicked == obj && ObjectClickedButton == MouseRightButton;

        private bool CheckIsMouseOver(SKObject obj)
        {
            // Already found the top object in this frame? skip it
            if (ObjectUnderMouse != null)
                return false;

            if (obj.Contains(MousePosition))
            {
                ObjectUnderMouse = obj;
				//_last_object_under_mouse = ObjectUnderMouse;

				if (Debug /*&& ObjectUnderMouse!=_last_object_under_mouse*/)
                    System.Diagnostics.Debug.WriteLine($"ObjectUnderMouse = {obj.Name}");

                return true;
            }
            else
                return false;
        }


        public virtual void Update()
        {
            lock (this)
            {
                // First pass Front-to-back (list backward) to determine first mouse hit
                ObjectUnderMouse = null;
                ObjectClicked = null;

                GetMouseState();

                // Avoid invalid positions (especially during DragEnd)
                MousePosition = NativeMousePosition;

				if (MousePosition.X == -1 && MousePosition.Y == -1)
                    MousePosition = LastValidMousePosition;
                else
                    LastValidMousePosition = MousePosition;

                MousePositionUnscaled = MousePosition;

                //System.Diagnostics.Debug.WriteLine($"MousePosition x:{MousePosition.X} y:{MousePosition.Y}");


                // Mouse position used for picking (effectively inverse transform)
                // TODO: use it inside checks?
                // NOTE: Disabled for Avalonia

                if (Canvas != null)
                {
                    MousePosition.X -= Canvas.TotalMatrix.TransX;
                    MousePosition.Y -= Canvas.TotalMatrix.TransY;
                    MousePosition.X /= Canvas.TotalMatrix.ScaleX;
                    MousePosition.Y /= Canvas.TotalMatrix.ScaleY;
                }

				DetectMouseEvents(MouseLeftButton);
				DetectMouseEvents(MouseRightButton);

				LastMousePressed = MousePressed.ToArray();

				// NOTE: Dragging is checked/started on the object's Update


				// Find the hover object, if not dragging
				if (!IsDraggingObject && !IsWaitingClick)
                    for (int i = Objects.Count - 1; i >= 0; i--)
                    {
                        if (Objects[i].IsEnabled && Objects[i].IsHitTestVisible)
                            if (CheckIsMouseOver(Objects[i]))
                                break;
                    }

                // Process animations
                double delta_time;
                if (AnimationStartTime == DateTime.MinValue)
                {
                    AnimationStartTime = DateTime.Now;
                    LastTime = DateTime.Now;
                }

                delta_time = (DateTime.Now - LastTime).TotalSeconds;

                for (int i = 0; i < Animations.Count; i++)
                {
                    Animations[i].Update((float)delta_time);
                }

                LastTime = DateTime.Now;

                if (ObjectDragged != null)
                {
                    // special, this reference could be not valid anymore!
                    if (Objects.Contains(ObjectDragged))
                    {
                        // DragStart() is issued by object in its update

                        if (MousePressed[MouseLeftButton])
                            ObjectDragged.DragMove();
                        else
                        {
                            ObjectDragged.DragEnd();
							
							ObjectDragged = null;

							if (Debug)
								System.Diagnostics.Debug.WriteLine($"ObjectDragged.DragEnd()");
						}
                    }
                    else
                    {
                        ObjectDragged = null;
                    }
                }
                else
                {
                    if (Objects.Contains(ObjectUnderMouse))
                    {
                        // detect a click for this frame
                        if (ObjectUnderMouse != null && !IsWaitingClick)
                        {
                            if (MouseClick[MouseLeftButton].CheckClick(MousePressed[MouseLeftButton], ObjectUnderMouse))
                            {
                                ObjectClicked = ObjectUnderMouse;
                                ObjectClickedButton = MouseLeftButton;
                            }
                            else if (MouseClick[MouseMiddleButton].CheckClick(MousePressed[MouseMiddleButton], ObjectUnderMouse))
                            {
                                ObjectClicked = ObjectUnderMouse;
                                ObjectClickedButton = MouseMiddleButton;
                            }
                            else if (MouseClick[MouseRightButton].CheckClick(MousePressed[MouseRightButton], ObjectUnderMouse))
                            {
                                ObjectClicked = ObjectUnderMouse;
                                ObjectClickedButton = MouseRightButton;
                            }
                        }
                    }
                    else
                    {
                        ObjectUnderMouse = null;
                    }

                    if (IsWaitingClick)
                    {
                        // Capture a single click (left or right)

                        if (MouseClick[MouseLeftButton].CheckClick(MousePressed[MouseLeftButton]))
                        {
                            OnClickCaptured?.Invoke(MouseLeftButton, MousePosition);
                        }
                        else if (MouseClick[MouseRightButton].CheckClick(MousePressed[MouseRightButton]))
                        {
                            OnClickCaptured?.Invoke(MouseRightButton, MousePosition);
                        }
                    }


                    // If the mouse is not captured check the scene pan
                    if (IsPanPressed)
                    {
                        if (!IsDraggingScene)
                        {
                            mouse_drag = MousePositionUnscaled;
                            IsDraggingScene = true;
                        }

                        var translation = MousePositionUnscaled - mouse_drag;

                        Translate(translation);
                        mouse_drag = MousePositionUnscaled;
                    }
                    else
                        IsDraggingScene = false;
                }

                // Processes input for all
                UpdateObjects();
            }
        }

        protected virtual void UpdateObjects()
        {
            // NOTE: update order is reversed, higher object first (so that dragging works)

            for (int i = Objects.Count-1; i >= 0; i--)
            {
                Objects[i].BeforeUpdate();

                if (Objects[i].IsEnabled && Objects[i].IsUpdatedInScene)
                {                   
                    Objects[i].Update();
                }
            }
        }

        // Determines if a draw is needed, also draws a last-time to reset visual states
        private bool _force_redraw = false;
        public void Refresh() => _force_redraw = true;


        private bool _last_should_draw = false;
        public bool IsDrawNeeded
        {
            get
            {
                bool should_draw = _force_redraw || (ObjectUnderMouse != null || ObjectDragged != null || IsDraggingScene || DeltaScale != 1);

                if (!should_draw && _last_should_draw)
                {
                    _force_redraw = false;
                    _last_should_draw = false;
                    return true;
                }

                _force_redraw = false;
                _last_should_draw = should_draw;

                return should_draw;
            }
        }
        
        public virtual void Draw()
        {
            lock (this)
            {
                Canvas.Clear(BackgroundColor);

                // Custom Drawing
                BeforeDraw?.Invoke();

                // Scaling (mouse only)
                if (DeltaScale != 1f)
                {
                    ScaleAroundPoint(DeltaScale, MousePositionUnscaled);
                }
                DeltaScale = 1;

                // Ordered drawing

                for (int i = 0; i < Objects.Count; i++)
                {
                    if (Debug)
                        Objects[i].DrawBoundingBox();

                    if (Objects[i].IsEnabled && Objects[i].IsDrawnInScene)
                        Objects[i].Draw();
                }

                // Late Draw
                for (int i = 0; i < Objects.Count; i++)
                {
                    if (Objects[i].IsEnabled && Objects[i].IsDrawnInScene)
                        Objects[i].LateDraw();
                }

                // Custom Drawing
                AfterDraw?.Invoke();

                Canvas.Flush();

                Frame++;
            }
        }        

        public void Translate(SKPoint translation)
        {            
           var m = Canvas.TotalMatrix;
           m.TransX += translation.X;
           m.TransY += translation.Y;
           Canvas.SetMatrix(m);                   
        }

        // Gets bounding box for all elements in canvas
        public SKRect GetBoundingBox()
		{
            IEnumerable<SKRect> bboxes = (from o in Objects where o.IsDrawnInScene where !o.GetBoundingBox().IsEmpty select o.GetBoundingBox()).ToList();

            int xMin = bboxes.Min(s => (int)s.Left);
            int yMin = bboxes.Min(s => (int)s.Top);
            int xMax = bboxes.Max(s => (int)s.Right);
            int yMax = bboxes.Max(s => (int)s.Bottom);
            SKRect result = new SKRect(xMin, yMin, xMax, yMax);
            return result;
        }

        public void CenterAndFit(SKRect rect = default)
		{
            //var bbox = GetBoundingBox();
            var bbox =  rect!=default ? rect : new SKRect(0, 0, 1920, 1080);
            CenterAndFitRectangle(bbox);
		}

        public void CenterAndFitRectangle(SKRect viewport, int trans_x=100, int trans_y=200)
        {
            if (Canvas == null)
                return;

            //var _currentMatrix = Canvas.TotalMatrix;
            //var viewPortRectangle = _currentMatrix.MapRect(viewport);
            var canvas_size = Canvas.DeviceClipBounds.Size;

            //HACK: don't understand but the scale is not perfect
            //var scale_adjust = 0.7f; // Math.Max(canvas_size.Width / 1920f, canvas_size.Height / 1080f);

            var m = Canvas.TotalMatrix;

            var _currentMatrix = SKMatrix.CreateIdentity();
            var viewPortRectangle = viewport;
            //var scale = Math.Min(canvas_size.Width / viewPortRectangle.Width, canvas_size.Height / viewPortRectangle.Height);
            var scale = canvas_size.Width / (viewPortRectangle.Width * 1.8f); // NOTE: the multiplication is a fix to make it work!
            //scale *= scale_adjust;
            
            _currentMatrix.ScaleX = scale;
            _currentMatrix.ScaleY = scale;
            _currentMatrix.TransX = trans_x*scale;
            _currentMatrix.TransY = trans_y*scale;
            
            Canvas.SetMatrix(_currentMatrix);
            Refresh();
        }

        public void ScaleAroundPoint(float scale, SKPoint point)
        {
            SKMatrix matrix_start = Canvas.TotalMatrix;
            SKMatrix matrix_final = SKMatrix.CreateIdentity();
            //SKPoint pinchTranslation = point.Offset(-point.X, -point.Y);

            //SKMatrix canvasTranslation = SKMatrix.MakeTranslation((float)pinchTranslation.X, (float)pinchTranslation.Y);
            //SKMatrix canvasTranslation = SKMatrix.MakeTranslation(-canvas.TotalMatrix.TransX, -canvas.TotalMatrix.TransY);
            SKMatrix canvasTranslation = SKMatrix.CreateIdentity();
            SKMatrix canvasScaling = SKMatrix.CreateScale(scale, scale, point.X, point.Y);

            SKMatrix canvasCombined = SKMatrix.CreateIdentity();
            SKMatrix.Concat(ref canvasCombined, ref canvasScaling, ref canvasTranslation);
            SKMatrix.Concat(ref matrix_final, ref canvasCombined, ref matrix_start);

            Canvas.SetMatrix(matrix_final);
        }

        public void Scale(float s)
		{
            Canvas.Scale(s);
		}

        public bool DragObject(SKObject obj)
        {
            if (IsDraggingScene)
                return false;

            if (obj == null)
                return false;

            //var prev_selected = ObjectSelected;

            // every drag operation is at least a selection (if selectable)
            if (obj.IsSelectable)
				SelectObject(obj);

            // TEST: to avoid moving when we only want to select, allow moving only if it was already selected
            // FIXME: doesn't work, the very next frame it will be dragged
            /*
            if (obj.IsSelectable && ObjectSelected != prev_selected)
                return false;
            */

			if (obj.IsDraggable)
            {
				ObjectDragged = obj;

				if (Debug)
					System.Diagnostics.Debug.WriteLine($"ObjectDragged.DragObject = {obj.Name}");

				return true;
			}
            else
                return false;
        }

        // todo: unselect/select event?
        public void SelectObject(SKObject obj, bool invoke = true)
        {
            if (obj != null && !obj.IsSelectable)
                return;

            var prev_selected = ObjectSelected;

            ObjectSelected = obj;

            if (invoke)
            {
                // events are fired only if not already selected?
                if (ObjectSelected != prev_selected)
                {
                    OnObjectUnSelected?.Invoke(prev_selected);
                    prev_selected?.onUnSelected?.Invoke();

                    OnObjectSelected?.Invoke(ObjectSelected);
                    ObjectSelected?.onSelected?.Invoke();
                }
            }

            Refresh();
        }
        
        public void UnSelectAllObjects(bool invoke = true)
        {
			var prev_selected = ObjectSelected;

			ObjectSelected = null;

			if (invoke)
			{
				// events are fired only if not already selected?
				if (ObjectSelected != prev_selected && prev_selected!=null)
				{
					OnObjectUnSelected?.Invoke(prev_selected);
					prev_selected?.onUnSelected?.Invoke();

					OnObjectSelected?.Invoke(ObjectSelected);
					ObjectSelected?.onSelected?.Invoke();
				}
			}

			Refresh();
		}

        public void AddAnimation(ITweener anim)
        {
            Animations.Add(anim);
        }

        // Routes a key event to the selected object, or general handlers
        public void HandleKey(bool is_down, KeyEventArgs key_args)
		{
            if (ObjectSelected != null)
                ObjectSelected?.onKey?.Invoke(is_down, key_args);
            else
                OnKey?.Invoke(is_down, key_args);
		}

        public void DetectMouseEvents(int mouse_button)
        {
			if (!LastMousePressed[mouse_button] && MousePressed[mouse_button])
				OnMouseDown(mouse_button, MousePosition);
			if (LastMousePressed[mouse_button] && !MousePressed[mouse_button])
				OnMouseUp(mouse_button, MousePosition);
		}

        public void OnMouseDown(int mouse_button, SKPoint mouse_position)
        {
            // log last click position
            MouseEventStatus = Click.ClickStatus.Pressed;
            MouseEventButton = mouse_button;
            MouseEventPosition = mouse_position;
        }

		public void OnMouseUp(int mouse_button, SKPoint mouse_position)
		{
			MouseEventStatus = Click.ClickStatus.Released;
			MouseEventButton = mouse_button;
			MouseEventPosition = mouse_position;
		}

	}
}
