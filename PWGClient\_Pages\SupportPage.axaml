<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.SupportPage">
	<Border Classes="Card" VerticalAlignment="Stretch" >
		<ScrollViewer VerticalAlignment="Top">
			<Grid RowDefinitions="Auto,Auto,*" VerticalAlignment="Stretch">

				<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto" ColumnDefinitions="*,Auto">
					<TextBlock Grid.Row="0" Text="Support" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
					<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
					<Grid  Grid.Row="2" ColumnDefinitions="*,Auto" Margin="5,10,0,10" >
						<TextBlock Grid.Column="0" VerticalAlignment="Center">
							Get support describing your issue and sending a message to the support team.
						</TextBlock>

						<Button Grid.Column="1" Margin="5" Classes="Primary Rounded"  ToolTip.Tip="Sends the support request." VerticalAlignment="Center" HorizontalAlignment="Right" Click="bt_send_request">
							<TextBlock Text="Send"/>
						</Button>
					</Grid>
					
				</Grid>
						
				<StackPanel Grid.Row="1" Orientation="Vertical" VerticalAlignment="Top" Margin="0,0,0,0">
					<TextBlock Text="Email:" FontWeight="Bold" Margin="10,10,10,0"/>
					<TextBox Text="{Binding SupportRequestEmail}" Margin="10"/>
					<TextBlock Text="Message:" FontWeight="Bold" Margin="10,10,10,0"/>
				</StackPanel>

				<TextBox Grid.Row="2" Height="150" Text="{Binding SupportRequestMessage}" HorizontalAlignment="Stretch" VerticalContentAlignment="Top" MaxLines="100"  AcceptsReturn="True" TextWrapping="Wrap" Margin="10"/>
			</Grid>
		</ScrollViewer>
	</Border>
	
</UserControl>
