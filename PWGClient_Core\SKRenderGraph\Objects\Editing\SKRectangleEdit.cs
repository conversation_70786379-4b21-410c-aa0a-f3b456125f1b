﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using SkiaSharp;
using Tabula.SKRenderGraph;
using static Tabula.SKRenderGraph.SKFreePolygon;

// A rectangle with 4 control points 

namespace Tabula.SKRenderGraph
{
    public class SKRectangleEdit : SKRectangle
    {
        public float Opacity = 1f;

        // Constraints
        public bool     Simmetrical = false;
        public bool     LockMove = false;
        public SKRect   MaximumRect = SKRect.Empty;
        public SKSize   MinimumSize = SKSize.Empty;

        // Events
        public Action<SKControlPoint, int, SKPoint> OnVertexMove;   // <control_point>,<vertex_idx>,<pos>


        public SKRectangleEdit(SKScene scene, SKRect rect) : base(scene, rect)
        {
            Color = new SKColor(0, 0, 0, 200);
            ColorHover = new SKColor(0, 0, 255, 200);
            ColorDragging = new SKColor(255, 255, 255, 200);

            // Vertices are alway 4, add the controlpoints
            var verts = new List<SKPoint>()
            {
                new SKPoint(rect.Left,rect.Top), new SKPoint(rect.Right,rect.Top), new SKPoint(rect.Right,rect.Bottom), new SKPoint(rect.Left,rect.Bottom)
            };

            for (int i = 0; i < verts.Count; i++)
            {
                var cp = new SKControlPoint(Scene);
                cp.IsEnabled = false;
                cp.onMove += OnControlPointMove;
                ControlPoints.Add(cp);
                Scene.Add(cp, Parent);
            }

            SyncToControlPoints();
        }

        private void OnControlPointMove(SKObject obj, SKPoint pos_rel)
        {
            var cp = (SKControlPoint) obj;

			int index = ControlPoints.IndexOf(cp);

            SKRect undo_rect = Rect;

            SetVertexPosition(index, cp.GetPosition());
                        
            if (Simmetrical)
            {
                SKPoint pos;

                switch (index)
                {
                    case 0:
                        pos = GetVertexPosition(2);
						SetVertexPosition(2, pos + new SKPoint(-pos_rel.X, -pos_rel.Y));
                        break;

					case 1:
						pos = GetVertexPosition(3);
						SetVertexPosition(3, pos + new SKPoint(-pos_rel.X, -pos_rel.Y));
						break;

					case 2:
						pos = GetVertexPosition(0);
						SetVertexPosition(0, pos + new SKPoint(-pos_rel.X, -pos_rel.Y));
						break;

					case 3:
						pos = GetVertexPosition(1);
						SetVertexPosition(1, pos + new SKPoint(-pos_rel.X, -pos_rel.Y));
						break;
				}
            }

			// check size
			if (MinimumSize!=SKSize.Empty && 
                (Rect.Size.Width < MinimumSize.Width || Rect.Size.Height < MinimumSize.Height)) 
			{
                Rect = undo_rect;
			}

			SyncToControlPoints();

			OnVertexMove?.Invoke(cp, index, pos_rel);
		}

        // taken directly from the rectangle
        public SKPoint GetVertexPosition(int index)
        {
            switch (index)
            {
                case 0: return new SKPoint(Rect.Left, Rect.Top);
				case 1: return new SKPoint(Rect.Right, Rect.Top);
				case 2: return new SKPoint(Rect.Right, Rect.Bottom);
				case 3: return new SKPoint(Rect.Left, Rect.Bottom);

                default:
                    return SKPoint.Empty;
			}
        }

        public void SetVertexPosition(int index, SKPoint p)
        {
			switch (index)
			{
				case 0:
                    Rect.Left = p.X;
                    Rect.Top = p.Y;
                    break;

                case 1:
					Rect.Right = p.X;
					Rect.Top = p.Y;
					break;

				case 2:
					Rect.Right = p.X;
					Rect.Bottom = p.Y;
					break;

				case 3:
					Rect.Left = p.X;
					Rect.Bottom = p.Y;
					break;
			}
		}

		public override void SyncFromControlPoints()
        {
            for (int i = 0; i <4; i++)
                SetVertexPosition(i, ControlPoints[i].GetPosition());
        }

        public override void SyncToControlPoints()
        {
            for (int i = 0; i < 4; i++)
                ControlPoints[i].SetPosition(GetVertexPosition(i));
        }

        public override void SetPosition(SKPoint pos)
        {
			base.SetPosition(pos);

			SyncToControlPoints();
        }

        public override void Move(SKPoint pos_rel)
        {
            if (LockMove)
                return;

			base.Move(pos_rel);

			SyncToControlPoints();
        }

		public override void OnEnable()
		{
			foreach (var cp in ControlPoints)
				cp.IsEnabled = IsSelected;

			base.OnEnable();
		}

		public override void OnDisable()
		{
			foreach (var cp in ControlPoints)
				cp.IsEnabled = false;

			base.OnEnable();
		}

		public override void Update()
        {
            // SyncFromControlPoints();

            if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsMouseOver)
            {
                Paint.Color = ColorHover;

                if (Scene.MouseLeftPressed)
                {
                    DragStart();
                }
            }
            else
            {
                Paint.Color = Color;
            }

            foreach (var cp in ControlPoints)
                cp.IsEnabled = IsSelected;

            if (MaximumRect != SKRect.Empty)
            {
                if (Rect.Left < MaximumRect.Left)
                    Rect.Left = MaximumRect.Left;
                if (Rect.Top < MaximumRect.Top)
                    Rect.Top = MaximumRect.Top;
                if (Rect.Right > MaximumRect.Right)
                    Rect.Right = MaximumRect.Right;
                if (Rect.Bottom > MaximumRect.Bottom)
                    Rect.Bottom = MaximumRect.Bottom;

				SyncToControlPoints();
			}
        }


        public override void Draw()
        {
            base.Draw();
        }

    }
}
