﻿<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
             xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
             xmlns:pmcore="clr-namespace:Tabula.PMCore;assembly=PWGClient_Core"
			 xmlns:dialogHostAvalonia="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
             xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             x:Class="Tabula.PWGClient.App"
			 Name="S-ARGAME Editor">

	<!-- NATIVE MENU -->
	<NativeMenu.Menu>
		<NativeMenu>
			<NativeMenuItem Header="About S-ARGAME"  /> <!-- Click="AboutMenuItem_OnClick" -->
		</NativeMenu>
	</NativeMenu.Menu>

  <Application.Resources>
    <!-- <themes:BundledTheme BaseTheme="Dark" PrimaryColor="Teal" SecondaryColor="Amber"/>    -->
	  <local:DebugConverter x:Key="DebugConverter"/>

	  <!-- raw value converters -->
      <local:IntConverter x:Key="IntConverter"/>
      <local:FloatConverter2 x:Key="FloatConverter2"/>
	  <local:FloatConverter4 x:Key="FloatConverter4"/>
	  <local:DoubleConverter2 x:Key="DoubleConverter2"/>
	  <local:HexColorConverterRGBA x:Key="HexColorConverterRGBA"/>
	  <local:HexColorConverterHSV x:Key="HexColorConverterHSV"/>
	  <local:BoolToColorToggleConverter x:Key="BoolToColorToggleConverter"/>	  

	  <!-- special converters -->
	  <local:BitmapValueConverter x:Key="BitmapValueConverter"/>
	  <local:BoolToOpacityConverter x:Key="BoolToOpacityConverter"/>
	  <local:StringToBoolConverter x:Key="StringToBoolConverter"/>
	  <local:StringCapitalizeConverter x:Key="StringCapitalizeConverter"/>	  
	  <local:LabelConverter x:Key="LabelConverter"/>
	  <local:FloatToPercentageConverter x:Key="FloatToPercentageConverter"/>
	  <local:CollectionHasElementsConverter x:Key="CollectionHasElementsConverter"/>
  </Application.Resources>
  
  <Application.DataTemplates>

		<!-- INSPECTOR DATA TEMPLATES -->

		<!-- ButtonFieldView -->
		<DataTemplate DataType="{x:Type pmcore:ButtonFieldView}">
			<Grid ColumnDefinitions="70,*">				
				<Button Grid.Column="1" Classes="Primary" Content="{Binding Label}"
						ToolTip.Tip="{Binding tooltip}"
						HorizontalAlignment="Stretch"
						HorizontalContentAlignment="Center"
						Height="45"
						Margin="10"
						Click="bt_entity_field_button" />
			</Grid>			
		</DataTemplate>

		<!-- FileFieldView -->
		<DataTemplate DataType="{x:Type pmcore:FileFieldView}">
			<Grid ColumnDefinitions="70,*">
				<TextBlock  Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
				<Button Grid.Column="1" Content="{materialIcons:MaterialIconExt Kind=FileImport}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Height="50"  Click="bt_entity_file_field_button"/>
			</Grid>
		</DataTemplate>

		<!-- IntegerFieldView -->
		<DataTemplate DataType="{x:Type pmcore:IntegerFieldView}">
			<Grid ColumnDefinitions="70,*">
				<TextBlock  Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
				<TextBox Grid.Column="1" Classes="property" VerticalAlignment="Center">
				<Interaction.Behaviors>
					<local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource IntConverter}}"/>
				</Interaction.Behaviors>      
				</TextBox>
			</Grid>
		</DataTemplate>

		<!-- FloatFieldView -->
		<DataTemplate DataType="{x:Type pmcore:FloatFieldView}">
		  <Grid ColumnDefinitions="70,*">
			<TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
			<TextBox Grid.Column="1" Classes="property" VerticalAlignment="Center">
				<Interaction.Behaviors>
				  <local:LostFocusUpdateBindingBehavior Text="{Binding value, Converter={StaticResource FloatConverter2}}"/>
				</Interaction.Behaviors>      
			</TextBox>
		  </Grid>
		</DataTemplate>

		<!-- BoolFieldView -->
		<DataTemplate DataType="{x:Type pmcore:BoolFieldView}">
			<Grid ColumnDefinitions="70,*">
				<TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
				<CheckBox Grid.Column="1" IsChecked="{Binding value}" Margin="5" VerticalAlignment="Center" HorizontalAlignment="Center" />
			</Grid>
		</DataTemplate>

	  <!-- BoolCardsFieldView -->
	  <DataTemplate DataType="{x:Type pmcore:BoolCardsFieldView}">
		  <Grid ColumnDefinitions="70,*">
			  <TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
			  <local:BoolCardsField Grid.Column="1"/>
		  </Grid>
	  </DataTemplate>

		<!-- StringFieldView -->
		<DataTemplate DataType="{x:Type pmcore:StringFieldView}">
			<Grid ColumnDefinitions="70,*">
				<TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
				<TextBox Grid.Column="1" Classes="property" Text="{Binding value}" VerticalAlignment="Center"/>
			</Grid>
		</DataTemplate>
	  
	    <!-- ColorFieldView -->
		<DataTemplate DataType="{x:Type pmcore:ColorFieldView}">
			<Grid ColumnDefinitions="70,*">
				<TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
				<ColorPicker Grid.Column="1" Margin="10" VerticalAlignment="Center" IsHexInputVisible="False" ColorModel="Hsva" ColorSpectrumShape="Box" IsComponentSliderVisible="False" IsColorPaletteVisible="False" IsColorComponentsVisible="False" IsColorModelVisible="False" IsAccentColorsVisible="False"  IsColorSpectrumVisible="False" Color="{Binding value, Converter={StaticResource HexColorConverterRGBA}}"/>
			</Grid>
		</DataTemplate>

		<!-- SliderFloatFieldView -->
		<DataTemplate DataType="{x:Type pmcore:SliderFloatFieldView}">
		  <Grid ColumnDefinitions="70,*">
			<TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center" />
			  <Slider Grid.Column="1" Minimum="{Binding min}" Maximum="{Binding max}" Value="{Binding value, Mode = TwoWay }" ToolTip.Tip="{Binding value, Converter={StaticResource FloatConverter2}}" ToolTip.Placement="TopEdgeAlignedLeft" VerticalAlignment="Center">
				  <!--
				  <Slider.RenderTransform>
					  <ScaleTransform ScaleX="0.7" ScaleY="0.7"/>
				  </Slider.RenderTransform>
				  -->
			  </Slider>
		  </Grid>
		</DataTemplate>

		<!-- Position2DFieldView -->
		<DataTemplate DataType="{x:Type pmcore:Position2DFieldView}">
		  <Expander Header="{Binding __Parent.name, Converter={StaticResource LabelConverter}}" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">
			<StackPanel Orientation="Vertical" HorizontalAlignment="Stretch">
			  <Grid ColumnDefinitions="*,*">
				<TextBox Grid.Column="0" Classes="property" HorizontalContentAlignment="Center" VerticalAlignment="Center">
				  <Interaction.Behaviors>
					<local:LostFocusUpdateBindingBehavior Text="{Binding position.x, Converter={StaticResource FloatConverter2}}"/>
				  </Interaction.Behaviors>
				</TextBox>
				<TextBox Grid.Column="1" Classes="property" HorizontalContentAlignment="Center" VerticalAlignment="Center">
				  <Interaction.Behaviors>
					<local:LostFocusUpdateBindingBehavior Text="{Binding position.y, Converter={StaticResource FloatConverter2}}"/>
				  </Interaction.Behaviors>
				</TextBox>
			  </Grid>
			</StackPanel>
		  </Expander>            
		</DataTemplate>

	  <!-- ChoiceFieldView -->
	  <DataTemplate DataType="{x:Type pmcore:ChoiceFieldView}">
		  <Grid ColumnDefinitions="70,*">
			  <TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
			  <ComboBox Grid.Column="1" Classes="property" 
						FontWeight="Medium" FontSize="8"
						IsTextSearchEnabled="True" ItemsSource="{Binding Choices}" SelectedValue="{Binding value}" SelectedValueBinding="{Binding Key}" DisplayMemberBinding="{Binding Value}"  VerticalAlignment="Center"/>
		  </Grid>
	  </DataTemplate>

	  <!-- ChoiceSelectorFieldView -->
	  <DataTemplate DataType="{x:Type pmcore:ChoiceSelectorFieldView}">
		  <Grid ColumnDefinitions="70,*">
			  <TextBlock Grid.Column="0" Classes="property" ToolTip.Tip="{Binding tooltip}" Text="{Binding Label, Converter={StaticResource LabelConverter}}" VerticalAlignment="Center"/>
			  <local:ChoiceSelectorField Grid.Column="1"/>
		  </Grid>
		 
	  </DataTemplate>

		<!-- HIERARCHY GROUPS, need to stay here -->
	  
		<!-- StructureGroup -->
		<TreeDataTemplate DataType="{x:Type local:StructureGroup}" ItemsSource="{Binding Items}">
			<Grid RowDefinitions="Auto,Auto,*" >
			</Grid>
		</TreeDataTemplate>

		<!-- OutputSurfaceGroup -->
		<TreeDataTemplate DataType="{x:Type local:OutputSurfaceGroup}" ItemsSource="{Binding Items}">
			<Grid RowDefinitions="Auto,Auto,*">
			</Grid>
		</TreeDataTemplate>

		<!-- CursorView -->
		<DataTemplate DataType="{x:Type pmcore:CursorView}">
			<Expander Header="Cursor" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">
				<StackPanel Orientation="Vertical" HorizontalAlignment="Stretch">
					<Grid ColumnDefinitions="*,*">
						<TextBox Grid.Column="0" Margin="10" HorizontalContentAlignment="Center">
							<Interaction.Behaviors>
								<local:LostFocusUpdateBindingBehavior Text="{Binding position.x, Converter={StaticResource FloatConverter2}}"/>
							</Interaction.Behaviors>
						</TextBox>
						<TextBox Grid.Column="1" Margin="10" HorizontalContentAlignment="Center">
							<Interaction.Behaviors>
								<local:LostFocusUpdateBindingBehavior Text="{Binding position.y, Converter={StaticResource FloatConverter2}}"/>
							</Interaction.Behaviors>
						</TextBox>
					</Grid>
				</StackPanel>
			</Expander>
		</DataTemplate>

		<!-- OutputSurfaceView -->
		<DataTemplate DataType="{x:Type pmcore:OutputSurfaceView}">
			<Grid RowDefinitions="Auto,*">
				<Expander Grid.Row="1" Header="Projection Surface #0" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">
					<StackPanel Orientation="Vertical" Margin="0,10,0,0">

						<!--
						<Grid ColumnDefinitions="*,*">						  
							<TextBlock Grid.Column="0" Margin="10" Text="x_offset"/>
							<Slider Grid.Column="1" Minimum="-1" Maximum="1" TickFrequency="0.01" SmallChange="0.01" Value="{Binding x_offset, Mode = TwoWay }"/>						  
						</Grid>
						<Grid ColumnDefinitions="*,*">
							<TextBlock Grid.Column="0" Margin="10" Text="x_tiling"/>
							<Slider Grid.Column="1" Minimum="-2" Maximum="2" TickFrequency="0.01" SmallChange="0.01" Value="{Binding x_tiling, Mode = TwoWay }"/>
						</Grid>
						<Grid ColumnDefinitions="*,*">
							<TextBlock Grid.Column="0" Margin="10" Text="y_offset"/>
							<Slider Grid.Column="1" Minimum="-1" Maximum="1" TickFrequency="0.01" SmallChange="0.01" Value="{Binding y_offset, Mode = TwoWay }"/>
						</Grid>
						<Grid ColumnDefinitions="*,*">
							<TextBlock Grid.Column="0" Margin="10" Text="y_tiling"/>
							<Slider Grid.Column="1" Minimum="-2" Maximum="2" TickFrequency="0.01" SmallChange="0.01" Value="{Binding y_tiling, Mode = TwoWay }"/>
						</Grid>		
						-->
						
						<!--
						<Button Content="Reset Crop" Margin="5" HorizontalAlignment="Center">
							<i:Interaction.Behaviors>
								<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
									<ia:CallMethodAction TargetObject="{Binding}" MethodName="ResetOffsetTiling" />
								</ia:EventTriggerBehavior>
							</i:Interaction.Behaviors>
						</Button>

							<Button Content="Reset Warp" Margin="5" HorizontalAlignment="Center">
							<i:Interaction.Behaviors>
								<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
									<ia:CallMethodAction TargetObject="{Binding}" MethodName="ResetSurfaceWarp" />
								</ia:EventTriggerBehavior>
							</i:Interaction.Behaviors>
						</Button>
						-->
					</StackPanel>
				</Expander>
			</Grid>
		</DataTemplate>

		<!-- StructureView (INSPECTOR) -->
		<DataTemplate DataType="{x:Type pmcore:StructureView}">
			<Grid RowDefinitions="Auto,*">			 
				<Expander Grid.Row="1" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">
					<Expander.Header>
						<Grid Background="Blue">
							<TextBlock Text="{Binding Name}"/>
						</Grid>
					</Expander.Header>
					<StackPanel Orientation="Vertical" Margin="0,10,0,0">
						<!-- <local:UserBitmap Width="48" Height="48" Path="player" /> -->

						<!-- STANDARD -->
						<StackPanel Orientation="Horizontal" Margin="0,10,0,0" HorizontalAlignment="Center" VerticalAlignment="Center">
							<Grid ColumnDefinitions="*,*">
								<TextBlock Grid.Column="0"  Classes="property" Text="Is Mask" VerticalAlignment="Center"/>
								<CheckBox Grid.Column="1"  Classes="property" IsChecked="{Binding is_mask}"/>
							</Grid>

							<Grid ColumnDefinitions="*,*">
								<TextBlock Grid.Column="0"  Classes="property" Text="Is Physical" VerticalAlignment="Center"/>
								<CheckBox Grid.Column="1"  Classes="property" IsChecked="{Binding is_physical}"/>
							</Grid>
						</StackPanel>

						<!-- Color for Experiences, direct set-->
						<Grid ColumnDefinitions="*,*" Margin="0,10,0,0" IsVisible="{Binding Main.IsExperience}" HorizontalAlignment="Center" VerticalAlignment="Center">
							<TextBlock Grid.Column="0"  Classes="property" Text="Color" VerticalAlignment="Center"/>
							<ColorPicker Grid.Column="1" IsHexInputVisible="False" ColorModel="Hsva" ColorSpectrumShape="Box" IsColorPaletteVisible="False" IsColorComponentsVisible="False" IsColorModelVisible="False" IsAccentColorsVisible="False"  IsColorSpectrumVisible="False" Color="{Binding params0.color, Converter={StaticResource HexColorConverterRGBA}}"/>
						</Grid>

						<!--Color for games, must be enabled -->
						<Grid ColumnDefinitions="Auto,*,*"  Margin="0,10,0,0" IsVisible="{Binding Main.IsGame}" HorizontalAlignment="Center" VerticalAlignment="Center">
							<CheckBox Grid.Column="0" IsChecked="{Binding params0.color_enable}"/>
							<TextBlock Grid.Column="1"  Classes="property" Text="Color" VerticalAlignment="Center"/>
							<ColorPicker Grid.Column="2" IsEnabled="{Binding params0.color_enable}" IsHexInputVisible="False" ColorModel="Hsva" ColorSpectrumShape="Box" IsColorPaletteVisible="False" IsColorComponentsVisible="False" IsColorModelVisible="False" IsAccentColorsVisible="False"  IsColorSpectrumVisible="False" Color="{Binding params0.color, Converter={StaticResource HexColorConverterRGBA}}"/>
						</Grid>


						<!-- GAME effects: NO MORE -->
						<!--
						<Expander Header="Game FX" ExpandDirection="Down" IsExpanded="False" HorizontalAlignment="Stretch" IsVisible="{Binding Main.IsGame}">
							<StackPanel Orientation="Vertical" Margin="0,10,0,0">
								
								<Grid ColumnDefinitions="Auto,*,*">
									<CheckBox Grid.Column="0" IsChecked="{Binding params0.color_animation_enable}"/>
									<TextBlock Grid.Column="1"  Classes="property" Text="Color FX" VerticalAlignment="Center"/>
									<NumericUpDown  Grid.Column="2" Classes="property" Value="{Binding params0.color_animation}" IsEnabled="{Binding params0.color_animation_enable}" Increment="1"/>
								</Grid>
								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Color FX Speed" VerticalAlignment="Center"/>
									<TextBox Grid.Column="1"  Classes="property" Text="{Binding params0.color_animation_speed, Converter={StaticResource FloatConverter4}}"/>
								</Grid>
								<Grid ColumnDefinitions="Auto, *,*">
									<CheckBox Grid.Column="0" IsChecked="{Binding params0.effect_enable}"/>
									<TextBlock Grid.Column="1" Classes="property" Text="Border Effect" VerticalAlignment="Center"/>
									<NumericUpDown  Grid.Column="2" Classes="property" Value="{Binding params0.effect}" IsEnabled="{Binding params0.effect_enable}" Increment="1"/>
								</Grid>
								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Scale" TextAlignment="Right"/>
									<Slider Grid.Column="1" Minimum="0.01" Maximum="2.0" Value="{Binding params0.effect_scale, Mode = TwoWay }"/>
								</Grid>
								<Grid ColumnDefinitions="Auto,*,*">
									<CheckBox Grid.Column="0" IsChecked="{Binding params0.effect2_enable}"/>
									<TextBlock Grid.Column="1" Classes="property" Text="Face Effect" VerticalAlignment="Center"/>
									<NumericUpDown  Grid.Column="2" Classes="property" Value="{Binding params0.effect2}" IsEnabled="{Binding params0.effect2_enable}" Increment="1"/>
								</Grid>
								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Scale" TextAlignment="Right"/>
									<Slider Grid.Column="1" Minimum="0.01" Maximum="2.0" Value="{Binding params0.effect2_scale, Mode = TwoWay }"/>
								</Grid>
								<Grid ColumnDefinitions="Auto,*,*">
									<CheckBox Grid.Column="0" IsChecked="{Binding params0.contour_enable}"/>
									<TextBlock Grid.Column="1"  Classes="property" Text="Contour Ray" VerticalAlignment="Center"/>
									<NumericUpDown  Grid.Column="2"  Classes="property" Value="{Binding params0.contour}" IsEnabled="{Binding params0.contour_enable}" Increment="1"/>
								</Grid>
							</StackPanel>
						</Expander>
						-->
						
						<!-- EXPERIENCE effects -->
						<Grid RowDefinitions="Auto,1,*" Margin="10" IsVisible="{Binding Main.CanAddStructureEffects}">
							<Grid Grid.Row="0" ColumnDefinitions="*,Auto" Background="{StaticResource FakeBackground}">
								<Grid  ColumnDefinitions="15,*,25" HorizontalAlignment="Stretch">
									<materialIcons:MaterialIcon Grid.Column="0" Width="15" Height="15" Kind="Flare" Margin="0,0,5,0"/>
									<TextBlock Grid.Column="1" Text="Effects" FontWeight="Bold" FontSize="14" HorizontalAlignment="Stretch"
										   VerticalAlignment="Center" TextAlignment="Left" Margin="3"/>
									<Border Grid.Column="2" Classes="Rounded" Background="{StaticResource SukiPrimaryColor}" PointerPressed="bt_structure_effects_add_pressed">
										<materialIcons:MaterialIcon Width="20" Height="20" Kind="Add" Margin="0" Padding="3"/>
									</Border>
								</Grid>
							</Grid>
							<ItemsControl Grid.Row="2" ItemsSource="{Binding effects}" Background="Transparent" Margin="0,5,0,0">
								<ItemsControl.ItemTemplate>
									<DataTemplate>
										<ContentControl Content="{Binding}"/>
									</DataTemplate>
								</ItemsControl.ItemTemplate>
							</ItemsControl>
						</Grid>
																														
						<!--
						<Grid ColumnDefinitions="*,*">
							<TextBlock Grid.Column="0"  Classes="property" Text="Color"/>
							<Slider Grid.Column="1" Minimum="0" Maximum="1" Value="{Binding color, Mode = TwoWay }"/>
						</Grid>
						-->
					  
						<!--
						<StackPanel Orientation="Horizontal" Margin="10" HorizontalAlignment="Stretch">
							<Button x:Name="bt_structure_remove" Content="Remove" Margin="5" HorizontalAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_structure_remove}">
										<ia:CallMethodAction TargetObject="{Binding}" MethodName="Remove"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>
						</StackPanel>
						-->
					</StackPanel>
				</Expander>
			</Grid>
		</DataTemplate>

		<!-- PMImageMarkerView -->
		<DataTemplate DataType="{x:Type pmcore:PMImageMarkerView}">
			<Grid RowDefinitions="Auto,*">
				<Expander Grid.Row="1" Header="{Binding Name}" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">
					<StackPanel Orientation="Vertical" Margin="0,10,0,0">
						<StackPanel Orientation="Horizontal" Margin="10" HorizontalAlignment="Stretch">
							<Button x:Name="bt_imagemarker_remove" Content="Disable" Margin="5" HorizontalAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_imagemarker_remove}">
										<ia:CallMethodAction TargetObject="{Binding}" MethodName="Delete"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>
						</StackPanel>
					</StackPanel>
				</Expander>
			</Grid>
		</DataTemplate>


	  <!-- FieldGroup -->
	  <DataTemplate DataType="{x:Type pmcore:FieldGroup}">
		  <Grid RowDefinitions="Auto,*">
			  <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="5" Background="{StaticResource FieldGroupHeader}" IsVisible="{Binding ShowHeader}">
					<TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="10" HorizontalAlignment="Stretch"
							   VerticalAlignment="Stretch" TextAlignment="Left" Margin="3" Background="{StaticResource FakeBackground}"
							   PointerPressed="bt_entity_fieldgroup_header_pressed"/>
			  </Grid>
			  <ItemsControl Grid.Row="1" ItemsSource="{Binding GroupFields}" Background="Transparent" 
							IsVisible="{Binding AreFieldsExpanded}">
				  <ItemsControl.ItemTemplate>
					  <DataTemplate>
						  <ContentControl Content="{Binding}"/>
					  </DataTemplate>
				  </ItemsControl.ItemTemplate>
			  </ItemsControl>
		  </Grid>
	  </DataTemplate>
	  
	  <!-- EntityView (INSPECTOR) -->
	  <DataTemplate DataType="{x:Type pmcore:EntityView}">
		<Grid RowDefinitions="Auto,2,*">


			<!-- <Expander Grid.Row="1" FontSize="16" FontWeight="Bold" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch">		

				  <Expander.Header>
					  <Grid ColumnDefinitions="Auto, *">
						  <Viewbox Grid.Column="0" Width="32" Height="32" IsVisible="{Binding HasIcon}" HorizontalAlignment="Center" VerticalAlignment="Center">
							  <local:UserBitmap Height="32" Path="{Binding icon}" VerticalContentAlignment="Center" HorizontalAlignment="Left" HorizontalContentAlignment="Left" IsVisible="{Binding HasIcon}" Margin="3"/>
						  </Viewbox>
						  
						  <TextBlock Grid.Column="1" Text="{Binding Name}" FontWeight="Bold" HorizontalAlignment="Left" VerticalAlignment="Center" TextAlignment="Left" Margin="3"/>
					  </Grid>						  
				  </Expander.Header>
				  -->

				  <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="5">
					  <StackPanel Orientation="Horizontal" >
						  <Viewbox Width="32" Height="32" IsVisible="{Binding HasIcon}" HorizontalAlignment="Center" VerticalAlignment="Center">
							  <local:UserBitmap Height="32" Path="{Binding icon}" VerticalContentAlignment="Center" HorizontalAlignment="Left" HorizontalContentAlignment="Left" IsVisible="{Binding HasIcon}" Margin="3"/>
						  </Viewbox>
						  <TextBlock Text="{Binding Name}" FontWeight="Bold" HorizontalAlignment="Left" VerticalAlignment="Center" TextAlignment="Left" Margin="3"/>
					  </StackPanel>
					  <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right">
						  <Viewbox Classes="toolicon" PointerPressed="bt_entity_tools_button" Tag="lock" IsVisible="{Binding HasVisual}">
							  <materialIcons:MaterialIcon Classes="toolicon" Kind="Lock" Foreground="{Binding IsLocked, Converter={StaticResource BoolToColorToggleConverter}, ConverterParameter={StaticResource RedColor}}" Background="{StaticResource FakeBackground}"/>
						  </Viewbox>
					  </StackPanel>
				  </Grid>

				  <Rectangle Grid.Row="1" HorizontalAlignment="Stretch"  Fill="{StaticResource SukiPrimaryColor}"/>
			
				  <StackPanel Grid.Row="2" Orientation="Vertical">

						<!-- General -->
						<!--
						<Grid  Grid.Row="2" ColumnDefinitions="*,*">
							<TextBlock Grid.Column="0" Margin="10" Text="Active"/>
							<ToggleButton Grid.Column="1" Classes="Switch" Margin="10" IsChecked="{Binding active}"/>				  
						</Grid>
						-->

						<!-- Visual -->
						<!-- <Expander Grid.Row="3" Header="Transform" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch" IsVisible="{Binding HasVisual}"> -->

					    <!-- Position -->
						<!--
						<Grid ColumnDefinitions="*,*"  Background="Transparent" Grid.Row="3" IsVisible="{Binding HasVisual}">
							<TextBox Grid.Column="0" Margin="10" HorizontalContentAlignment="Center">
								<Interaction.Behaviors>
									<local:LostFocusUpdateBindingBehavior Text="{Binding position.x, Converter={StaticResource FloatConverter2}}"/>
								</Interaction.Behaviors>
							</TextBox>
							<TextBox Grid.Column="1" Margin="10" FontSize="10" HorizontalContentAlignment="Center">
								<Interaction.Behaviors>
									<local:LostFocusUpdateBindingBehavior Text="{Binding position.y, Converter={StaticResource FloatConverter2}}"/>
								</Interaction.Behaviors>
							</TextBox>
						</Grid>
						-->
						<!-- </Expander> -->

						<!-- Fields -->
						<!--  <Expander Grid.Row="4" Header="Fields" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch" IsVisible="{Binding HasFields}"> -->
					  <!--
					    <ItemsControl ItemsSource="{Binding FieldsShown}" Background="Transparent" Grid.Row="4" IsVisible="{Binding HasFields}">
							<ItemsControl.ItemTemplate>
								<DataTemplate>
									<ContentControl Content="{Binding}"/>
								</DataTemplate>
							</ItemsControl.ItemTemplate>
						</ItemsControl>
						-->

					  <ItemsControl ItemsSource="{Binding Groups}" Background="Transparent" Grid.Row="4" IsVisible="{Binding HasFields}">
						  <ItemsControl.ItemTemplate>
							  <DataTemplate>
								  <ContentControl Content="{Binding}"/>
							  </DataTemplate>
						  </ItemsControl.ItemTemplate>
					  </ItemsControl>
				  </StackPanel>

				<!--</Expander> -->
		</Grid>
    </DataTemplate>

	  <!-- StructureEffectView (INSPECTOR) -->
	  <DataTemplate DataType="{x:Type pmcore:StructureEffectView}">
		  <Grid RowDefinitions="Auto,*">
			  <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="15,5,0,5" Background="{StaticResource FieldGroupHeader}" IsVisible="{Binding ShowHeader}">
				  <TextBlock Grid.Column="0" Text="{Binding Name}" FontWeight="Bold" FontSize="10" HorizontalAlignment="Stretch"
							 VerticalAlignment="Stretch" TextAlignment="Left" Margin="3" Background="{StaticResource FakeBackground}"
							 PointerPressed="bt_structureeffect_header_pressed"/>
				  <Border Grid.Column="1" Classes="Rounded" Background="{StaticResource FakeBackground}"  PointerPressed="bt_structure_effect_delete_pressed">
					  <materialIcons:MaterialIcon Width="15" Height="15" Kind="Close" Margin="0" Padding="3"/>
				  </Border>
			  </Grid>
			  <ItemsControl Grid.Row="1" ItemsSource="{Binding fields}" Background="Transparent"
							IsVisible="{Binding AreFieldsExpanded}">
				  <ItemsControl.ItemTemplate>
					  <DataTemplate>
						  <ContentControl Content="{Binding}"/>
					  </DataTemplate>
				  </ItemsControl.ItemTemplate>
			  </ItemsControl>
		  </Grid>
	  </DataTemplate>


  </Application.DataTemplates>
  
  <Application.Styles>
	
  

    <!-- MATERIAL -->
	  <!--
	<themes:MaterialThemes BaseTheme="Dark" PrimaryColor="Teal" SecondaryColor="Amber" />
	-->

	<!-- SUKIUI -->
	  <StyleInclude Source="avares://SukiUI/Theme/Index.xaml"/>
	  <!-- <StyleInclude Source="avares://SukiUI/ColorTheme/Dark.axaml" /> -->
	 
	  

	<!-- DialogHost -->
	  <!--
	 <StyleInclude Source="avares://DialogHost.Avalonia/Styles.xaml"/>
	 -->
 
	<!--
    <StyleInclude Source="/Styles/Styles.xaml" /> 
    <StyleInclude Source="/Styles/SideBar.xaml" />
	-->
	  
	 <!-- Avalonia 0.11 -->
	  <!--
	<FluentTheme/>
	-->
	  <StyleInclude Source="avares://Avalonia.Controls.ColorPicker/Themes/Simple/Simple.xaml" />


	  <!-- SARGAME Style Classes -->

	  
	  <Style Selector="TextBlock.property">
		  <Setter Property="FontSize" Value="10"/>
		  <Setter Property="HorizontalAlignment" Value="Right"/>
		  <Setter Property="FontWeight" Value="Medium"/>
		  <Setter Property="Margin" Value="0,0,5,0"/>
		  <Setter Property="TextAlignment" Value="Center"/>
	  </Style>

	  <Style Selector="TextBox.property">
		  <Setter Property="FontSize" Value="10"/>
		  <Setter Property="FontWeight" Value="Medium"/>
		  <Setter Property="Margin" Value="5"/>
		  <Setter Property="TextAlignment" Value="Center"/>
	  </Style>

	  <Style Selector="CheckBox.property">
		  <Setter Property="Margin" Value="5"/>
	  </Style>

	  <Style Selector="NumericUpDown.property">
		  <Setter Property="Margin" Value="5"/>
	  </Style>

	  <Style Selector="Viewbox.toolicon">
		  <Setter Property="Width" Value="16"/>
		  <Setter Property="Margin" Value="5"/>
	  </Style>

	  <materialIcons:MaterialIconStyles />
	  
	  <dialogHostAvalonia:DialogHostStyles />
	  
  </Application.Styles>

	<Application.Resources>
		<!-- SUKIUI -->
		<Color x:Key="SukiPrimaryColor">#1EB1FF</Color>
		<Color x:Key="SukiPrimaryColorGradient">#1EB1FF</Color>
		<Color x:Key="SukiLightBackground">#0F0F1E</Color>
		

		<!-- Override of Dark theme -->
		<!--
		<Color x:Key="SukiPrimaryColorGradient">#2f54eb</Color>
		<Color x:Key="SukiPrimaryColor">#597ef7</Color>
		<Color x:Key="SukiLightBackground">#393939</Color>
		-->

		<Color x:Key="SukiBackground">#0F0F1E</Color>
		<Color x:Key="SukiCardBackground">#0F0F1E</Color>

		<Color x:Key="SukiLightColoredBackground">#393939</Color>
		<Color x:Key="SukiStrongBackground">#444444</Color>
		<Color x:Key="SukiControlTouchBackground">#505050</Color>
		<Color x:Key="SukiText">#dddddd</Color>
		<Color x:Key="SukiBorderBrush">#454545</Color>
		<Color x:Key="SukiControlBorderBrush">#505050</Color>
		<Color x:Key="SukiMediumBorderBrush">#494949</Color>
		<Color x:Key="SukiLightBorderBrush">#444444</Color>

		<Color x:Key="ThemeBackgroundBrush">Black</Color>
		<Color x:Key="ThemeForegroundBrush">White</Color>

		<!-- SARGAME -->
		<Color x:Key="ExpanderBackground">#25254B</Color>
		<Color x:Key="FakeBackground">#00000001</Color>
		<Color x:Key="RedColor">#FF0000</Color>
		<Color x:Key="FieldGroupHeader">#531EB1FF</Color>
		
		
		
	</Application.Resources>
</Application>
