using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Skia;
using ReactiveUI;
using SkiaSharp;
using System.ComponentModel;
using PropertyChanged;
using System.IO;
using Avalonia.LogicalTree;
using Avalonia.Controls.Primitives;
using System;
using Avalonia.Interactivity;
using System.Collections.Generic;
using Tabula.PMCore;

namespace Tabula.PWGClient
{
    [DoNotNotify]
    [AddINotifyPropertyChangedInterface]
    public partial class BoolCardsField : UserControl
    {
        public BoolCardsField()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        async void bt_true(object sender, RoutedEventArgs args)
        {
			var view = (BoolCardsFieldView) DataContext;
            view.value = true;
		}

		async void bt_false(object sender, RoutedEventArgs args)
		{
			var view = (BoolCardsFieldView) DataContext;
			view.value = false;
		}

		protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
		{

			base.OnPropertyChanged(change);
		}

        protected override void OnLoaded(RoutedEventArgs e)
        {
            base.OnLoaded(e);
        }

        
    }
}
