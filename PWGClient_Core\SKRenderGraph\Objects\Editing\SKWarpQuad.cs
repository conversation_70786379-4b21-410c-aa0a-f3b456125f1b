﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.SKRenderGraph;

// A 4 corners quad that warps a bitmap in non-affine perspective

namespace Tabula.SKRenderGraph
{
    public class SKWarpQuad : SKObject
    {
        public SKImage Image = null;  // if valorized will warp this bitmap
        public float Opacity = 1f;

        public SKPoint Center = new SKPoint(0, 0);              // pivot point

        public List<SKPoint> Vertices { get; protected set; } = new List<SKPoint>();    // clockwise ordered points, coordinates are relative to pivot 

        private SKPath path = new SKPath();

        public override bool Contains(SKPoint pos) => path.Contains(pos.X, pos.Y);

        // Events
        public Action<SKControlPoint, int, SKPoint> OnVertexMove;   // <control_point>,<vertex_idx>,<pos>


        public SKWarpQuad(SKScene scene, SKRect rect) : base(scene)
        {
            Color = new SKColor(0, 0, 0, 200);
            ColorHover = new SKColor(0, 0, 255, 200);
            ColorDragging = new SKColor(255, 255, 255, 200);

            Center = new SKPoint(0, 0);

            // Vertices are alway 4, add the controlpoints
            Vertices = new List<SKPoint>()
            {
                new SKPoint(rect.Left,rect.Top), new SKPoint(rect.Right,rect.Top), new SKPoint(rect.Right,rect.Bottom), new SKPoint(rect.Left,rect.Bottom)
            };

            for (int i = 0; i < Vertices.Count; i++)
            {
                var cp = new SKControlPoint(Scene);
                cp.IsEnabled = false;
                cp.onMove += (s, pos_rel) => OnVertexMove?.Invoke(cp, i, pos_rel);
                ControlPoints.Add(cp);
                Scene.Add(cp, Parent);
            }

            SyncToControlPoints();
        }

        public void Clear()
        {
            Vertices.Clear();
        }

        public override SKPoint GetPosition() => Center;

        public override SKSize GetSize()
        {
            var size = new SKSize()
            {
                Width = path.Bounds.Width,
                Height = path.Bounds.Height
            };

            return size;

            //throw new NotImplementedException("Implement warpquad getsize");
        }

        public void SetVertices(List<SKPoint> verts)
        {
            Vertices = verts;
            SyncToControlPoints();
        }

        // Since vertexes are relative to center
        public SKPoint GetVertexPosition(int index) => Center + Vertices[index];

        public void SetVertexPosition(int index, SKPoint world_pos) => Vertices[index] = world_pos - Center;

        public override void SyncFromControlPoints()
        {
            for (int i = 0; i < Vertices.Count; i++)
                SetVertexPosition(i, ControlPoints[i].GetPosition());
        }

        public override void SyncToControlPoints()
        {
            for (int i = 0; i < Vertices.Count; i++)
                ControlPoints[i].SetPosition(GetVertexPosition(i));
        }

        public override void SetPosition(SKPoint pos)
        {
            Center = pos;

            SyncToControlPoints();

            base.SetPosition(pos);
        }

        public override void Move(SKPoint pos_rel)
        {
            Center.Offset(pos_rel);

            SyncToControlPoints();

            base.Move(pos_rel);
        }

        public override void Update()
        {
            SyncFromControlPoints();

            if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsMouseOver)
            {
                Paint.Color = ColorHover;

                if (Scene.MouseLeftPressed)
                {
                    DragStart();
                }
            }
            else
            {
                Paint.Color = Color;
            }


            foreach (var cp in ControlPoints)
                cp.IsEnabled = IsSelected;

            path.Reset();

            // Origin on first vertex
            path.MoveTo(GetVertexPosition(0));

            for (int i = 1; i < Vertices.Count; i++)
                path.LineTo(GetVertexPosition(i));

            path.Close();

            // UpdateContours();
        }

        /*
        public override void UpdateContours()
        {
            foreach (var s in Segments)
                s.Contour.Update();
        }
        */

        public override void Draw()
        {
            if (Image == null)
            {
                Canvas.DrawPath(path, Paint);
            }
            else
            {
                // warp the bitmap

                // order in function is
                // 0: upper-left
                // 1: upper-right
                // 2: lower-left
                // 3: lower-right

                // So: 0,1,3,2

                var bitmapSize = new SKSize(Image.Width, Image.Height);
                var matrix = ComputeMatrix(bitmapSize, GetVertexPosition(0), GetVertexPosition(1),
                                                   GetVertexPosition(3), GetVertexPosition(2));

                Canvas.Save();
                var prev_matrix = Canvas.TotalMatrix;
                Canvas.SetMatrix(prev_matrix.PreConcat(matrix));
                Canvas.DrawImage(Image, 0, 0, new SKPaint() { IsAntialias = true, FilterQuality = SKFilterQuality.High, Color = (SKColor)new SKColorF(1, 1, 1, Opacity) });
                Canvas.Restore();
            }

            /*
            if (IsSelected)
            {
                // Contours always drawn/hitchecked, but they are transparent when not hover
                foreach (var s in Segments)
                    s.Contour.Draw();
            }

            // some segments may have other drawing to do (controlpoints lines)
            if (IsSelected)
            {
                Paint.Color = ColorAnchorLines;
                foreach (var s in Segments)
                    s.LastDraw(Canvas, Paint);
            }
            */
        }

        static SKMatrix ComputeMatrix(SKSize size, SKPoint ptUL, SKPoint ptUR, SKPoint ptLL, SKPoint ptLR)
        {
            // Scale transform
            SKMatrix S = SKMatrix.CreateScale(1 / size.Width, 1 / size.Height);

            // Affine transform
            SKMatrix A = new SKMatrix
            {
                ScaleX = ptUR.X - ptUL.X,
                SkewY = ptUR.Y - ptUL.Y,
                SkewX = ptLL.X - ptUL.X,
                ScaleY = ptLL.Y - ptUL.Y,
                TransX = ptUL.X,
                TransY = ptUL.Y,
                Persp2 = 1
            };

            // Non-Affine transform
            SKMatrix inverseA;
            A.TryInvert(out inverseA);
            SKPoint abPoint = inverseA.MapPoint(ptLR);
            float a = abPoint.X;
            float b = abPoint.Y;

            float scaleX = a / (a + b - 1);
            float scaleY = b / (a + b - 1);

            SKMatrix N = new SKMatrix
            {
                ScaleX = scaleX,
                ScaleY = scaleY,
                Persp0 = scaleX - 1,
                Persp1 = scaleY - 1,
                Persp2 = 1
            };

            // Multiply S * N * A
            SKMatrix result = SKMatrix.CreateIdentity();
            SKMatrix.PostConcat(ref result, S);
            SKMatrix.PostConcat(ref result, N);
            SKMatrix.PostConcat(ref result, A);

            return result;
        }
    }
}
