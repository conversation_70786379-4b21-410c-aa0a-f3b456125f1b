﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

namespace Tabula.SKRenderGraph
{
    public class SKRectangle : SKObject
    {
        public SKRect Rect = SKRect.Empty;
       
        public override bool Contains(SKPoint pos) => Rect.Contains(pos);

        public SKRectangle(SKScene scene, SKRect rect) : base(scene)
        {
            Rect = rect;
        }

        public override SKPoint GetPosition() => new SKPoint(Rect.MidX, Rect.MidY);

        public override void SetPosition(SKPoint pos)
        {
            // NOTE: rectangle location is top-left
            Rect.Location = pos - new SKPoint((float)Rect.Width / 2f, (float)Rect.Height / 2f);

            base.SetPosition(pos);
        }

        public override void SetSize(SKSize size)
        {
            Rect.Size = size;

            base.SetSize(size);
        }

        public override SKSize GetSize() => Rect.Size;

        public override SKRect GetBoundingBox() => Rect;

		public override void Move(SKPoint pos_rel)
        {
            Rect.Offset(pos_rel);

            base.Move(pos_rel);
        }

        public override void Update()
        {
            if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsMouseOver)
            {
                Paint.Color = ColorHover;

                if (Scene.MouseLeftPressed)
                {
                    DragStart();
                }
            }
            else
                Paint.Color = Color;

            base.Update();
        }

        public override void Draw()
        {
            Canvas.DrawRect(Rect, Paint);

            base.Draw();
        }
    }
}
