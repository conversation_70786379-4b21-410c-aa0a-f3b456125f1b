<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"        
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.ProjectionPage">
	<Border Classes="Card" Margin="7,7,-5,8">
		<Grid ColumnDefinitions="Auto,*">

			<!-- Tools -->
			<StackPanel Grid.Column="0" Orientation="Vertical" Margin="0,0,10,0">
				<!--
				<ToggleButton IsChecked="{Binding DrawCursor}" Margin="2" Background="Gray">
					<ToggleButton.Styles>
						<Style Selector="ToggleButton Image.tbchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton Image.tbunchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbunchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
					</ToggleButton.Styles>
					<Panel>
						<Image Source="/Images/buttons/cursor.png" Width="30" Height="30" Classes="tbunchecked"/>
						<Image Source="/Images/buttons/cursor.png" Width="30" Height="30" Classes="tbchecked"/>
					</Panel>
				</ToggleButton>
				-->
					
				<Button HorizontalAlignment="Center" Margin="0"  Padding="5" ToolTip.Tip="Fit into view">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="Fit"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/fit.png" Width="25" Height="25" />
				</Button>
					
				<Button HorizontalAlignment="Center" Margin="0" Padding="5" ToolTip.Tip="Zoom out">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="ZoomOut"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/zoom_in.png" Width="25" Height="25"/>
				</Button>

				<Button HorizontalAlignment="Center" Margin="0" Padding="5" ToolTip.Tip="Zoom in">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="ZoomIn"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/zoom_out.png" Width="25" Height="25"/>
				</Button>

				<Button HorizontalAlignment="Center" Margin="0,20,0,0" Padding="5" ToolTip.Tip="Reset projection warp"
						Click="bt_reset_click">
					<Image Source="/Images/buttons/reset.png" Width="25" Height="25"/>
				</Button>

				<!--
				<ToggleButton IsChecked="{Binding PanToggle}" Margin="10" Background="Gray">
					<ToggleButton.Styles>
						<Style Selector="ToggleButton Image.tbchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton Image.tbunchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbunchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
					</ToggleButton.Styles>
					<Panel>
						<Image Source="/Images/buttons/pan.png" Width="30" Height="30" Classes="tbunchecked"/>
						<Image Source="/Images/buttons/pan.png" Width="30" Height="30" Classes="tbchecked"/>
					</Panel>
				</ToggleButton>
				-->
					
				<!--
				<Button HorizontalAlignment="Center" Margin="10" Background="Gray">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="SendKey_Delete"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/delete.png" Width="30" Height="30"/>
				</Button>
				-->

			</StackPanel>

			<!-- View -->
			<local:ScreenEditorPanel Name="screen_editor_panel" Grid.Column="1" x:Name="screen_editor" Background="Transparent"/>

			<!-- Modes -->
			<StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Top" HorizontalAlignment="Center">

				<Button Name="bt_warp" Background="{Binding WarpButtonColor}" MinWidth="100" Margin="0,5,5,5" VerticalAlignment="Center" HorizontalContentAlignment="Center" Click="bt_warp_click">
					<TextBlock Text="Warp"/>
				</Button>
				<Button Name="bt_region" Background="{Binding RegionButtonColor}" Margin="5" MinWidth="100" VerticalAlignment="Center" HorizontalContentAlignment="Center" Click="bt_region_click">
					<TextBlock Text="Region"/>
				</Button>
				<Button Name="bt_calibrate" Background="{Binding CalibrateButtonColor}" Margin="5" MinWidth="100" VerticalAlignment="Center" HorizontalContentAlignment="Center" Click="bt_calibrate_click">
					<TextBlock Text="Calibration"/>
				</Button>
			</StackPanel>
		</Grid>
	</Border>

</UserControl>
