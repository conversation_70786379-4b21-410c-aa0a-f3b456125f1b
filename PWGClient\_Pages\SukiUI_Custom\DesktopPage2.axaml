<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="SukiUI.Controls.DesktopPage2"
    xmlns="https://github.com/avaloniaui"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dialogHostAvalonia="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
    xmlns:material="using:Material.Icons.Avalonia"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:suki="clr-namespace:SukiUI.Controls"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Styles>
        <Style Selector="suki|DesktopPage2">
            <Setter Property="Template">
                <ControlTemplate>


                    <DockPanel>

                        <Border
                            BorderBrush="{DynamicResource SukiBorderBrush}"
                            BorderThickness="0,0,0,1.5"
                            DockPanel.Dock="Top" Height="40">
                            <Grid>
                                <Grid
                                    Background="#1777FF"
                                    ColumnDefinitions="Auto,*,Auto"
                                    IsHitTestVisible="False"
                                    RowDefinitions="Auto,Auto">
									<Image  Source="{TemplateBinding LogoImage}"
										    Grid.Column="0"
										    Height="25"
											 HorizontalAlignment="Left"
										VerticalAlignment="Center"
                                        IsHitTestVisible="False"
                                        Margin="5"
                                        Width="25"
									/>
									<!--
                                    <material:MaterialIcon
                                        Foreground="{TemplateBinding LogoColor}"
                                        Grid.Column="0"
                                        Height="25"
                                        HorizontalAlignment="Left"
										VerticalAlignment="Center"
                                        IsHitTestVisible="False"
                                        Kind="{TemplateBinding LogoKind}"
                                        Margin="5"
                                        Width="25" />
										-->
                                    <TextBlock
                                        FontSize="{TemplateBinding TitleFontSize}"
                                        FontWeight="{TemplateBinding TitleFontWeight}"
                                        Grid.Column="1"
                                        HorizontalAlignment="{TemplateBinding TitleHorizontalAlignment}"
                                        IsHitTestVisible="False"
                                        Text="{TemplateBinding Title}"
                                        VerticalAlignment="Center" />
                                    <Menu
                                        Grid.ColumnSpan="3"
                                        Grid.Row="1"
                                        IsHitTestVisible="True"
                                        IsVisible="{TemplateBinding MenuVisibility}"
                                        ItemsSource="{TemplateBinding MenuItems}"
                                        Margin="0,-5,0,0" />


                                </Grid>
                                <DockPanel
                                    Grid.Column="2"
                                    HorizontalAlignment="Right"
                                    Margin="0,0,5,0">

                                    <!--  Minimize button  -->
                                    <Button
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Classes="Accent"
                                        Click="MinimizeHandler"
                                        Height="23"
                                        IsVisible="{TemplateBinding IsMinimizeButtonEnabled}"
                                        Margin="0,2,1,0"
                                        Padding="0"
                                        Width="23">
                                        <material:MaterialIcon
                                            Foreground="White"
                                            Height="14"
                                            Kind="WindowMinimize"
                                            Margin="0,5,0,0"
                                            Width="14" />
                                    </Button>
                                    <!--  Maximize button  -->
                                    <Button
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Classes="Accent"
                                        Click="MaximizeHandler"
                                        Height="23"
                                        IsVisible="{TemplateBinding IsMaximizeButtonEnabled}"
                                        Margin="0,2,1,0"
                                        Padding="0"
                                        Width="23">
                                        <material:MaterialIcon
                                            Foreground="White"
                                            Height="14"
                                            Kind="WindowRestore"
                                            Width="14" />
                                    </Button>
                                    <!--  Close button  -->

                                    <Button
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Classes="Accent"
                                        Click="CloseHandler"
                                        Height="23"
                                        IsHitTestVisible="True"
                                        Margin="0,9,15,9"
                                        Padding="0"
                                        Width="25">
                                        <material:MaterialIcon
                                            Foreground="White"
                                            Height="23"
                                            Kind="CloseCircleOutline"
                                            Width="23" />

                                    </Button>
                                </DockPanel>
                            </Grid>
                        </Border>

                        <ContentControl Content="{TemplateBinding Content}" />

                    </DockPanel>


                </ControlTemplate>
            </Setter>
        </Style>


    </UserControl.Styles>
</UserControl>
