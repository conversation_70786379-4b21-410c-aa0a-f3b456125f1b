<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
			 xmlns:pmcore="clr-namespace:Tabula.PMCore;assembly=PWGClient_Core"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 Width="{Binding DialogMediumSize.Width}" Height="{Binding DialogMediumSize.Height}"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.StructureEffectAddDialog" >

	<dialogHost:DialogHost Identifier="structure_effect_add_dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True">
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

		<Border BorderBrush="White" BorderThickness="1" Background="{DynamicResource ExpanderBackground}">
			<Grid RowDefinitions="Auto,*,Auto">
				<StackPanel Grid.Row="0"  Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
					<materialIcons:MaterialIcon Width="30" Height="30" Kind="Flare"/>
					<Label  Content="Add Structure Effect" FontSize="14" FontWeight="Bold" Margin="10" HorizontalAlignment="Center"/>
				</StackPanel>

				<Button HorizontalAlignment="Right" VerticalAlignment="Top" Margin="10" Padding="5" ToolTip.Tip="Close"
						Click="bt_closedialog_click">
					<materialIcons:MaterialIcon Kind="Close" Classes="Basic" Width="30" Height="30"/>
				</Button>

				<ScrollViewer  Grid.Row="1" Margin="10" Padding="0,10,10,0" Background="{DynamicResource ExpanderBackground}">
					<ItemsControl ItemsSource="{Binding EntitiesToCreate_StructureEffects}">

						<ItemsControl.ItemsPanel>
							<ItemsPanelTemplate>
								<WrapPanel/>
							</ItemsPanelTemplate>
						</ItemsControl.ItemsPanel>

						<ItemsControl.ItemTemplate>
							<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
								<Grid RowDefinitions="Auto,Auto" Margin="5">
									<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
										<Grid>
											<local:UserBitmap Path="{Binding Icon}"
															  Width="60"
															  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
										</Grid>
									</Button>
									<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
										<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
									</Grid>
								</Grid>
							</DataTemplate>
						</ItemsControl.ItemTemplate>

					</ItemsControl>
				</ScrollViewer>

				<!--
				<TabControl Grid.Row="1" Margin="10" 
						Name="structure_effect_add_tab"
						Padding="0,10,10,0"
						Focusable="False" KeyboardNavigation.TabNavigation="None"
						Background="{DynamicResource ExpanderBackground}"
							TabIndex="1">

					<TabItem Name="tab_effects_face" Header="Face" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_StructureFaceEffects}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}" 
																	  Width="60"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>		
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<TabItem Name="tab_effects_edge" Header="Effects" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_StructureEdgeEffects}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="256" Height="144">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="200" Height="112"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					
			</TabControl>
				-->
				
			</Grid>

		</Border>
	</dialogHost:DialogHost>

</UserControl>
