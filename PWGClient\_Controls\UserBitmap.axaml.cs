using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Skia;
using ReactiveUI;
using SkiaSharp;
using System.ComponentModel;
using PropertyChanged;
using System.IO;
using Avalonia.LogicalTree;
using Avalonia.Controls.Primitives;
using System;
using Avalonia.Interactivity;
using System.Collections.Generic;

namespace Tabula.PWGClient
{
    [DoNotNotify]
    [AddINotifyPropertyChangedInterface]
    public partial class UserBitmap : UserControl
    {
        // TEST: support local caching indexed by path and width
        // bitmap seem to disappear??!?
        private static Dictionary<(string path,int width, int height), Bitmap> _bitmap_cache = new Dictionary<(string path, int width, int height), Bitmap>();


        public UserBitmap()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public static readonly StyledProperty<string?> PathProperty =
                    AvaloniaProperty.Register<UserBitmap, string?>(nameof(Path));

        public string? Path
        {
            get => GetValue(PathProperty);
            set => SetValue(PathProperty, value);
        }

        public static readonly StyledProperty<double?> DecodeWidthProperty =
                   AvaloniaProperty.Register<UserBitmap, double?>(nameof(DecodeWidth));

        public double? DecodeWidth
        {
            get => GetValue(DecodeWidthProperty);
            set => SetValue(DecodeWidthProperty, value);
        }

		public static readonly StyledProperty<double?> DecodeHeightProperty =
				   AvaloniaProperty.Register<UserBitmap, double?>(nameof(DecodeHeight));

		public double? DecodeHeight
		{
			get => GetValue(DecodeHeightProperty);
			set => SetValue(DecodeHeightProperty, value);
		}

		protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
		{
            if (change.Property == PathProperty)
            {
                RefreshBitmap();
            }

			base.OnPropertyChanged(change);
		}

		#region INotifyPropertyChanged
		/*
        protected void RaisePropertyChanged(PropertyChangedEventArgs args)
        {
            ((IReactiveObject)this).RaisePropertyChanged(args);
        }

        protected void RaisePropertyChanged(string name)
        {
            ((IReactiveObject)this).RaisePropertyChanged(name);
        }
        */
		#endregion

		private Bitmap bitmap;

        private double Aspect;
        private double FinalWidth;
        private double FinalHeight;

        protected override void OnLoaded(RoutedEventArgs e)
        {
            base.OnLoaded(e);

            RefreshBitmap();
        }
		
		public void RefreshBitmap()
        {
			/*
            if (bitmap != null)
            {
                bitmap.Dispose();
                bitmap = null;
            }
            */

            // If just one size value is provided calculate the other

			var requested_width = (int)(DecodeWidth == null || double.IsNaN((double)DecodeWidth) ? (double.IsNaN(Width) ? -1 : Width) : DecodeWidth);
			var requested_height = (int)(DecodeHeight == null || double.IsNaN((double) DecodeHeight) ? (double.IsNaN(Height) ? -1 : Height) : DecodeHeight);

			if (!string.IsNullOrEmpty(Path))
            {
                if (!_bitmap_cache.TryGetValue((Path, requested_width, requested_height), out bitmap))
                {
                    bitmap = LoadBitmap(Path, width: requested_width, height: requested_height);
                    _bitmap_cache.Add((Path, requested_width, requested_height), bitmap);
				}
                else
                {
                    // cache hit!
                }

                if (bitmap != null)
                {
					Aspect = (double)bitmap.PixelSize.Width / (double)bitmap.PixelSize.Height;

					FinalWidth = double.IsNaN(Width) ? (!double.IsNaN(Height) ? Height * Aspect : double.NaN) : Width;
					FinalHeight = double.IsNaN(Height) ? (!double.IsNaN(Width) ? Width / Aspect : double.NaN) : Height;
				}
                else
                {
                    // error!
                }
                
            }

            // Important: always set both object widht / height or it won't render
            Width = FinalWidth;
            Height = FinalHeight;

            InvalidateVisual();
		}
        
        /*
		protected override void OnDataContextChanged(EventArgs e)
		{            
			base.OnDataContextChanged(e);

			RefreshBitmap();
		}
        */

		public override void Render(DrawingContext context)
        {
            if (bitmap != null)
            {
				context.DrawImage(bitmap,
                    new Rect(0, 0, bitmap.PixelSize.Width, bitmap.PixelSize.Height),
                    new Rect(0, 0, FinalWidth, FinalHeight)
                    );
            }
        }

        public static Bitmap LoadBitmap(string path, int width=-1, int height=-1)
        {
            try
            {
                // check cache first
                byte[] data = App.GetIcon(path);

                
                //if (!App.Icons.TryGetValue(path, out data))
                if (data == null)
                {
                    // check filesystem
                    if (File.Exists(path))
                    {
                        data = File.ReadAllBytes(path);
                    }
                }


                if (data != null)
                    if (width == -1 && height == -1)
                        return new Bitmap(new MemoryStream(data));
                    else
                    {
                        /*
                        var original_bmp = new Bitmap(new MemoryStream(data));

						var aspect = (double)original_bmp.PixelSize.Width / (double)original_bmp.PixelSize.Height;

						var final_width = width==-1 ? (height!=-1 ? (int) ((double) height * aspect) : -1) : width;
						var final_height = height == -1 ? (width != -1 ? (int)((double)width / aspect) : -1) : height;

                        if (final_width != -1 && final_height != -1)
                            return original_bmp.CreateScaledBitmap(new PixelSize(final_width, final_height));
                        else
                            return original_bmp;
                        */

                        // more optimized
                        if (width != -1)
                            return Bitmap.DecodeToWidth(new MemoryStream(data), width);
                        else if (height != -1)
                            return Bitmap.DecodeToHeight(new MemoryStream(data), height);
                    }
            }
            catch
            {

            }

            return null;
        }
    }
}
