﻿<UserControl xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ext="clr-namespace:Avalonia.ExtendedToolkit.Controls;assembly=Avalonia.ExtendedToolkit"
		xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"			 
		xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
        xmlns:local="clr-namespace:Tabula.PWGClient"
		xmlns:core="clr-namespace:Tabula.PWGClient;assembly=PWGClient_Core"
		xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
		xmlns:siv="https://github.com/kekyo/SkiaImageView"
        xmlns:pmcore="clr-namespace:Tabula.PMCore;assembly=PWGClient_Core"
        xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
        xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"        
		mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="Tabula.PWGClient.MainView"
		>

	<dialogHost:DialogHost Identifier="dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True"> 
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

	<Grid ColumnDefinitions="270,*,10,300" RowDefinitions="0,*,20" Background="{StaticResource ExpanderBackground}">

		<!-- Menu -->
		<!--
		<DockPanel Grid.Row="0" Grid.ColumnSpan="4">
			<Menu DockPanel.Dock="Top">
				<MenuItem Header="_File">
					<MenuItem Header="_Open..."/>
					<Separator />
					<MenuItem Header="_Exit"/>
				</MenuItem>
				<MenuItem Header="_Edit">
					<MenuItem Header="Copy"/>
					<MenuItem Header="Paste"/>
				</MenuItem>
			</Menu>
		</DockPanel>
		-->

			<!-- Left Panel -->
		<Border Classes="Card" Grid.Row="1" Grid.Column="0" Margin="10,10,0,7" >
			<Grid RowDefinitions="Auto,Auto,Auto,*">
				<!--
				<Grid.Background>
					<LinearGradientBrush  StartPoint="0%,0%" EndPoint="100%,0%" >
						<GradientStop Offset="0" Color="Black" />
						<GradientStop Offset="1" Color="{DynamicResource ExpanderBackground}" />
					</LinearGradientBrush>
				</Grid.Background>
				-->


				<!-- Logo -->				
				<StackPanel Grid.Row="0" Orientation="Vertical">
					<Image Source="/Images/logo.png" Margin="0,-15,0,0" 
						   RenderOptions.BitmapInterpolationMode="HighQuality" />

					<!-- Running Module Thumb -->
					<Button Margin="10,10,10,10" Width="200" Height="112" Click="bt_runningmodule_thumb_Click"
							ToolTip.Tip="Click here to make sure the game window is receiving input!">
						<Grid>
							<Image  Source="/Images/screen.png"  VerticalAlignment="Center" RenderOptions.BitmapInterpolationMode="HighQuality" />
							<Image  Source="{Binding RunningModule.ThumbnailPath, Converter={StaticResource BitmapValueConverter}}" RenderOptions.BitmapInterpolationMode="HighQuality"/>
							<!-- FOCUS -->
							<Border Classes="Card" HorizontalAlignment="Stretch" Background="{StaticResource SukiPrimaryColor}" VerticalAlignment="Bottom"
									Padding="0" Margin="15,0,15,5"
									Height="30">
								<Border.IsVisible>
									<MultiBinding Converter="{x:Static BoolConverters.And}">
										<Binding Path="IsConnected"/>
										<Binding Path="!IsApplicationInFocus"/>
									</MultiBinding>
								</Border.IsVisible>								
								<Grid>
									<TextBlock Text="FOCUS" Foreground="White" FontWeight="Bold" VerticalAlignment="Center" HorizontalAlignment="Center" TextAlignment="Center" FontSize="16">
										<TextBlock.Styles>
											<Style Selector="TextBlock">
												<Style.Animations>
													<Animation Duration="0:0:2" IterationCount="INFINITE">
														<KeyFrame Cue="0%">
															<Setter Property="Opacity" Value="0.3"/>
														</KeyFrame>
														<KeyFrame Cue="50%">
															<Setter Property="Opacity" Value="1.0"/>
														</KeyFrame>
														<KeyFrame Cue="100%">
															<Setter Property="Opacity" Value="0.3"/>
														</KeyFrame>
													</Animation>
												</Style.Animations>
											</Style>
										</TextBlock.Styles>
									</TextBlock>
								</Grid>
							</Border>
						</Grid>
					</Button>

					

				
					<!-- JOIN AuthCode -->
					<Border Classes="Card" HorizontalAlignment="Stretch" Background="DarkBlue"
							Padding="0" Margin="15,0,15,5"
							Height="30" IsVisible="{Binding HasAuthCodes}"
							ToolTip.Tip="Use the join code on the Mobile Controller to join the game"
							PointerPressed="joincode_PointerPressed">
						<Grid ColumnDefinitions="50*,50*">
							<TextBlock Grid.Row="0" Grid.Column="0"  Classes="property" Text="Join Code:" VerticalAlignment="Center"
									   HorizontalAlignment="Right" TextAlignment="Right" FontSize="12"/>
							<TextBlock Grid.Row="0" Grid.Column="1"  Text="{Binding AuthCode}" Margin="10,0,0,0" VerticalAlignment="Center"
									   HorizontalAlignment="Left" TextAlignment="Left" FontFamily="Courier" FontSize="16" FontWeight="Bold"/>
						</Grid>
					</Border>

					<!-- Busy -->
					<ProgressBar IsIndeterminate="True" HorizontalAlignment="Stretch" Opacity="{Binding IsBusyOpacity}" />
					
					<!-- Save / Close / Connect -->
					<StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
						<Button Width="50" Height="50" Margin="0" Padding="0" Content="{materialIcons:MaterialIconExt Kind=ContentSave}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Click="bt_save_project_Click" IsVisible="{Binding IsConnected}"  ToolTip.Tip="Save Project"/>
						<Button Width="50" Height="50" Margin="0" Padding="0" Content="{materialIcons:MaterialIconExt Kind=CloseCircle}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"  Click="bt_stop_module_Click" IsVisible="{Binding IsModuleProcessRunning}"  ToolTip.Tip="Stop running module"/>
						<Button Width="50" Height="50" Margin="0" Padding="0" Content="{materialIcons:MaterialIconExt Kind=Replay}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"  Click="bt_reset_module_Click" IsVisible="{Binding IsGame}"  ToolTip.Tip="Reset game"/>
						<Button Width="50" Height="50" Margin="0" Padding="0" Content="{materialIcons:MaterialIconExt Kind=Connection}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"  Click="bt_connect_server_Click" IsVisible="{Binding IsModuleProcessRunningAndNotConnected}" ToolTip.Tip="Connect"/>
						<Button Width="50" Height="50" Margin="0" Padding="0" Classes="Primary" Content="{materialIcons:MaterialIconExt Kind=Connection}" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"  Click="bt_connect_server_Click" IsVisible="{Binding IsModuleProcessRunningAndConnected}" ToolTip.Tip="Disconnect"/>
					</StackPanel>
					
					
				</StackPanel>

				

				<!-- Buttons -->
				<!--
				<Expander Grid.Row="2" IsVisible="{Binding IsConnected}" Header="Actions">
					<StackPanel>

						<Grid  ColumnDefinitions="*,*">
							<Button Grid.Column="0" x:Name="bt_load_project" Content="Load Project"  Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Click="bt_load_project_Click"/>
							<Button Grid.Column="1" x:Name="bt_load_wall" Background="Orange" Content="Load Wall"  Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Click="bt_load_wall_Click"/>
						</Grid>

						<Grid  ColumnDefinitions="*,*">
							<Button Grid.Column="0" x:Name="bt_download_project" Content="Download"  Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Click="bt_download_project_Click"/>

							<Button Grid.Column="1" x:Name="bt_save_project" Content="Save" Background="Red"  Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_model_save}">
										<local:CallMethodActionAsync TargetObject="{Binding View}" MethodName="SaveModel"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>
						</Grid>

						<Grid  ColumnDefinitions="*,*,*">
							<Button Grid.Column="0" x:Name="bt_model_edit" Content="Edit" Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_model_edit}">
										<ia:CallMethodAction TargetObject="{Binding View}" MethodName="ChangeModeToEdit"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>
							<Button Grid.Column="1" x:Name="bt_model_play" Content="Play" Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_model_play}">
										<ia:CallMethodAction TargetObject="{Binding View}" MethodName="ChangeModeToPlay"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>
							<Button Grid.Column="2" x:Name="bt_model_join" Content="Join" Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center">
								<i:Interaction.Behaviors>
									<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_model_join}">
										<ia:CallMethodAction TargetObject="{Binding View}" MethodName="PlayerJoin"/>
									</ia:EventTriggerBehavior>
								</i:Interaction.Behaviors>
							</Button>

						</Grid>

						<Button Classes="Primary" Click="bt_next_background_Click" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center">
							<TextBlock>Next Background</TextBlock>
						</Button>

						<Slider Value="{Binding View.Screen.background_mask_opacity, Mode= TwoWay}" Minimum="0" Maximum="1" TickFrequency="0.01" SmallChange="0.01"/>

						<Slider Value="{Binding BackgroundScale}" Minimum="0.5" Maximum="4" TickFrequency="0.01" SmallChange="0.01"/>

						<Button Classes="Primary" Content="Calibrate" Click="bt_calibrate_Click" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"/>

						<Button Classes="Primary" Content="Next Scene" Click="bt_next_scene_Click" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"/>
						<Grid  ColumnDefinitions="*,60">
							<ComboBox Grid.Column="0" ItemsSource="{Binding SceneNames^}" SelectedItem="{Binding SelectedSceneName}" FontSize="10" Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center"/>
							<Button Grid.Column="1" Background="Orange" Content="Load" FontSize="10" FontWeight="Bold"  Margin="5" HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" Click="bt_load_scene_Click"/>
						</Grid>

					</StackPanel>
				</Expander>
				-->
				
				<!-- Scrollable Hierarchy -->
				<ScrollViewer Grid.Row="3" Margin="0,10,0,0">
					<TreeView x:Name="hierarchy" 
							  ItemsSource="{Binding Hierarchy}" 
							  SelectedItem="{Binding SelectedItem, Mode=OneWay}" 
							  SelectionMode="Single"
							  SelectionChanged="hierarchy_SelectionChanged">

						<TreeView.Styles>
							<Style Selector="TreeViewItem">
								<Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
								<Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>								
								<Setter Property="Padding" Value="{Binding Padding}"/>								
							</Style>
						</TreeView.Styles>

						<!-- HIERARCHY DATATEMPLATES, Inspector ones are in App.xml -->
						<TreeView.DataTemplates>

							<!-- FIX: (preview5) EntityView alone was not working as Items were not used... 
								 Used {x:Type local:HierarchyEntityView} and use View.name etc..
								 Also create right HierarchyEntityView item in MainWindowUI.cs:220
							-->
							<!--
							<TreeDataTemplate DataType="{x:Type local:HierarchyEntityView}" ItemsSource="{Binding Items}">
								<Grid ColumnDefinitions="Auto,*">
									<Grid.ContextMenu>
										<ContextMenu ItemsSource="{Binding MenuItems}"/>
									</Grid.ContextMenu>
									<local:UserBitmap Grid.Column="0" Width="16" Height="16" Path="{Binding View.icon}" VerticalContentAlignment="Center" IsVisible="{Binding View.HasIcon}" Margin="0,0,10,0"/>
									<Border Grid.Column="1" HorizontalAlignment="Stretch" Background="Transparent">
										<TextBlock Text="{Binding View.name}" FontWeight="{Binding View.FontWeight}" VerticalAlignment="Center" HorizontalAlignment="Stretch" TextAlignment="Left"/>
									</Border>
								</Grid>
							</TreeDataTemplate>
							-->

							<!-- HierarchyItem, catches undefined items -->
							<TreeDataTemplate DataType="core:HierarchyItem" ItemsSource="{Binding Items}">
								<Grid HorizontalAlignment="Stretch">

									<!-- menu -->
									<Button Classes="Accent" Width="15" Height="15" Margin="0" Padding="0" BorderThickness="0"
											Content="{materialIcons:MaterialIconExt Kind=Menu}" HorizontalAlignment="Right" HorizontalContentAlignment="Center"
											IsVisible="{Binding HasMenuItems}" Background="Transparent" Click="bt_flyout" ClickMode="Press">
										<FlyoutBase.AttachedFlyout>
											<MenuFlyout ItemsSource="{Binding MenuItems}"/>
										</FlyoutBase.AttachedFlyout>
									</Button>

									<TextBlock FontWeight="{Binding FontWeight}" Text="{Binding Title}" FontSize="{Binding FontSize}" VerticalAlignment="Center"/>
								</Grid>
							</TreeDataTemplate>

							<!-- EntityView (hierarchy) -->
							<TreeDataTemplate DataType="{x:Type pmcore:EntityView}" ItemsSource="{Binding items_shown_in_editor}">
								<Grid ColumnDefinitions="Auto,*">
									<Grid.ContextMenu>
										<ContextMenu ItemsSource="{Binding MenuItems}" IsVisible="{Binding HasMenuItems}"/>
									</Grid.ContextMenu>
									<Viewbox Grid.Column="0" Width="16" Height="16" IsVisible="{Binding HasIcon}" HorizontalAlignment="Center" VerticalAlignment="Center">
										<local:UserBitmap Height="16" Path="{Binding icon}" VerticalContentAlignment="Center" HorizontalAlignment="Center" HorizontalContentAlignment="Center" IsVisible="{Binding HasIcon}" Margin="0,0,10,0"/>
									</Viewbox>									
									<Border Grid.Column="1" HorizontalAlignment="Stretch" Background="Transparent">
										<TextBlock Text="{Binding Name}" FontSize="{Binding FontSize}" FontWeight="{Binding FontWeight}" VerticalAlignment="Center" HorizontalAlignment="Stretch" TextAlignment="Left"/>
									</Border>
								</Grid>
							</TreeDataTemplate>
							
							<!-- IntegerFieldView -->
							<DataTemplate DataType="{x:Type pmcore:IntegerFieldView}">
								<TextBlock Text="{Binding name}"/>
							</DataTemplate>

							<!-- CursorView -->
							<DataTemplate DataType="pmcore:CursorView">
								<Grid ColumnDefinitions="Auto,Auto" RowDefinitions="Auto,Auto">
									<TextBlock Grid.Row="0" Grid.Column="0">X:</TextBlock>
									<TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding position.x, Converter={StaticResource FloatConverter2}}"/>
									<TextBlock Grid.Row="1" Grid.Column="0">Y:</TextBlock>
									<TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding position.y, Converter={StaticResource FloatConverter2}}"/>
								</Grid>
							</DataTemplate>

							<!-- StructureView -->
							<DataTemplate DataType="pmcore:StructureView">
								<TextBlock Text="{Binding Name}" FontSize="{Binding FontSize}" FontWeight="{Binding FontWeight}" Padding="{Binding Padding}" FontStyle="Italic"/>
							</DataTemplate>

							<!-- OutputSurfaceView -->
							<DataTemplate DataType="pmcore:OutputSurfaceView">
								<TextBlock Text="{Binding Name}" FontStyle="Italic"/>
							</DataTemplate>

						</TreeView.DataTemplates>

					</TreeView>
				</ScrollViewer>

			</Grid>
			</Border>

			<!-- Central Editor -->
			<TabControl Name="maintab" 
						Padding="0,10,10,0" Margin="5,10,0,0" Grid.Row="1" Grid.Column="1" 
						Focusable="False" KeyboardNavigation.TabNavigation="None"
						SelectionChanged="tabcontrol_selectionchanged" 
						Background="{DynamicResource ExpanderBackground}"
						Grid.ColumnSpan="3">
				<!-- Grid.ColumnSpan="{Binding MainTabColumnSpan}" -->

				<!-- Dashboard -->
				<TabItem Name="tab_dashboard" Header="Dashboard" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
					<local:DashboardPage Name="page_dashboard" />
				</TabItem>

				<!-- Projection -->
				<TabItem Name="tab_projection" Header="Projection" IsVisible="{Binding IsConnected}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
					<local:ProjectionPage Name="page_projection"/>
				</TabItem>

				<!-- Level Design -->
				<TabItem Name="tab_level_design" Header="Design" IsVisible="{Binding IsConnected}" Focusable="False" KeyboardNavigation.TabNavigation="None">
					<local:LevelDesignPage Name="page_level_design"/>
				</TabItem>

				<!-- Game Settings -->
				<TabItem Name="tab_game_settings" IsVisible="{Binding IsModuleProcessRunningAndConnectedNotCalibrator}" Focusable="False" KeyboardNavigation.TabNavigation="None">
					<TabItem.Header>
						<StackPanel Orientation="Horizontal">
							<materialIcons:MaterialIcon Kind="Controller" Margin="0,0,5,0" Foreground="Yellow"/>
							<TextBlock Text="{Binding RunningModule.name, Converter={StaticResource StringCapitalizeConverter}}" FontWeight="ExtraBold" Foreground="Yellow" />
						</StackPanel>
					</TabItem.Header>
					<local:GameSettings Name="page_game_settings"/>
				</TabItem>

				<!--
				<TabItem Header="Pointer Test" Focusable="False" KeyboardNavigation.TabNavigation="None">
					<local:PointerTestPage/>
				</TabItem>
				-->
			

			</TabControl>

			<!-- Notifications -->
			<Border Classes="Card" Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3"
					Background="#FF003FFF" IsHitTestVisible="False" Opacity="{Binding NotificationOpacity}"
					HorizontalAlignment="Right" VerticalAlignment="Top" Width="300" Height="50" Padding="5" Margin="7,5,16,7"
					BorderBrush="Transparent" BoxShadow=".001 .001 30 2 White">
				<TextBlock Text="{Binding NotificationText}" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
			</Border>
		
			<!-- Splitter / Separator -->
			<!--
			<GridSplitter Name="splitter" IsVisible="{Binding ShouldShowInspector}" 
						  Grid.Column="2" Grid.Row="1" Width="5" 
						  Background="Cyan"/>
						  
			-->

			<!-- INSPECTOR -->
			<ScrollViewer Name="inspector" 
						  IsHitTestVisible="{Binding ShouldShowInspector}" 
						  Opacity="{Binding ShouldShowInspector, Converter={StaticResource BoolToOpacityConverter}}"  
						  Grid.Column="3" Grid.Row="1" Margin="0,85,33,10" VerticalAlignment="Top">
				
				<ScrollViewer.Background>
					<SolidColorBrush Color="Black" Opacity="0.7"/>
				</ScrollViewer.Background>
					
				<StackPanel Orientation="Vertical">
					<ContentControl Name="inspector_content" Content="{Binding SelectedItemForInspector}"/>
				</StackPanel>
				
			</ScrollViewer>

			<!-- Status Bar -->
			<!--
			<Grid Grid.Row="2" Grid.ColumnSpan="4" Background="{DynamicResource ExpanderBackground}">
				
				<StackPanel Orientation="Horizontal" VerticalAlignment="Center">
					<Label Content="{Binding PlayersStats}" Foreground="White" FontSize="10" HorizontalAlignment="Left" VerticalContentAlignment="Center" VerticalAlignment="Center"/>
					<Label Content="{Binding ServerStats}" Foreground="Yellow" FontSize="10" HorizontalAlignment="Right" VerticalContentAlignment="Center" VerticalAlignment="Center"/>
				</StackPanel>			
				
			</Grid>
			-->
			
		</Grid>

	</dialogHost:DialogHost>

    
  </UserControl>
