//TABULA_GUID:{E66B2A10-A00F-4068-B33B-3B07E19B6914}
using System;
using System.ComponentModel;
using System.Linq.Expressions;

/**
 * BasePropertyChanged:
 * Super light class to derive to implement INotifyPropertyChanged, useful to call Raise* methods even when using Fody.PropertyChanged
 */

/* Version:
 *  1.1 (03/09/2017) added other methods from other similar classes
 *  1.0  
 */


namespace Tabula
{

    public class BasePropertyChanged : INotifyPropertyChanged
    {      
        public event PropertyChangedEventHandler PropertyChanged;
        
        public void RaisePropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }      

        public void RaiseAllPropertyChanged()
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(string.Empty));
        }

        public void RaisePropertyChanged<T>(Expression<Func<T>> expr)
        {
            var body = (MemberExpression)expr.Body;
            string name = body.Member.Name;
            RaisePropertyChanged(name);
        }

        public void SetProperty<T>(ref T storage, T value, string propertyName)
        {
            if (!object.Equals(storage, value))
            {
                storage = value;
                RaisePropertyChanged(propertyName);
            }
        }

        public void SetProperty<T>(ref T storage, T value, Expression<Func<T>> expr)
        {
            if (!object.Equals(storage, value))
            {
                storage = value;
                RaisePropertyChanged(expr);
            }
        }
    }
        
}