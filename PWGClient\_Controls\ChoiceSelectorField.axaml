<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
              xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 mc:Ignorable="d" d:DesignWidth="200" d:DesignHeight="200"
             x:Class="Tabula.PWGClient.ChoiceSelectorField">
	<Grid ColumnDefinitions="Auto,*,Auto">
		<Viewbox Grid.Column="0" Width="20" Margin="10,5,5,5" PointerPressed="bt_left">
			<materialIcons:MaterialIcon Kind="ArrowLeftDropCircle" Background="{StaticResource FakeBackground}"/>
		</Viewbox>
		<Border Grid.Column="1" BorderBrush="Gray" BorderThickness="2" CornerRadius="10" Height="25">
			<TextBlock Text="{Binding Value}" FontWeight="Medium" FontSize="10" VerticalAlignment="Center" TextAlignment="Center" />
		</Border>
		<Viewbox Grid.Column="2" Width="20" Margin="5,5,10,5" PointerPressed="bt_right">
			<materialIcons:MaterialIcon Kind="ArrowRightDropCircle" Background="{StaticResource FakeBackground}"/>
		</Viewbox>
	</Grid>
</UserControl>
