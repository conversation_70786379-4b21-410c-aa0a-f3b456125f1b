﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Controls;
using SkiaSharp;
using static System.Runtime.InteropServices.JavaScript.JSType;

// A generic Sprite object drawing a Bitmap with optional bounds
// Fallbaks to a custom color

namespace Tabula.SKRenderGraph
{
    public class SKSprite : SKObject
    {
        [Flags]
        public enum DrawFlags
        {
            None = 0,
            DrawBounds = 1 << 0,
            DrawBoundsIfSelected = 1 << 1
        }


        public SKRect       Rect = SKRect.Empty;
        public SKPoint      Offset = SKPoint.Empty;
        public float        Rotation = 0;
        //public SKPoint      RotationPivot = SKPoint.Empty;
		public SKImage      Bitmap;

        public DrawFlags    DrawFlag = DrawFlags.None;
        public SKColor      BoundsColor = SKColors.White;
        public int          LabelSize = 12;
        public string       LabelText = null;
        public SKColor      LabelColor = SKColors.White; 
        public SKTextAlign  LabelAlign = SKTextAlign.Center;

        public bool         FitInBounds = false;        // scales the bitmap so that if fits the rectangle
        public SKHelpers.Align FitAlign = SKHelpers.Align.None;

        public byte         Alpha = 255;

		public SKSprite(SKScene scene, SKRect rect) : base(scene)
        {
            Rect = rect;
        }

        // Just a position and size, no Bitmap
        public static SKSprite Create(SKScene scene, SKRect rect)
        {
			var sprite = new SKSprite(scene, rect);

			return sprite;
		}


		public static SKSprite CreateFromFile(SKScene scene, SKRect rect, string filename)
            => CreateFromBytes(scene, rect, File.ReadAllBytes(filename));

        // Create from bytearray (ex: loaded externally and cached)
        public static SKSprite CreateFromBytes(SKScene scene, SKRect rect, byte[] data, float scale = 1f)
		{
            var sprite = new SKSprite(scene, rect);

            scale = 0.5f;

            if (scale != 1f)
            {
				sprite.Bitmap = ResizeImage(SKImage.FromEncodedData(data), scale);
			}
            else
                sprite.Bitmap = SKImage.FromEncodedData(data);

            return sprite;
        }

        public static SKSprite CreateFromEmbeddedResource(SKScene scene, SKRect rect, string resource_id)
        {
            var sprite = new SKSprite(scene, rect);

            Assembly assembly = Assembly.GetExecutingAssembly();

            Stream stream = assembly.GetManifestResourceStream(resource_id);
            sprite.Bitmap = SKImage.FromEncodedData(stream);

            return sprite;
        }

		public static SKImage ResizeImage(SKImage image, float scale)
		{
			int newWidth = (int)(image.Width * scale);
			int newHeight = (int)(image.Height * scale);

			using (var bitmap = new SKBitmap(newWidth, newHeight))
			using (var canvas = new SKCanvas(bitmap))
			{
				var paint = new SKPaint
				{
					FilterQuality = SKFilterQuality.High
				};

				canvas.DrawImage(image, new SKRect(0, 0, newWidth, newHeight), paint);
				return SKImage.FromBitmap(bitmap);
			}
		}

		public void SetImage(string filename)
            => SetImage(File.ReadAllBytes(filename));

        public void SetImage(byte[] data)
        {
            Bitmap = SKImage.FromEncodedData(data);
            Rect = new SKRect(0,0,Bitmap.Width,Bitmap.Height);
        }

        // will be used only for drawing and picking
        private SKRect      OffsetRect
        {
            get
            {
                var offset_rect = new SKRect(Rect.Left, Rect.Top, Rect.Right, Rect.Bottom);
                offset_rect.Offset(Offset);
                return offset_rect;
            }
        }

        public virtual void SetOffset(SKPoint offset)
        {
            Offset = offset;
        }

		public override bool Contains(SKPoint pos)
		{
			if (Rotation == 0)
				return OffsetRect.Contains(pos);
			else
			{
				using (var path = new SKPath())
				{
					path.AddRect(OffsetRect);
                    SKMatrix matrix = SKMatrix.CreateRotationDegrees(Rotation, OffsetRect.MidX - Offset.X, OffsetRect.MidY - Offset.Y);
					path.Transform(matrix);
				
					return path.Contains(pos.X, pos.Y);
				}
			}
		}

        public override SKRect GetBoundingBox() => OffsetRect;		

		public override SKPoint GetPosition() => new SKPoint(Rect.MidX, Rect.MidY);

        public override void SetPosition(SKPoint pos)
        {
            // NOTE: rectangle location is top-left
            Rect.Location = pos - new SKPoint((float)Rect.Width / 2f, (float)Rect.Height / 2f);
            //Rect.Offset(Offset);

            base.SetPosition(pos);
        }

        public override void SetSize(SKSize size)
        {
            var prev_pos = GetPosition();
            Rect.Size = size;
            SetPosition(prev_pos);

            base.SetSize(size);
        }

        public override SKSize GetSize() => Rect.Size;

        public override void Move(SKPoint pos_rel)
        {
            Rect.Offset(pos_rel);

            base.Move(pos_rel);
        }

        int normal_layer = int.MinValue;
		int normal_order = int.MinValue;
		byte normal_alpha = 255;
        public override void Update()
        {
            if (normal_layer == int.MinValue)
            {
                normal_layer = Layer;
				normal_order = Order;
				normal_alpha = Color.Alpha;
			}

			// SPECIAL: locked sprites (not draggable) change layer and are put behind the last
			if (!IsDraggable)
			{
                if (Layer != SKScene.LayerLockedObjects)
                {
                    Layer = SKScene.LayerLockedObjects;
                    Scene.SetLastInLayer(this);
                    Color = new SKColor(Color.Red, Color.Green, Color.Blue, 100);
				}
			}
            else
			{
                if (Layer != normal_layer)
                {
                    Layer = normal_layer;
                    Order = normal_order;
					Color = new SKColor(Color.Red, Color.Green, Color.Blue, normal_alpha);
				}
			}


			if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsMouseOver)
            {
                Paint.Color = ColorHover;

                if (IsSelectableWithLeftClick)
                {
                    if (IsMouseLeftClicked)
                    {
                        Select();
                    }
                    else if (IsSelected && IsLeftMouseStartDragging)
                    {
                        DragStart();
                    }
                }
                else
                {
                    // implicit selection
                    if (IsLeftMouseStartDragging)
                    {
                        DragStart();
                    }
                }
            }
            else
                Paint.Color = Color;

            base.Update();
        }

        private SKPaint _get_paint()
		{
			var paint = new SKPaint()
			{
				Color = Color,
				IsAntialias = true,
				FilterQuality = SKFilterQuality.Low
			};

			return paint;
		}

		public override void Draw()
		{
			if (Rotation != 0)
			{
				Canvas.Save();
				Canvas.RotateDegrees(Rotation, OffsetRect.MidX - Offset.X, OffsetRect.MidY - Offset.Y);
			}

			if (FitInBounds)
			{
				var out_rect = new SKRect(0, 0, OffsetRect.Width, OffsetRect.Height);
				var in_rect = new SKRect(0, 0, Bitmap.Width, Bitmap.Height);

				var fit_rect = SKHelpers.FitRect(in_rect, out_rect, FitAlign);
				fit_rect.Offset(OffsetRect.Location);

				if (Bitmap != null)
				{
					Canvas.DrawImage(Bitmap,
						new SKRect(0, 0, Bitmap.Width, Bitmap.Height),
						fit_rect, _get_paint());
				}
				else
				{
					Canvas.DrawRect(OffsetRect, _get_paint());
				}
			}
			else
			{
				if (Bitmap != null)
				{
					Canvas.DrawImage(Bitmap,
						new SKRect(0, 0, Bitmap.Width, Bitmap.Height),
						OffsetRect, _get_paint());
				}
				else
				{
					Canvas.DrawRect(OffsetRect, _get_paint());
				}
			}

			if (LabelText != null)
			{
				SKPoint rcenter = new SKPoint(
					(OffsetRect.Left + (OffsetRect.Left + OffsetRect.Width)) / 2,
					(OffsetRect.Top + (OffsetRect.Top + OffsetRect.Height)) / 2);

				Canvas.DrawText(LabelText.ToString(), rcenter.X, rcenter.Y, new SKPaint()
				{
					Color = LabelColor,
					TextSize = LabelSize / Scene.CanvasScale,
					TextAlign = LabelAlign,
					IsAntialias = true,
					FilterQuality = SKFilterQuality.High
				});
			}

			if (DrawFlag.HasFlag(DrawFlags.DrawBounds) ||
				(DrawFlag.HasFlag(DrawFlags.DrawBoundsIfSelected) && IsSelected && IsSelectable))
			{
				Canvas.DrawRect(OffsetRect, new SKPaint() { Style = SKPaintStyle.Stroke, Color = BoundsColor });
			}

			if (Rotation != 0)
				Canvas.Restore();
		}
	}
}
