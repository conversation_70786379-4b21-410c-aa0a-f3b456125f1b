﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

namespace Tabula.SKRenderGraph
{
    public class SKControlPoint : SKCircle
    {
		public const float UnselectedSize = 5;      // constant visual size
		public const float SelectedSize = 10;       // constant visual size
        public const float HitSize = 15;            // constant hit size

        private SKPoint position;

        // TEST: undo move
        private SKPoint position_undo;
                
        public SKControlPoint(SKScene scene) : base(scene, SKPoint.Empty, SelectedSize) //  base(SKRect.Empty)
        {            
            Layer = SKScene.LayerControlPoints;
            Color = new SKColor(0, 255, 250, 120);
            IsSelectable = false;
        }

        public override void SetPosition(SKPoint pos)
        {
            position = pos;

            base.SetPosition(pos);
        }

		public override SKPoint GetPosition() => position;

		public override SKRect GetBoundingBox() => SKRect.Empty;    // to disable computation in scene bbox

        public override void Move(SKPoint pos_rel)
        {
            position_undo = position;
            position += pos_rel;

            base.Move(pos_rel);
        }

        public void UndoMove()
        {
            position = position_undo;
        }

        public override void Update()
        {
            // Rect = new SKRect(position.X - real_size / 2f, position.Y - real_size / 2f, position.X + real_size / 2f, position.Y + real_size / 2f);

            var _size = IsMouseOver ? SelectedSize : UnselectedSize;

            Radius = _size / Scene.CanvasScale;    // constant size
            HitRadius = HitSize / Scene.CanvasScale;

            Center = position;

            base.Update();
        }

        public override void LateDraw()
        {
            Paint.IsStroke = true;

            // outer black
            Paint.Color = SKScene.ColorBlack;            
            Paint.StrokeWidth = 4 / Scene.CanvasScale;
            Canvas.DrawCircle(Center, Radius, Paint);

            // outer white
            Paint.Color = SKScene.ColorWhite;
            Paint.StrokeWidth = 2 / Scene.CanvasScale;
            Canvas.DrawCircle(Center, Radius, Paint);

            // fill
            Paint.IsStroke = false;
            Paint.Color = Color;

            base.Draw();

        }
    }
}
