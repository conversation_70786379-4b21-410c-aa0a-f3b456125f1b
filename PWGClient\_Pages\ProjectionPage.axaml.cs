using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Avalonia.Media;
using Tabula.PMCore;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class ProjectionPage : UserControl, ITabbedPage
	{
		public ProjectionPage()
		{
			InitializeComponent();

			PointerPressed += (s, args) =>
			{
				var point = args.GetCurrentPoint(null);
				if (point.Properties.IsRightButtonPressed)
				{
					MainWindowUI.Instance.SetCursorHand();
				}
			};

			PointerReleased += (s, args) =>
			{
				var point = args.GetCurrentPoint(null);
				if (!point.Properties.IsRightButtonPressed)
				{
					MainWindowUI.Instance.SetCursorDefault();
				}
			};
		} 

		void bt_warp_click(object sender, RoutedEventArgs args)
			=> screen_editor_panel.Warp();
		

		void bt_region_click(object sender, RoutedEventArgs args)
			=> screen_editor_panel.Region();
		

		async void bt_calibrate_click(object sender, RoutedEventArgs args)
			=> await screen_editor_panel.Calibrate();
		

		async void bt_reset_click(object sender, RoutedEventArgs args)
		{
			try
			{
				switch(MainWindowUI.Instance.ProjectionSetupMode)
				{
					case MainWindowUI.ProjectionSetupModes.Warp:
						{
							var choice = await ProgressDialog.ShowChoicesAsync("Reset Warp ?", new string[] { "Yes", "No" });

							if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
								return;

							MainWindowUI.Instance.View.OutputSurfaces[0].ResetSurfaceWarp();
						}
						break;

					case MainWindowUI.ProjectionSetupModes.Region:
						{
							var choice = await ProgressDialog.ShowChoicesAsync("Reset Region ?", new string[] { "Yes", "No" });

							if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
								return;

							MainWindowUI.Instance.View.OutputSurfaces[0].ResetOffsetTiling();
						}
						break;
				}
				
			}
			catch { }
		}

		#region ITabbedPage

		public void OnTabSelected()
		{
			bt_warp_click(null, null);

			// Select the outputsurface 0 and fit the view
			/*
			if (MainWindowUI.Instance.View != null)
				MainWindowUI.Instance.SelectedItem = MainWindowUI.Instance.View.OutputSurfaces[0];
			*/

			MainWindowUI.Instance.Fit();
		}

		public void OnTabUnselected()
		{
			InfoBox.StopCheckAction();
		}

		#endregion
	}


}
