﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Tabula;
using Tabula.SharedObjectMap;
using System.Linq;
using Tabula.PMCore.Unity;
using PropertyChanged;

// The server method are defined with partial classes on the server


namespace Tabula.PMCore
{
    public static class Defaults
    {
        public static int RPCServerPort = 16100;
        public static int OSCServerPort = 16200;
        public static int HTTPServerPort = 8080;
    }

	// FIXME: deprecate and generalize?    
	/*
    public enum ApplicationStatus
    {
        None = 0,
        Edit = 1,   // Editing and calibration
        Play = 2    // Game play
    } */

	// This is the basic "Project"
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Model : SharedObjectMap.GuidObject
    {
		[Flags]
		[JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
		public enum Flags
		{
			None = 0,
			Game				= 1 << 1,    
			Experience			= 1 << 2,     
			Tool				= 1 << 3,     
			CreateEntities		= 1 << 10,      // allow adding entities
			StructureEffects	= 1 << 11,      // allow adding structure effects
			FocusNotice			= 1 << 13,      // warns of missing focus
			ExperiencePlayers	= 1 << 14,      // experience supports the entering of players (walkable placeables are enabled)
		}

		public int Version;             // SARGAME protocol version (editor and module must use the same, incremented when there are breaking additions or modifications)
        public int TemplateVersion;     // Project template version (to trigger a new copy of project files from template folder, useful to force updated projects)

        public string ProjectName;      // project name (for scenarios, this is the verbose name)
		public Flags ProjectFlags;		// flags for the project

		public string ScenarioId;       // scenario identifier (calibration and structures) Screen, OutputSurfaces, Structures
		

        // The whole virtual screen size, mapped to one or more ouputsurfaces
        public Screen Screen;

        // Surfaces configuration (the "Screens")
        // NOTE: just one at the moment
        public List<OutputSurface> OutputSurfaces;

        // For runtime
		#region Editing

		// transmitting the visible cursor when interacting
		public Cursor Cursor;

        // for structure/contour drawing, one at a time
        public Polygon Polygon;

        // the status of the focus for the module (player)
        [JsonIgnore]
        public bool ModuleFocus = false;

		#endregion

        // Templates to clone for entities, with default parameters, searched by type
        public List<Entity>     Templates;

		#region Structures

		// Physical structures
		public List<Structure>  Structures;

		#endregion

		#region Entities

        public List<Entity> Entities;

		#endregion

		#region Normalization

		// populates the model
		public void SetDefaults()
        {
            ProjectName = "<undefined>";

            var standard_screen_size = new SizeI() { width = 1920, height = 1080 };

            Screen = new Screen()
            {
                size = standard_screen_size,

                // TODO: re-enter some pixels
                screen_markers = new List<ScreenMarker>()
                { new ScreenMarker() { position = new Vector2f(0, 0) },
                    new ScreenMarker() { position = new Vector2f(standard_screen_size.width, 0) },
                    new ScreenMarker() { position = new Vector2f(standard_screen_size.width, standard_screen_size.height) },
                    new ScreenMarker() { position = new Vector2f(0, standard_screen_size.height) }
                },

                image_markers = new List<ImageMarker>()
			};

            for (int i = 0; i < Screen.MAX_IMAGE_MARKERS; i++)
                Screen.image_markers.Add(new ImageMarker() { position = new Vector2f(0,0), enabled = false });

            OutputSurfaces = new List<OutputSurface>() { new OutputSurface() };
            OutputSurfaces[0].aspect = 1;
            OutputSurfaces[0].vertices = new List<Vector2i> { new Vector2i(0, 0), new Vector2i(Screen.size.width, 0), new Vector2i(Screen.size.width, Screen.size.height), new Vector2i(0, Screen.size.height) };

            Structures = new List<Structure>();

            Cursor = new Cursor();

            Polygon = new Polygon();           

            Templates = new List<Entity>();

            Entities = new List<Entity>();
        }

        // Makes sure the loaded scenario has the needed objects created
        public override void Normalize()
        {
            base.Normalize();

			// PMCore_Server decides the project type
#if UNITY_STANDALONE || UNITY_EDITOR
			ProjectFlags = PMCore_Server.Instance.ProjectFlags;
#endif
			// TODO: set to current screen size?
			var standard_screen_size = new SizeI() { width = 1920, height = 1080 };

			// Screen
			if (Screen == null)
            {               
				Screen = new Screen()
				{
					size = standard_screen_size,

					screen_markers = new List<ScreenMarker>()
					{ new ScreenMarker() { position = new Vector2f(0,0),},
					  new ScreenMarker() { position = new Vector2f(standard_screen_size.width,0) },
					  new ScreenMarker() { position = new Vector2f(standard_screen_size.width,standard_screen_size.height) },
					  new ScreenMarker() { position = new Vector2f(0,standard_screen_size.height) }
					},

					image_markers = new List<ImageMarker>()
				};
			}

            if (Screen.screen_markers == null || (Screen.screen_markers!=null && Screen.screen_markers.Count==0))
                Screen.screen_markers = new List<ScreenMarker>()
                    { new ScreenMarker() { position = new Vector2f(0,0),},
                      new ScreenMarker() { position = new Vector2f(standard_screen_size.width,0) },
                      new ScreenMarker() { position = new Vector2f(standard_screen_size.width,standard_screen_size.height) },
                      new ScreenMarker() { position = new Vector2f(0,standard_screen_size.height) }
                    };

			// check calibration points are valid
			foreach (var cp in Screen.screen_markers)
            {
                if (cp.position == null)
                    cp.position = new Vector2f(0, 0);
            }

            if (Screen.image_markers == null || (Screen.image_markers != null && Screen.image_markers.Count==0))
                Screen.image_markers = new List<ImageMarker>();

            // normalize up to 10
			for (int i = Screen.image_markers.Count; i < Screen.MAX_IMAGE_MARKERS; i++)
				Screen.image_markers.Add(new ImageMarker() { position = new Vector2f(0, 0), enabled = false });

			foreach (var cp in Screen.image_markers)
			{
                if (cp.position == null)
                {
                    cp.position = new Vector2f(0, 0);
                    cp.enabled = false;
                }
			}

			// Output surfaces
			if (OutputSurfaces == null || OutputSurfaces.Count == 0)
            {
                OutputSurfaces = new List<OutputSurface>() { new OutputSurface() };
                OutputSurfaces[0].aspect = 1;
                OutputSurfaces[0].vertices = new List<Vector2i> { new Vector2i(0, 0), new Vector2i(Screen.size.width, 0), new Vector2i(Screen.size.width, Screen.size.height), new Vector2i(0, Screen.size.height) };
            }

            if (Structures == null)
			    Structures = new List<Structure>();

            if (Cursor == null)
			    Cursor = new Cursor();

            if (Polygon == null)
			    Polygon = new Polygon();

            if (Templates == null)
			    Templates = new List<Entity>();

            if (Entities == null)
			    Entities = new List<Entity>();
		}

#endregion

		#region Entities search and add

		// Searches entity tree for name && !! type
		public List<(Entity entity, Entity parent)> SearchEntities(string name=null, string type=null, Entity.Flags flags = Entity.Flags.None)
        {
            void _search_entity(Entity entity, List<(Entity entity, Entity parent)> _found_entities)
            {
                if (entity.items != null)
                    foreach (var e in entity.items)
                    {
                        bool name_ok = (string.IsNullOrEmpty(name) || (!string.IsNullOrEmpty(name) && e.name == name));
                        bool type_ok = (string.IsNullOrEmpty(type) || (!string.IsNullOrEmpty(type) && e.type == type));
                        bool flags_ok = (flags == Entity.Flags.None || (flags != Entity.Flags.None && e.flags.HasFlag(flags)));

                        if (name_ok && type_ok && flags_ok)
                        {
                            _found_entities.Add((e, entity));
                        }

                        _search_entity(e, _found_entities);
                    }
            }


           var found_entities = new List<(Entity entity, Entity parent)>();

            foreach (var e in Entities)
                _search_entity(e, found_entities);

            return found_entities;
        }

        public void AddEntityIfNotExist(Entity entity, string entity_context="Entities", string name = "", string type = "", Entity.Flags flags = Entity.Flags.None)
        {
            var found_entities = SearchEntities(name, type);

            if (found_entities.Count == 0)
                AddEntity(entity, entity_context);
        }

        public void AddEntity(Entity entity, string entity_context)
        {
            // Makes sure entity context root exists
            // NOTE: working on Model not wrappers because it is before SetModel()
            var root = (from e in Entities where e.name == entity_context select e).FirstOrDefault();
            if (root == null)
            {
                root = new Entity()
                {
                    name = entity_context,
                    flags = Entity.Flags.None,
                    items = new List<Entity>()
                };

                Entities.Add(root);
            }

            root.items.Add(entity);
        }

		#endregion

		#region Helpers

        // Calculates real screen size depending on offset 
        public RectI GetVisualScreenSize(int output_surface_idx=0)
        {
            if (output_surface_idx >= OutputSurfaces.Count)
                return default;

            var os = OutputSurfaces[output_surface_idx];

            return new RectI()
            {
                x = (int) ((float) os.x_offset * (float) Screen.size.width),
				y = (int) ((float) os.y_offset * (float) Screen.size.height),				
			    width = (int)((float)Screen.size.width * os.x_tiling),
				height = (int)((float)Screen.size.height * os.y_tiling)
		    };
		}

		public const int ScreenMarkersSafeAreaBorders = 60;

		// Returns the rectangle where marker should be in to be clearly taken from the picture and detected
		public RectI GetVisualMarkerSafeArea(int output_surface_idx=0)
        {
            var screen_rect = GetVisualScreenSize(output_surface_idx);

            screen_rect.x += ScreenMarkersSafeAreaBorders;
			screen_rect.y += ScreenMarkersSafeAreaBorders;
			screen_rect.width -= ScreenMarkersSafeAreaBorders*2;
			screen_rect.height -= ScreenMarkersSafeAreaBorders*2;

            return screen_rect;
		}

        public Entity FindEntityByName(string name)
        {
            if (Entities == null)
                return null;

            foreach (var e in Entities)
            {
                var found = e.FindEntityByName(name);
                if (found != null)
                    return found;
            }

            return null;
        }


		#endregion

		#region Extra settings

        public string BackgroundVideoPath = null;

		#endregion

	}

	#region Player and Input

	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class PlayerJoinResult
    {
        public const int ReasonAccept_NewPlayer = 1;
		public const int ReasonAccept_ExistingPlayer = 2;

		public const int ReasonDeny_General = -1;           // general deny
		public const int ReasonDeny_JoinDisabled = -2;      // not the right moment
		public const int ReasonDeny_NetworkingDisabled = -3;      // networking disabled (for players)
		public const int ReasonDeny_TooMany = -4;           // too many waiting players

		public int   reason;
        public int   player_client_id;  // the unique id to be used for direct communication (raw packets to networked players)
        public int   player_flags;      // flags containig info about enabled input modes and buttons

        public PlayerJoinResult() { }

		public PlayerJoinResult(int _reason, int _player_client_id=0, 
            int _player_flags = PlayerControllerMessage.Flags_Default)
        { 
            reason = _reason; 
            player_client_id = _player_client_id; 
            player_flags = _player_flags;
        }
    }

	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class PlayerLeftResult
    {
        public const int ReasonLeft_ClientLeft = 1;             // the client requested a leave
		public const int ReasonLeft_ClientDisconnected = 2;     // ?
		public const int ReasonLeft_KeepAliveExpired = 3;
		public const int ReasonLeft_FirstMessageWaitExpired = 4;    // not received first message in time

		public const int ReasonLeft_DenyClientNotFound = -1;    // NOTE: this will happen if there is a leave request from the client after a death notification

		public int reason;

		public PlayerLeftResult() { }

		public PlayerLeftResult(int _reason) { reason = _reason; }
	}


	#endregion

	#region Generic

	// For getting statistics and other data
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class NamedValue
	{
		public string name;
		public object value;

        public bool AsBool => Convert.ToBoolean(value);
        public int AsInt => Convert.ToInt32(value);
		public float AsFloat => Convert.ToSingle(value);
		public double AsDouble => Convert.ToDouble(value);
		public string AsString => Convert.ToString(value);

		public static bool GetBool(List<NamedValue> list, string name)
			=> (bool) (from s in list where s.name == name select s).FirstOrDefault()?.AsBool;

		public static int GetInt(List<NamedValue> list, string name)
            => (int) (from s in list where s.name==name select s).FirstOrDefault()?.AsInt;

		public static float GetFloat(List<NamedValue> list, string name)
			=> (float)(from s in list where s.name == name select s).FirstOrDefault()?.AsFloat;

		public static double GetDouble(List<NamedValue> list, string name)
			=> (double)(from s in list where s.name == name select s).FirstOrDefault()?.AsDouble;

		public static string GetString(List<NamedValue> list, string name)
			=> (string)(from s in list where s.name == name select s).FirstOrDefault()?.AsString;

	}

	// For getting key values lists (for comboboxes etc)
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
    [AddINotifyPropertyChangedInterface]
    [Serializable]
	public class KeyValueString
    {
        public string key;
        public string value;

        public string Key => key;
        public string Value => value;
    }

	// more general specifying also a primitive type (es: for props)
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
	[AddINotifyPropertyChangedInterface]
	[Serializable]
	public class KeyValueType
	{
        public enum _type
        {
            Bool = 1,
            String = 2,
            Int = 3,
            Float = 4,
            Double = 5
		}

		public string key;
        public _type  type = _type.Bool;
		public string value;    // always a string, converted when requested Value

		public string Key => key;
		public object Value
        {
            get
            {
                switch(type)
                {
                    case _type.Bool: return Convert.ToBoolean(value);
                    case _type.String: return value;
                    case _type.Int: return Convert.ToInt32(value);
                    case _type.Float: return Convert.ToSingle(value);
                    case _type.Double: return Convert.ToDouble(value);

                    default:
                        return "<undefined>";
				}
            }
        }
	}

	// NEW: complete new version, just a seralizable helper class
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
    [Serializable]
	public partial class EntityCreate
	{
		public long		guid;					// can be set in creation if the entity has been loaded / creted dynamically for persistence, will call SetModel() on the wrapper
		
        public string	type;					// type of the entity
		public string	icon;					// icon filename (no extension)
		public string	name_desc;				// descriptive name
		public string	category;				// category in editor (if empty will not be transmitted as creatable)
		public string	group;					// group in Entities to add to (or create)
		public bool		has_visual = true;		// if it has visual (in editor will be a drag & drop)
		public bool		show_editor = true;		// user creable, will be listed in editor when creating entities
		public string	license_feature = "";   // license feature required to create this entity

		// properties to set
#if UNITY_EDITOR || UNITY_STANDALONE
		[UnityEngine.Header("Properties")]
#endif
        public List<KeyValueType> properties;

		// These is a generic keystore for setting fields to their default value
#if UNITY_EDITOR || UNITY_STANDALONE
		[UnityEngine.Header("Fields Values")]
#endif
		public List<KeyValueString> fields_defaults;
	}

	#endregion

	#region General Classes

	[System.Reflection.Obfuscation(Exclude = true)]
	public class RectF : SharedObjectMap.GuidObject
	{
        public float x, y;
		public float width, height;
	}

	[System.Reflection.Obfuscation(Exclude = true)]
	public class RectI : SharedObjectMap.GuidObject
	{
		public int x, y;
		public int width, height;
	}

	[System.Reflection.Obfuscation(Exclude = true)]
	public class SizeF : SharedObjectMap.GuidObject
    {
        public float width, height;
    }

	[System.Reflection.Obfuscation(Exclude = true)]
	public class SizeI : SharedObjectMap.GuidObject
    {
        public int width, height;
    }

	[System.Reflection.Obfuscation(Exclude = true)]
	public class Vector2i : SharedObjectMap.GuidObject
    {
        public int x, y;

        public Vector2i() { }

        public Vector2i(int _x, int _y)
        {
            x = _x; y = _y;
        }

        public Vector2i(float _x, float _y)
        {
            x = (int) _x; y = (int) _y;
        }
    }

	[System.Reflection.Obfuscation(Exclude = true)]
	public class Vector2f : SharedObjectMap.GuidObject
    {
        public float x, y;

        public Vector2f() { }
        public Vector2f(float _x, float _y)
        {
            x = _x; y = _y;
        }
    }

	#endregion

	#region Mapping and Surfaces

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class Screen : SharedObjectMap.GuidObject
    {
        public SizeI size;

        // markers for homography
        public List<ScreenMarker>   screen_markers;     // always 4
		public List<ImageMarker>    image_markers;      // for ease of UI, a maximum number of image_markers is created and only a certain number used

        public const int MAX_IMAGE_MARKERS = 10;

		public Media? calibration_image;    // the reference image of the wall

        public float calibration_image_opacity = 1f;    // opacity of the calibration image (wall) in the editor

        public float background_mask_opacity = 0f;      // opacity of the background obsucrer
    }

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class ScreenMarker : SharedObjectMap.GuidObject
	{
		public Vector2f? position;   // position of calibration point on screen
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class ImageMarker : SharedObjectMap.GuidObject
	{
		public Vector2f? position;   // position of calibration point on reference image
        public bool enabled;         // true if it is used in the calibration
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class Media : SharedObjectMap.GuidObject
	{        
        public string   file;
        public SizeI?   image_size;
        public DateTime time;
	}

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class OutputSurface : SharedObjectMap.GuidObject
    {
        // 4 corners for warping whole screen
        public List<Vector2i> vertices = new List<Vector2i>();

        // aspect
        public float aspect;

        // tiling and offset
        public float x_tiling=1;
        public float x_offset=0;
        public float y_tiling=1;
        public float y_offset=0;
    }

    // Helper/temporary class for creating structures / contours
    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Polygon : SharedObjectMap.GuidObject
	{
        public bool enabled;

        // list of dynamically added points
        public List<Vector2i> vertices = new List<Vector2i>();
    }

	#endregion

	#region Structure Effect Entity

    // this class is mainly to discriminate between normal entities

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class StructureEffect : Entity
    {
        // TODO: Effect type is in pops "effect_type" can be "face","edge","contour"
	}

	#endregion

	#region Structures

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Structure : Entity
    {
		// at least 3 vertices  in clockwise order
		public List<Vector2f> vertices = new List<Vector2f>();

        // NOTE: effects are defined in base class as data, the implementation rules
        public bool     is_physical = true;         // by default every structure is physical (has a collider)
        public bool     is_mask = false;            // shortcut for using the structure as a black mask (black color, no effects or contour)

        // parameters, divided by base and future new shaders
        public StructureParams0   params0 = new StructureParams0();     // standard structure indexed effects
        public StructureParams1   params1 = new StructureParams1();     // standard shader	

        // Effects stack
        public List<StructureEffect> effects;

		[JsonIgnore]
        public bool selected_for_editing = false;

		[JsonIgnore]
		public bool IsOverriding =>
			params0.texture_enable || params0.texture_mask_enable || params0.effect_enable || params0.effect2_enable || params0.contour_enable || params0.color_animation_enable || params0.color_enable || params0.material_enable;

		// position is still used to move the whole structure, but it is not visually significant
		// 
		public override void Normalize()
		{
			base.Normalize();

            if (effects == null)
                effects = new List<StructureEffect>();
		}
	}

    // Classes for serialized structure params, depends on shader used

    // Base parameters, game-related effects based on StructureSettings effects indexes
    [Serializable]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class StructureParams0 : SharedObjectMap.GuidObject
	{
		public bool     texture_enable = false;
		public string   texture;

		public bool     texture_mask_enable = false;
		public string   texture_mask;

        public bool     effect_enable = false;
		public int      effect = -1;                // type of mesheffect (-1 none) for contour (or contour + front face)
		public float    effect_scale = 1.0f;              // effect scale (TODO: include in effect parameters?)

		public bool     effect2_enable = false;
		public int      effect2 = -1;               // type of mesheffect (-1 none) for front face
		public float    effect2_scale = 1.0f;              // effect2 scale (TODO: include in effect parameters?)

		public bool     contour_enable = false;
		public int      contour = -1;               // type of contour (-1 none)

		public bool     color_animation_enable = false;
		public int      color_animation = -1;       // type of color animation (-1 none)
		public float    color_animation_speed = 1;  // speed of the color animation        

		public bool     color_enable = false;
		public string   color = "#FFFFFFFF";                 // hexadecimal

		public bool     material_enable = false;
		public int      material = -1;
	}

	// First standard shader PMStructureEffectShader
	[Serializable]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class StructureParams1 : SharedObjectMap.GuidObject
	{
		public float    rotation = 0;
		public bool     texture_mask_invert = false;
		public bool     texture_screenspace = false;
		public float    opacity = 0;

        // TODO:...
	}

	#endregion

	#region Entities

	// Base class for positionable objects
	[System.Reflection.Obfuscation(Exclude = true)]
	public abstract class Visual : SharedObjectMap.GuidObject
    {
        public Vector2f? position;
        public SizeF?    size;              // bounding box, defaults to single point
        public Vector2f? offset;            // offset of position (icons, bounding boxes..)

        // NEW
        public float     scale;             // general scale, for sprite handling
        public float     rotation;          // general rotation, for sprite handling

        public List<Vector2f>? points;     // NEW every visual can have a list of points, used in polylines, areas, etc..

		public override void Normalize()
        {
            base.Normalize();

            if (position == null)
                position = new Vector2f(0, 0);

            if (size == null)
                size = new SizeF();

            if (offset == null)
                offset = new Vector2f(0, 0);

            if (points == null)
                points = new List<Vector2f>();
        }
    }


    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class Cursor : Visual
    {
        public bool draw = true;            // TODO: move in Visual ?
    }

	[GuidObjectClass]
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Entity : Visual
    {
        [Flags]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public enum Flags
        {
            None = 0,
            HasVisual			= 1 << 1,      // entity has a visual representation
            ShowWhenSelected	= 1 << 2,      // visual representation is only visualized when selected
            ShowInEditor		= 1 << 3,      // for future replacement of show_editor
            ShowAsZone			= 1 << 4,      // shows the zone and not the scaled icon
			IconFit				= 1 << 5       // icon fits in visual bounds
		}

        public string name;         // IMPORTANT: if name is prepended with # (ex: #myobject) then it is a REFERENCE of an existing object in scene, not instantiated with prefab, a component will be added
        public string type;
        public string container;    // gameobject container for creation
                
        public string icon;

        public string? tag;

		public bool show_editor = true; // shows in hierarchy

		public string name_desc;    // the name visualized

        public bool   active = true;        // gameobject active
        public bool   sync_fields = true;   // field sync behaviour (true by default)

        [GuidObjectCollection]  // per permettere il polimorfismo nella collezione
        public List<object>? fields;    // lista di parametri di questa entità

        public List<string>?  fields_shown;  // fields that will be shown (if null it is ignored, if empty NO field will be shown)

        public List<Entity>? items;     // entità figlie

		public Dictionary<string, object>? props;   // key-vale properties (not to be visual edited)

		public Flags flags = Flags.None;    // flags

		// Presets
		#region Presets

		public List<Preset>? presets;
        public string? preset;  // selected preset

		// will just set model fields values, very low level
        public void ApplyPreset(string preset_name)
        {
			void _set_field_value(string fname, object value)
			{
				foreach (var f in fields)
				{
					dynamic dfield;

					try
					{
						dfield = f;

						if (dfield.name == fname)
						{
							dfield.SetValue(value);
							return;
						}
					}
					catch { }
				}
			}

			// check preset exists
			if (presets == null)
				return;

			var selected_preset = (from p in presets where p.name == preset_name select p).FirstOrDefault();
			if (selected_preset == null)
				return;
			if (selected_preset.fields == null)
				return;

			foreach (var kvp in selected_preset.fields)
			{
				_set_field_value(kvp.Key, kvp.Value);
			}
		}

		#endregion

		[JsonIgnore]
        public bool IsReference => name.StartsWith("#");


        private Dictionary<string, dynamic> _fields_cached = new Dictionary<string, dynamic>();

		#region Fields

		public void AddField(string name, Field field)
        {
            if (fields == null)
                fields = new List<object>();

            field.name = name;
            fields.Add(field);
        }

		public T GetField<T>(string field_name) where T: Field
		{
			if (fields == null)
				return null;

			// caching
			if (_fields_cached.TryGetValue(field_name, out dynamic cached_field))
            {
                return (T) cached_field;
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
                if (field != null)
                    _fields_cached.Add(field_name, field);

                return (T) field;
            }
		}

		public T GetFieldValue<T>(string field_name)
        {
			// caching
			if (_fields_cached.TryGetValue(field_name, out dynamic cached_field))
            {
                return (T)cached_field.GetValue();
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
                if (field != null)
                    _fields_cached.Add(field_name, field);
                else
                    return default;

                return (T)field.GetValue();
            }
        }

        public object GetFieldValue(string field_name)
		{
			// caching
			if (_fields_cached.TryGetValue(field_name, out dynamic cached_field))
            {
                return cached_field.GetValue();
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
                if (field != null)
                {
                    _fields_cached.Add(field_name, field);
                    return field.GetValue();
                }
                else
                    return null;
            }
        }

		public bool SetFieldValue<T>(string field_name, T field_value)
		{
            if (fields == null)
                return false;

            dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();

            if (field != null)
            {
                field.SetValue(field_value);
                return true;
            }
            else
                return false;           
		}

        // hides field setting show_editor 
        public void HideField(string name)
        {
			dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == name.ToLower() select f).FirstOrDefault();
            if (field != null)
                field.show_editor = false;
		}

        public void HideFields(params string[] names)
        {
            foreach (var name in names)
				HideField(name);
		}

		public void HideFieldGroup(string group)
		{
            var group_fields = (from f in fields.OfType<Field>() where f.@group != null && f.@group == @group select f);
            if (group_fields != null)
                foreach (var f in group_fields)
                    f.show_editor = false;
		}

		public void ShowField(string name)
		{
			dynamic field = (from f in fields.OfType<Field>() where f.name.ToLower() == name.ToLower() select f).FirstOrDefault();
			if (field != null)
				field.show_editor = true;
		}

		public void ShowFields(params string[] names)
		{
			foreach (var name in names)
				ShowField(name);
		}

		public void ShowFieldGroup(string group)
		{
			var group_fields = (from f in fields.OfType<Field>() where f.@group != null && f.@group == @group select f);
			if (group_fields != null)
				foreach (var f in group_fields)
					f.show_editor = true;
		}


		#endregion

		#region Tags

		public void AddTag(string _tag)
		{
            if (string.IsNullOrEmpty(tag))
                tag = string.Empty;
           
			var tagList = tag.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
			if (!tagList.Contains(_tag))
			{
				tagList.Add(_tag);
				tag = string.Join(",", tagList);
			}
		}

		public void RemoveTag(string _tag)
		{
            if (string.IsNullOrEmpty(tag))
                return;

			var tagList = tag.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
			if (tagList.Contains(_tag))
			{
				tagList.Remove(_tag);
				tag = string.Join(",", tagList);
			}
		}

        public bool HasTag(string _tag)
        {
			if (string.IsNullOrEmpty(tag))
				return false;

			var tagList = tag.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            return tagList.Contains(_tag);
		}

		public List<string> GetTags()
		{
            if (string.IsNullOrEmpty(tag))
                return new List<string>();

			return tag.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
		}

		#endregion

		#region Props

        // Props are an easy way to store data withing an entity

        public void SetProp(string name, object value)
        {
            if (props == null)
                props = new Dictionary<string, object>();

            if (props.ContainsKey(name))
                props[name] = value;
            else
                props.Add(name, value);
        }

        // version for typed props
		public void SetProp(KeyValueType p)
		{
			if (props == null)
				props = new Dictionary<string, object>();

			if (props.ContainsKey(p.key))
				props[p.key] = p.Value;
			else
				props.Add(p.key, p.Value);
		}

		public bool HasProp(string name)
            => props!=null && props.ContainsKey(name);

        public T GetProp<T>(string name)
        {
            if (props == null)
                return default;

            if (props.ContainsKey(name))
                return (T)props[name];
            else
                return default;
        }

		#endregion

		public Entity FindEntityByName(string name_to_search)
		{
			if (name == name_to_search)
				return this;

            if (items != null)
                foreach (var i in items)
                {
                    var found = i.FindEntityByName(name_to_search);
                    if (found != null)
                        return found;
                }

            return null;
		}
	}

	#endregion

	#region Fields Attributes

	// Attribute to flag fields so that they are automatically mapped
	public class MapFromEntityFieldAttribute : Attribute
	{
        public bool   show_editor = true;
		public string group;
        public int    group_index = 0;
		public int    field_index = 0;
		public string name_desc;
		public string info;
		public string tooltip;
        public object hide_value;
		public string[] hides_fields;          // optional: will hide other fields depending on its
		public string[] hides_groups;

		public Type FieldType;
	}

	public class ButtonFieldAttribute : MapFromEntityFieldAttribute
	{
		public string action;

		public ButtonFieldAttribute()
		{
			FieldType = typeof(ButtonField);
		}
	}

	public class FloatFieldAttribute : MapFromEntityFieldAttribute
	{
		public float min = 0.2f, max = 3.0f;

		public FloatFieldAttribute()
		{
			FieldType = typeof(FloatField);
		}
	}

	public class SliderFloatFieldAttribute : FloatFieldAttribute
	{
		public SliderFloatFieldAttribute()
		{
			FieldType = typeof(SliderFloatField);
		}
	}

	public class FileFieldAttribute : MapFromEntityFieldAttribute
	{
        public string   title;          // for the browse dialog
		public string[] file_types;     // wildcard extensions for files

		public FileFieldAttribute()
		{
			FieldType = typeof(FileField);
		}
	}

	public class ColorFieldAttribute : MapFromEntityFieldAttribute
	{
		public ColorFieldAttribute()
		{
			FieldType = typeof(ColorField);
		}
	}

	public class ChoiceFieldAttribute : MapFromEntityFieldAttribute
	{
		public string[] choice_keys;
		public string[] choice_values;

		public ChoiceFieldAttribute()
		{
			FieldType = typeof(ChoiceField);
		}
	}

	public class ChoiceSelectorFieldAttribute : ChoiceFieldAttribute
	{
		public ChoiceSelectorFieldAttribute()
		{
			FieldType = typeof(ChoiceSelectorField);
		}
	}

	public class BoolFieldAttribute : MapFromEntityFieldAttribute
	{
		public BoolFieldAttribute()
		{
			FieldType = typeof(BoolField);
		}
	}

	public class BoolCardsFieldAttribute : MapFromEntityFieldAttribute
	{
		public string true_label;
		public string false_label;

		public double width;
		public double height;

		public BoolCardsFieldAttribute()
		{
			FieldType = typeof(BoolCardsField);
		}
	}

	#endregion

	#region Fields

	// just a base class, doesn't need view/wrappers
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Field : SharedObjectMap.GuidObject
    {
        public string   name;               // real mapped name
        public string?  name_desc;          // descriptive name (for editor)
		public int      field_index;
		public string?  info;               // shown information (ex: right side)
        public string?  tooltip;            // tooltip information
        public string?  tag;                // generic tag (es: advanced settings..)

        public string   group;              // grouping in editor
		public string?  group_desc;         // descriptive name (for editor)
		public int      group_index;
		public string?  license_feature;    // shown only if the license has this feature

		public bool     show_editor = true; // another way to specify it to be shown in editor

        public object?      hide_value;            // the value that will trigger hides, ex: false for bools)
        public string[]?    hides_fields;          // optional: will hide other fields depending on its
		public string[]?    hides_groups;          // optional (bool) field name that must be true in order for this to show

		public bool IsExpanded = false;
        public bool IsSelected = false;

        public virtual void SetValue(object v, MapFromEntityFieldAttribute attr=null)
		{
            if (attr != null)
            {
                show_editor = attr.show_editor;
                group = attr.group;
                name_desc = attr.name_desc;
                group_index = attr.group_index;
				field_index = attr.field_index;
				info = attr.info;
                tooltip = attr.tooltip;
                hide_value = attr.hide_value;
                hides_fields = attr.hides_fields;
                hides_groups = attr.hides_groups;
            }
        }

        public virtual object GetValue()
        { return null; }

        public static Type GetFieldForType(Type type)
		{
            if (type == typeof(string))
                return typeof(StringField);
            else if (type == typeof(int))
                return typeof(IntegerField);
            else if (type == typeof(float))
                return typeof(FloatField);
            else if (type == typeof(bool))
                return typeof(BoolField);
            
            return null;
        }

		// TODO
		public static void SetAttributes(Entity entity,
			 string field_name, int? field_index=null, int? group_index=null,
			 string name_desc=null, string info=null, string tooltip=null,
			string group=null, string group_desc = null)
		{
			dynamic field = (from f in entity.fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
			if (field == null)
				return;

			if (field_index!=null)
				field.field_index = (int) field_index;

			if (group_index!=null)
				field.group_index = (int) group_index;

			if (field_name != null)
				field.name = field_name;

			if (name_desc!=null)
				field.name_desc = name_desc;

			if (info != null)
				field.info = info;

			if (tooltip != null)
				field.tooltip = tooltip;

			if (group != null)
				field.group = group;

			if (group_desc != null)
				field.group_desc = group_desc;

		}
	}

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class ButtonField : Field
    {
		public string action;   // action to call when pressed (if empty the field name will be used)

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			var b_attr = attr as ButtonFieldAttribute;
			if (b_attr != null)
			{
				action = b_attr.action;
			}
		}
	}

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class StringField : Field
    {
        public string value;


		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
            base.SetValue(v, attr);

            value = (string)Convert.ChangeType(v, typeof(string));
		}

		public override object GetValue()
		{
            return value;
		}
	}

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class IntegerField : Field
    {
        public int value;

        // clamping, applied on the client side
        public int min, max;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
			base.SetValue(v, attr);

			value = (int) Convert.ChangeType(v, typeof(int));
        }

        public override object GetValue()
        {
            return value;
        }
    }

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class FloatField : Field
    {
        public float value;

        // clamping, applied on the client side
        public float min, max;

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			value = (float)Convert.ChangeType(v, typeof(float));

			var slider_attr = attr as FloatFieldAttribute;
			if (slider_attr != null)
			{
				min = slider_attr.min;
				max = slider_attr.max;
			}
		}

		public override object GetValue()
        {
            return value;
        }
    }

    [GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class BoolField : Field
    {
        public bool value;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
			base.SetValue(v, attr);

			value = (bool) Convert.ChangeType(v, typeof(bool));
		}

        public override object GetValue()
        {
            return value;
        }
    }

	// alternative GUI
	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class BoolCardsField : BoolField
	{
		public string true_label;
		public string false_label;

		public double width;
		public double height;

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			var battr = attr as BoolCardsFieldAttribute;
			if (battr != null)
			{
				true_label = battr.true_label;
				false_label = battr.false_label;
				width = battr.width;
				height = battr.height;
			}
		}
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class SliderFloatField : Field
    {
        public float value;
        public float min, max;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
			base.SetValue(v, attr);

			value = (float)Convert.ChangeType(v, typeof(float));

            var slider_attr = attr as SliderFloatFieldAttribute;
            if (slider_attr != null)
            {
                min = slider_attr.min;
                max = slider_attr.max;
            }
        }

        public override object GetValue()
        {
            return value;
        }

        public static void SetAttributes(Entity entity, string field_name, float min, float max)
		{
			dynamic field = (from f in entity.fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
            if (field == null)
                return;

            if (!(field is SliderFloatField))
                return;

			field.min = min;
			field.max = max;
		}
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class SliderFloatPercentageField : SliderFloatField
	{
		public float percentage_factor;
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class ChoiceField : StringField
	{
        public string[] choice_keys;
		public string[] choice_values;

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			value = (string) Convert.ChangeType(v, typeof(string));

			var choice_attr = attr as ChoiceFieldAttribute;
			if (choice_attr != null)
			{
                choice_keys = choice_attr.choice_keys;
				choice_values = choice_attr.choice_values;
			}
		}

		public static void SetAttributes(Entity entity, string field_name, string[] choice_keys, string[] choice_values)
		{
			dynamic field = (from f in entity.fields.OfType<Field>() where f.name.ToLower() == field_name.ToLower() select f).FirstOrDefault();
			if (field == null)
				return;

			if (!(field is ChoiceField))
				return;

			field.choice_keys = choice_keys;
			field.choice_values = choice_values;
		}

		// special for populating with resource map
#if UNITY_STANDALONE || UNITY_EDITOR

		public static void SetChoicesFromResourceMap(Entity entity, string field_name, string resource_list_name)
		{
			var keys = new List<string>();
			var values = new List<string>();

			var rl = PMCore_Server.Instance.GetResourceList(resource_list_name);
			if (rl == null)
				return;

			foreach (var i in rl.items)
			{
				keys.Add(i.name);
				values.Add(i.name_desc);
			}

			ChoiceField.SetAttributes(entity, field_name,
				choice_keys: keys.ToArray(),
				choice_values: values.ToArray());
		}

#endif
	}

	// choices are selected with different GUI widget
	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class ChoiceSelectorField : ChoiceField
	{

	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class Position2DField : Field
    {
        public Vector2f position;

        public override void SetValue(object value, MapFromEntityFieldAttribute attr = null)
        {
			base.SetValue(value, attr);

			value = (Vector2f) value;
        }

        public override object GetValue()
        {
            return position;
        }
    }

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class FileField : Field
	{
		public string value;            // path
        public string title;            // browse dialog title
        public string[] file_types;     // wildcard extensions for files

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			value = (string) Convert.ChangeType(v, typeof(string));

			var file_attr = attr as FileFieldAttribute;
			if (file_attr != null)
			{
                title = file_attr.title;
				file_types = file_attr.file_types;
			}
		}

		public override object GetValue()
		{
			return value;
		}
	}

	[GuidObjectClass]
	[System.Reflection.Obfuscation(Exclude = true)]
	public partial class ColorField : Field
	{
		public string value;    // hex value

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			base.SetValue(v, attr);

			// TODO: check this is a valid color hex?
#if UNITY_STANDALONE || UNITY_EDITOR
			if (v is UnityEngine.Color)
            {
                try
                {
					value = "#" + UnityEngine.ColorUtility.ToHtmlStringRGBA((UnityEngine.Color)v);
				}
                catch
                {
                    value = "#FFFFFFFF";
                }
                
            } else
#endif
			if (v is string)
            {
				value = (string)Convert.ChangeType(v, typeof(string));
			}
		}

		public override object GetValue()
		{
#if UNITY_STANDALONE || UNITY_EDITOR
            if (UnityEngine.ColorUtility.TryParseHtmlString(value, out var unity_color))
                return unity_color;
            else
                return value;
#else
            return value;
#endif
		}

#if UNITY_STANDALONE || UNITY_EDITOR

		public UnityEngine.Color GetColor()
        {
            if (UnityEngine.ColorUtility.TryParseHtmlString(value, out UnityEngine.Color _color))
                return _color;
            else
                return UnityEngine.Color.white;
		}

#endif
	}

#endregion

	#region Presets

	// Entities can optionally have presets, collections of values for their fields
	[System.Reflection.Obfuscation(Exclude = true)]
	public class Preset : SharedObjectMap.GuidObject
    {
        public string   name;
        public bool     is_readonly;    // for default presets
		public Dictionary<string, object>? fields;
	}

	#endregion

	#region Calibration

	[Serializable]
	[System.Reflection.Obfuscation(Exclude = true)]
	public class CalibrationSnapshot
	{
		public SizeI screen_full;
		public RectI screen_crop;

		public List<Vector2i> markers;
	}

	#endregion
}
