//TABULA_GUID:{6DF71AD7-F284-41BD-BCFF-9B851916FAFB}

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Newtonsoft.Json;

#if !UNITY_EDITOR && !UNITY_STANDALONE
using PropertyChanged;
#endif

#if (UNITY_EDITOR || UNITY_STANDALONE) && !SHAREDOBJECTMAP_CLIENT_LIGHT
public class AddINotifyPropertyChangedInterfaceAttribute : Attribute { };
public class DoNotCheckEqualityAttribute : Attribute { };
namespace PropertyChanged { };
#endif

namespace Tabula.Unity
{
    [Serializable]
	[AddINotifyPropertyChangedInterface]
    public class UnityToolData
    {
		public SystemInfo? systeminfo { get; set; }
		
		public ObservableCollection<Tabula.Unity.Display> displays { get; set; }
	}

	[Serializable]
	[AddINotifyPropertyChangedInterface]
	public class SystemInfo
	{
		public string deviceModel { get; set; }
		public string deviceName { get; set; }
		public string deviceType { get; set; }
		public string deviceUniqueIdentifier { get; set; }

		public string operatingSystem { get; set; }
		public string operatingSystemFamily { get; set; }
		public int systemMemorySize { get; set; }

		public string dataPath { get; set; }

		public int graphicsDeviceID { get; set; }
		public string graphicsDeviceName { get; set; }
		public string graphicsDeviceType { get; set; }
		public string graphicsDeviceVendor { get; set; }
		public string graphicsDeviceVersion { get; set; }
		public int graphicsMemorySize { get; set; }
		public int graphicsShaderLevel { get; set; }
		public int maxTextureSize { get; set; }

	}

	// Writes the found displays as a json object
	[Serializable]
	[AddINotifyPropertyChangedInterface]
	public class Display
	{
		public string name { get; set; }
		public int system_width { get; set; }
		public int system_height { get; set; }

		public int rendering_width { get; set; }
		public int rendering_height { get; set; }
		public bool active { get; set; }

		public bool IsCurrentScreen  => active;

		public string Description
			=> $"{system_width} x {system_height}" + $"\n\n{name}";

		public int MonitorIndex => data != null ? data.displays.IndexOf(this) + 1 : 0;

		[JsonIgnore]
		public UnityToolData data;
	}
}
