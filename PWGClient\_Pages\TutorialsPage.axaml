<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.TutorialsPage">
	<Border Classes="Card" >
		<Grid RowDefinitions="Auto,*">

			<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto">
				<TextBlock Grid.Row="0" Text="Tutorials" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
				<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
				<TextBlock Grid.Row="2" Margin="5,10,0,10">
					Here is a list that will be updated with the <Bold>how-to</Bold> guides and <Bold>tutorials</Bold>.
				</TextBlock>
			</Grid>

			<ScrollViewer Grid.Row="1" VerticalAlignment="Top">
				<ItemsControl ItemsSource="{Binding SARGAME.Tutorials}">

					<ItemsControl.ItemsPanel>
						<ItemsPanelTemplate>
							<WrapPanel/>
						</ItemsPanelTemplate>
					</ItemsControl.ItemsPanel>

					<ItemsControl.ItemTemplate>
						<DataTemplate DataType="{x:Type sargame:TutorialEntry}">
							<Border Margin="10" Classes="Hoverable" >
								<Grid RowDefinitions="Auto,Auto" ClipToBounds="True">
									<Button  Grid.Row="0" BorderThickness="0" Classes="Basic" BorderBrush="Transparent" Width="256" Height="144" Margin="10" Padding="0" Click="bt_tutorial_click">
										<Grid>
											<Image  Source="{Binding ThumbnailPath, Converter={StaticResource BitmapValueConverter}}" RenderOptions.BitmapInterpolationMode="HighQuality"/>
											<!--
											<Image Source="/Images/overlay_new.png" RenderOptions.BitmapInterpolationMode="HighQuality" IsVisible="{Binding IsNew}"/>
											<Image Source="/Images/overlay_try.png" RenderOptions.BitmapInterpolationMode="HighQuality" IsVisible="{Binding !IsLicensed}"/>
											-->
										</Grid>
									</Button>
									<TextBlock Grid.Row="1" Margin="14,10,10,14" FontWeight="Bold" Classes="h5" Text="{Binding title}"/>																	
								</Grid>
							</Border>
						</DataTemplate>
					</ItemsControl.ItemTemplate>

				</ItemsControl>
			</ScrollViewer>
					
		</Grid>
	</Border>
	
</UserControl>
