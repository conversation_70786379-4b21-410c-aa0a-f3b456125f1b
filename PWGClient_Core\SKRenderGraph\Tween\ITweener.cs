using System;
using System.Collections.Generic;
using System.Text;

namespace Tabula.SKRenderGraph.Tween
{
    public delegate void ValueChangedHandler<T>(T newValue);
    public delegate void ValueDeltaChangedHandler<T>(T deltaValue);

    public delegate void EndHandler();

    public interface ITweener
    {
        [Obsolete("Use Playing property instead")]
        bool Running { get;}
        bool Playing { get;}
        event EndHandler Ended;

        void Update(float time);

        void Play();
        void Pause();
        void Reset();
        void Restart();
        void Reverse();
    }

    public interface ITweener<T> : ITweener
    {
        T Value { get;}
        event ValueChangedHandler<T> ValueChanged;

        void Reset(T to);
        void Reset(T to, TimeSpan duration);
        void Reset(T to, float speed);
        void Reset(T from, T to, TimeSpan duration);
        void Reset(T from, T to, float speed);
    }
}
