<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:wpf="clr-namespace:Material.Styles.Assists;assembly=Material.Styles"
             xmlns:avalonia="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             xmlns:local="clr-namespace:Tabula.PWGClient"
			 mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.ScreenEditorPanel">
  <Grid>
	  
	  <local:ScreenEditor Name="screen_editor" Background="Transparent"/>
	  
	  <!-- Info box -->
	  <local:InfoBox IsVisible="{Binding InfoBox.IsVisible}"/>
	
  </Grid>
</UserControl>
