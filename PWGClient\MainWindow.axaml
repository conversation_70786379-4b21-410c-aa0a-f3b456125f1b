<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"	
		xmlns:suki_custom="clr-namespace:SukiUI.Controls"		
        xmlns:local="clr-namespace:Tabula.PWGClient"
		mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="Tabula.PWGClient.MainWindow"
		 Icon="/Images/icon_128.png"
		Title="{Binding WindowTitle}">
	<Window.Styles>
		<Style Selector="Button.Blinking">
			<Style.Animations>
				<Animation Duration="0:0:1">
					<KeyFrame Cue="0%">
						<Setter Property="Opacity" Value="0.0"/>
					</KeyFrame>
					<KeyFrame Cue="100%">
						<Setter Property="Opacity" Value="1.0"/>
					</KeyFrame>
				</Animation>
			</Style.Animations>
		</Style>
	</Window.Styles>
		<local:MainView />
	<!--
	<suki:InteractiveContainer>
		<suki_custom:DesktopPage2
            LogoColor="White"			
            LogoKind="SpaceInvaders"	
			LogoImage="Images/icon_128.png"
            MenuVisibility="False"
			TitleFontWeight="Bold"
            Title="S-ARGAME Editor">
			<local:MainView />
		</suki_custom:DesktopPage2>
	</suki:InteractiveContainer>
	-->
</Window>
