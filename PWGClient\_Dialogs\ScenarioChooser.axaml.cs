using Avalonia.Controls;
using Avalonia.Interactivity;
using Org.BouncyCastle.Asn1.Cmp;
using PropertyChanged;
using Sentry;
using System;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class ScenarioChooser : UserControl
	{
		public string dialog_identifier = "scenariochooser_dialog";

		public ScenarioChooser()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			SARGAME.Instance.LoadScenarios();

			base.OnLoaded(e);
		}

		public static async Task<ScenarioEntry> Show()
		{
			var ret = await DialogHostAvalonia.DialogHost.Show( new ScenarioChooser());

			return ret as ScenarioEntry;
		}

		async void bt_choose_Click(object sender, RoutedEventArgs args)
		{
			ScenarioEntry s = (sender as Control).DataContext as ScenarioEntry;

			//await DialogTest.Show("scenariochooser_dialog");

			DialogHostAvalonia.DialogHost.Close("dialog", s);

			// Test: save the scenario names
			SARGAME.Instance.SaveScenariosIfNeeded();
		}

		async void bt_create_new_Click(object sender, RoutedEventArgs args)
		{
			SARGAME.Instance.CreateScenario("<new>");
		}

		async void bt_cancel_Click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", null);
		}

		async void bt_delete_Click(object sender, RoutedEventArgs args)
		{
			ScenarioEntry s = (sender as Control).DataContext as ScenarioEntry;

			var choice = await ProgressDialog.ShowChoicesAsync("Remove the Scenario?", new string[] { "Yes", "No" }, identifier: dialog_identifier);

			if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
				return;

			try
			{
				s.Delete();

				// await ProgressDialog.ShowMessageAsync("Scenario removed", identifier: dialog_identifier);
			}
			catch (Exception ex)
			{
				await ProgressDialog.ShowMessageAsync("Error removing scenario", identifier: dialog_identifier);
			}

			SARGAME.Instance.LoadScenarios();
		}
	}
}
