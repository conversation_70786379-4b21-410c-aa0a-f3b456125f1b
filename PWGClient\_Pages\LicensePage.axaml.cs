using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using Tabula.Licensing.LicenseActivator;
using Sentry;
using Tabula.Licensing;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class LicensePage : UserControl
	{
		public LicensePage()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			App.Instance.CheckLicense();

			base.OnLoaded(e);
		}

		async void bt_activate_Click(object sender, RoutedEventArgs args)
		{
			if (string.IsNullOrEmpty(MainWindowUI.Instance.EditedLicenseSerial))
			{
				await ProgressDialog.ShowMessageAsync("Please enter a license code.");
				return;
			}

			MainWindowUI.Instance.IsBusy = true;

			var cts = new CancellationTokenSource();

			var msgbox = ProgressDialog.ShowMessage(
				message: "Activating license...",
				progress: true,
				cancel_button_text: "Cancel",
				cancellation_token: cts.Token,
				on_cancel_button_click: () => cts.Cancel()
				);

			int activate_ret = 0;
			int validate_match = 0;	// return of validate call in case activate fails

			try
			{
				await Task.Run(async () =>
				{
					// try to get also unityuniqueid
					var unitytool_data = await SARGAME.Instance.GetLastUnityToolDataOrInvoke();

					activate_ret = LicenseActivatorLib.ActivateLicense(
						MainWindowUI.Instance.EditedLicenseSerial, 
						unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
						use_legacy_hid: true,
						platform: (OperatingSystem.IsWindows() ? DeviceData.Platform_Windows : (OperatingSystem.IsMacOS() ? DeviceData.Platform_MacOS : null)));

					// a -2 error means no activation could be done (TODO: better having return codes than badrequests!)
					// so we try a ValidateLicense() to see if there is already a license on the server

					if (activate_ret == -2)
					{
						validate_match = LicenseActivatorLib.ValidateLicense(MainWindowUI.Instance.EditedLicenseSerial,
							unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
							use_legacy_hid: true);

						// the match is returned, here some sort of heuristic can be used:
						// -4: it is completely different, probably another machine, we could prompt to reactivate here
						// <=-2: same as this license but more than 1 HID is changed
						switch (validate_match) 
						{
							case 1:
							case -1:
								// perfect or fuzzy 1.. should not happen, activate should have worked
								break;

							case -2:
							case -3:
								// too fuzzy 
								break;

							case -4: // completely different
								break;

							// -1000: error, no serial found
							// -1001: error, no generated license yet
							// -1002: error, invalid license data

							case -1000:
								break;

							case -1001:
								break;

							case -1002:
								break;
							
						}
					}

				}, cts.Token);
			}
			catch(OperationCanceledException)
			{
				MainWindowUI.Instance.IsBusy = false;
				msgbox.Close();
				return;
			}
			catch(Exception ex)
			{
				msgbox.Close();

				await ProgressDialog.ShowMessageAsync("Exception activating license");
				SARGAME.App.logException("License activate", ex);
			}

			msgbox.Close();

			if (activate_ret == 1)
			{
				await ProgressDialog.ShowMessageAsync("License activated.\n\nEditor will now restart.");
				//MainWindow.Instance.Close();
				await App.RestartAsync();
				return;
			}
			else
			{
				SARGAME.App.logError($"Error activating license, activate_ret={activate_ret}, validate_match={validate_match}");

				bool reactivate = false;

				// TODO: different messages depending on error, unfortunately ActivateLicense() gives just bad-request
				switch (validate_match)
				{
					case 1:
					case -1:
						await ProgressDialog.ShowMessageAsync($"Error activating license.\n\nCodes: {activate_ret} / {validate_match}");
						// perfect or fuzzy 1.. should not happen, activate should have worked
						break;

					case -2:
					case -3: // too fuzzy, but could be this machine
						await ProgressDialog.ShowMessageAsync($"Some hardware components have changed and License is now invalid. Please contact support.\n\nCodes: {activate_ret} / {validate_match}");
						break;

					case -4: // completely different, should be a different machine
						await ProgressDialog.ShowMessageAsync($"License is already activated on another machine.\n\nCodes: {activate_ret} / {validate_match}");
						break;

					default:
						await ProgressDialog.ShowMessageAsync($"Error activating license.\n\nCodes: {activate_ret} / {validate_match}");
						break;
				}

			}

			// App.Instance.CheckLicense();
			// MainWindowUI.Instance.EditedLicenseSerial = MainWindowUI.Instance.LicenseSerial;
			// MainWindowUI.Instance.LicenseNeedRestart = true;

			MainWindowUI.Instance.IsBusy = false;
		}

		async void bt_activate_trial_Click(object sender, RoutedEventArgs args)
		{
			/*
			if (string.IsNullOrEmpty(MainWindowUI.Instance.EditedLicenseSerial))
			{
				await ProgressDialog.ShowMessageAsync("Please enter a license code.");
				return;
			}
			*/

			// TODO: Ask for email

			MainWindowUI.Instance.IsBusy = true;

			var cts = new CancellationTokenSource();

			var msgbox = ProgressDialog.ShowMessage(
				message: "Activating Trial...",
				progress: true,
				cancel_button_text: "Cancel",
				cancellation_token: cts.Token,
				on_cancel_button_click: () => cts.Cancel()
				);

			int activate_ret = 0;

			try
			{
				await Task.Run(async () =>
				{
					// try to get also unityuniqueid
					var unitytool_data = await SARGAME.Instance.GetLastUnityToolDataOrInvoke();

					//  1: OK
					// -3: ERR license.bytes is null
					activate_ret = LicenseActivatorLib.ActivateTrial(
						email: "", 
						unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
						use_legacy_hid: true);

					// a -2 error means no activation could be done (TODO: better having return codes than badrequests!)
					// so we try a ValidateLicense() to see if there is already a license on the server					
				}, cts.Token);
			}
			catch (OperationCanceledException)
			{
				MainWindowUI.Instance.IsBusy = false;
				msgbox.Close();
				return;
			}
			catch (Exception ex)
			{
				msgbox.Close();

				await ProgressDialog.ShowMessageAsync("Exception activating trial license");
				SARGAME.App.logException("Trial License activate", ex);
			}

			msgbox.Close();

			if (activate_ret == 1)
			{
				await ProgressDialog.ShowMessageAsync("Trial License activated.\n\nEditor will now restart.");
				//MainWindow.Instance.Close();
				await App.RestartAsync();
				return;
			}
			else
			{
				SARGAME.App.logError($"Error activating trial license, activate_ret={activate_ret}");
			}

			// App.Instance.CheckLicense();
			// MainWindowUI.Instance.EditedLicenseSerial = MainWindowUI.Instance.LicenseSerial;
			// MainWindowUI.Instance.LicenseNeedRestart = true;

			MainWindowUI.Instance.IsBusy = false;
		}




		async void bt_update_Click(object sender, RoutedEventArgs args)
		{
			// NOTE: update always on current serial, not the edited one

			if (string.IsNullOrEmpty(MainWindowUI.Instance.LicenseSerial))
			{
				await ProgressDialog.ShowMessageAsync("No license serial to update!");
				return;
			}

			MainWindowUI.Instance.IsBusy = true;

			var cts = new CancellationTokenSource();

			var msgbox = ProgressDialog.ShowMessage(
				message: "Updating license...",
				progress: true,
				cancel_button_text: "Cancel",
				cancellation_token: cts.Token,
				on_cancel_button_click: () => cts.Cancel()
				);

			int activate_ret = 0;

			try
			{
				await Task.Run(async () =>
				{
					var unitytool_data = await SARGAME.Instance.GetLastUnityToolDataOrInvoke();

					activate_ret = LicenseActivatorLib.ActivateLicense(
						MainWindowUI.Instance.LicenseSerial,
						unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
						use_legacy_hid: true,
						platform: (OperatingSystem.IsWindows() ? DeviceData.Platform_Windows : (OperatingSystem.IsMacOS() ? DeviceData.Platform_MacOS : null)));;

				}, cts.Token);
			}
			catch (OperationCanceledException)
			{
				MainWindowUI.Instance.IsBusy = false;
				msgbox.Close();
				return;
			}
			catch (Exception ex)
			{
				msgbox.Close();

				await ProgressDialog.ShowMessageAsync("Exception updating license");
				SARGAME.App.logException("License update", ex);
			}

			msgbox.Close();

			if (activate_ret == 1)
			{
				await ProgressDialog.ShowMessageAsync("License updated.\n\nEditor will now restart.");
				//MainWindow.Instance.Close();
				await App.RestartAsync();
				return;
			}
			else
			{
				SARGAME.App.logError($"Error updating license, ret={activate_ret}");
				await ProgressDialog.ShowMessageAsync("Error updating license");
			}

			// App.Instance.CheckLicense();
			// MainWindowUI.Instance.LicenseNeedRestart = true;
			MainWindowUI.Instance.IsBusy = false;
		}

		async void bt_deactivate_Click(object sender, RoutedEventArgs args)
		{
			if (string.IsNullOrEmpty(MainWindowUI.Instance.EditedLicenseSerial))
			{
				await ProgressDialog.ShowMessageAsync("Please enter a license code.");
				return;
			}

			var cts = new CancellationTokenSource();

			MainWindowUI.Instance.IsBusy = true;

			var msgbox = ProgressDialog.ShowMessage(
				message: "Deactivating license...",
				progress: true,
				cancel_button_text: "Cancel",
				cancellation_token: cts.Token,
				on_cancel_button_click: () => cts.Cancel()
				);

			int deactivate_ret = 0;

			try
			{
				await Task.Run(async () =>
				{
					var unitytool_data = await SARGAME.Instance.GetLastUnityToolDataOrInvoke();

					deactivate_ret = LicenseActivatorLib.DeactivateLicense(MainWindowUI.Instance.EditedLicenseSerial,
						unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
						use_legacy_hid: true);

				}, cts.Token);
			}
			catch (OperationCanceledException)
			{
				MainWindowUI.Instance.IsBusy = false;
				msgbox.Close();
				return;
			}
			catch (Exception ex)
			{
				msgbox.Close();

				await ProgressDialog.ShowMessageAsync("Exception deactivating license");
				SARGAME.App.logException("License deactivation", ex);
			}

			msgbox.Close();

			if (deactivate_ret == 0)
			{
				await ProgressDialog.ShowMessageAsync("License deactivated.\n\nEditor will now restart.");
				//MainWindow.Instance.Close();
				await App.RestartAsync();
				return;
			}
			else
			{
				SARGAME.App.logError($"Error deactivating license, ret={deactivate_ret}");

				// -1: ERR serial is needed
				// -2: ERR in LicenseValidate bad call
				// -3: ERR license is too different (validate return)
				// -4: ERR cannot deactivate licensing system
				// -5: ERR in LicenseDeactivate bad call
				// -6: ERR license is too different (deactivate return)
				switch (deactivate_ret)
				{
					case -2:
					case -5:
						await ProgressDialog.ShowMessageAsync($"Server error deactivating license.\n\nCode: {deactivate_ret}");
						break;

					case -4:
						await ProgressDialog.ShowMessageAsync($"Internal licensing system errror.\nPlease contact support.\n\nCode: {deactivate_ret}");
						break;

					case -3:
					case -6:
						await ProgressDialog.ShowMessageAsync($"Current license has too different hardware.\nPlease contact support to deactivate license.\n\nCode: {deactivate_ret}");
						break;

					default:
						await ProgressDialog.ShowMessageAsync($"Generic error deactivating license.\n\nCode: {deactivate_ret}");
						break;
				}

			}

			App.Instance.CheckLicense();

			MainWindowUI.Instance.LicenseNeedRestart = true;

			MainWindowUI.Instance.IsBusy = false;
		}
	}

	

}
