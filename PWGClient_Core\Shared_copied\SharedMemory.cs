//TABULA_GUID:{92F36384-1C8C-466C-99E7-7AC46E9EF3A2}
using System;
//using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;

// Needs /unsafe

namespace Tabula
{
    public static class SharedMemoryNativeMethods
    {
        [Flags]
        public enum PageProtection : uint
        {
            NoAccess = 0x01,
            Readonly = 0x02,
            ReadWrite = 0x04,
            WriteCopy = 0x08,
            Execute = 0x10,
            ExecuteRead = 0x20,
            ExecuteReadWrite = 0x40,
            ExecuteWriteCopy = 0x80,
            Guard = 0x100,
            NoCache = 0x200,
            WriteCombine = 0x400,
        }

        public const int INVALID_HANDLE_VALUE = -1;
        public const int FILE_MAP_WRITE = 0x0002;
        public const int ERROR_ALREADY_EXISTS = 183;

        public const int PAGE_READWRITE = 0x04;

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr CreateFileMapping(int hFile,
            IntPtr lpFileMappingAttributes, PageProtection flProtect,
            uint dwMaximumSizeHigh,
            uint dwMaximumSizeLow, string lpName);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr MapViewOfFile(IntPtr hFileMappingObject, uint dwDesiredAccess, uint dwFileOffsetHigh, uint dwFileOffsetLow, uint dwNumberOfBytesToMap);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr OpenFileMapping(uint dwDesiredAccess, bool bInheritHandle, string lpName);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool UnmapViewOfFile(IntPtr lpBaseAddress);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll")]
        static extern uint GetLastError();


        public static IntPtr getSharedMemoryNative(string shname, uint size, bool create, ref IntPtr map)
        {
            IntPtr m_hMap = IntPtr.Zero;

            m_hMap = SharedMemoryNativeMethods.OpenFileMapping(SharedMemoryNativeMethods.FILE_MAP_WRITE, false, shname);
            if (m_hMap.Equals(IntPtr.Zero))
            {
                if (create)
                    m_hMap = SharedMemoryNativeMethods.CreateFileMapping(SharedMemoryNativeMethods.INVALID_HANDLE_VALUE, IntPtr.Zero, SharedMemoryNativeMethods.PageProtection.ReadWrite, 0, size, shname);
                else
                    return IntPtr.Zero;
            }

            IntPtr m_pMgs = SharedMemoryNativeMethods.MapViewOfFile(m_hMap, SharedMemoryNativeMethods.FILE_MAP_WRITE, 0, 0, size);

            if (m_pMgs.Equals(IntPtr.Zero))
                return IntPtr.Zero;

            map = m_hMap;

            return m_pMgs;
        }
    }

    public abstract class SharedMemoryBase
    {
        protected IntPtr map = IntPtr.Zero;

        public string Name { get; protected set; }
        public int Size { get; protected set; } = 0;
        public IntPtr Data { get; protected set; } = IntPtr.Zero;

        public bool IsOpen => (!Data.Equals(IntPtr.Zero));

        public virtual void Close()
        {
            if (!Data.Equals(IntPtr.Zero))
                SharedMemoryNativeMethods.UnmapViewOfFile(Data);
            if (!map.Equals(IntPtr.Zero))
                SharedMemoryNativeMethods.CloseHandle(map);

            Data = IntPtr.Zero;
            map = IntPtr.Zero;
        }


    }


    // Marshaled memory for managed classes/structs
    public class SharedMemory<T> : SharedMemoryBase
    {
        // backing object
        private T obj = default(T);

        // Helper to check for updates using peekInt or peekUint
        protected int last_update_int = 0;
        protected uint last_update_uint = 0;

        public SharedMemory()
        { }

        public SharedMemory(string shname, bool create_if_nonexisting)
        {
            Name = shname;
            Open(shname, create_if_nonexisting);
        }

        ~SharedMemory()
        {
            Close();
        }

        public T Object
        {
            get { return obj; }
        }

        public virtual bool Open(string shname, bool create_if_nonexisting)
        {
            Name = shname;
            Size = (int)Marshal.SizeOf(typeof(T));
            Data = SharedMemoryNativeMethods.getSharedMemoryNative(shname, (uint)Size, create_if_nonexisting, ref map);

            return (!Data.Equals(IntPtr.Zero));
        }

        // reads and close an existing sharedmem, don't do this too frequently
        public static bool ReadAndClose(string shname, out T data)
        {
            var shmem = new SharedMemory<T>();

            data = default(T);

            if (shmem.Open(shname, false))
            {
                data = shmem.Read();
                shmem.Close();

                return true;
            }
            else
                return false;
        }

        public T Read()
        {
            try
            {
                return (T)Marshal.PtrToStructure(Data, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }

        public void Write(T obj, bool deleteOld = false)
        {
            Marshal.StructureToPtr(obj, Data, deleteOld);
        }

        // peeks into structure header, good for optimizations
        public int peekInt32()
        {
            unsafe
            {
                int* pint = (int*)Data.ToPointer();
                return *pint;
            }
        }

        public uint peekUint32()
        {
            unsafe
            {
                uint* pint = (uint*)Data.ToPointer();
                return *pint;
            }
        }

        public unsafe byte* peek(int offset)
        {
            return (byte*)Data.ToPointer() + offset;
        }

        // checks the update int peeking
        public bool IsUpdatedInt
        {
            get
            {
                int upd_int = peekInt32();
                if (upd_int != last_update_int)
                {
                    last_update_int = upd_int;
                    return true;
                }
                else
                    return false;
            }
        }

        // checks the update int peeking
        public bool IsUpdatedUint
        {
            get
            {
                uint upd_uint = peekUint32();
                if (upd_uint != last_update_uint)
                {
                    last_update_uint = upd_uint;
                    return true;
                }
                else
                    return false;
            }
        }
    }


    public class SharedMemoryUnsafe : SharedMemoryBase
    {
        public bool IsAvailable => Data != IntPtr.Zero;

        public SharedMemoryUnsafe()
        { }

        public SharedMemoryUnsafe(string shname, int sh_size, bool create_if_nonexisting)
        {
            Name = shname;
            Open(shname, sh_size, create_if_nonexisting);
        }

        public virtual bool Open(string shname, int sh_size, bool create_if_nonexisting)
        {
            Name = shname;
            Size = sh_size;
            Data = SharedMemoryNativeMethods.getSharedMemoryNative(shname, (uint)Size, create_if_nonexisting, ref map);

            return (!Data.Equals(IntPtr.Zero));
        }
    }
}