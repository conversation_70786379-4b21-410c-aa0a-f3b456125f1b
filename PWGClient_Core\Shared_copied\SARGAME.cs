//TABULA_GUID:{81A3E15F-F44C-4DCB-8F7C-F77F905920A4}

// This class embeds all logic for local installation (scenes etc..)
// Needs LocalAppConfig to be already setup

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Tabula.PMCore;
using Tabula.Unity;
using System.Diagnostics;
using System.Threading;
using System.Net.Http;
using Sentry;
using System.Xml.Linq;
using System.ComponentModel;

#if UNITY_EDITOR || UNITY_STANDALONE
using Tabula.PMCore.Unity;
#endif

#if !UNITY_EDITOR && !UNITY_STANDALONE
using PropertyChanged;
using System.Net.Http;
using static Tabula.SharedObjectMap.SharedObjectMap;
using System.Threading;
using NetSparkleUpdater.AppCastHandlers;
using NetSparkleUpdater.Enums;
using NetSparkleUpdater.SignatureVerifiers;
using NetSparkleUpdater;
using System.IO.Compression;
using System.Net.NetworkInformation;
using static Tabula.PWG.SARGAME.SARGAME;
using Avalonia.Threading;
using System.Drawing;
using System.Runtime.InteropServices;
#else

// Stubs

public class BasePropertyChanged 
{
	public void RaisePropertyChanged(string propertyName)
	{}
}

public class DependsOnAttribute : System.Attribute
{
	public DependsOnAttribute(string dependency)
	{}

	public DependsOnAttribute(string dependency, params string[] otherDependencies)
	{}
}
#endif

#if UNITY_EDITOR || UNITY_STANDALONE

namespace System
{
	public static class OperatingSystem
	{
		public static bool IsWindows() => PMCore_Server.IsWindows;
		public static bool IsMacOS() => PMCore_Server.IsMac;
	}
}

#endif

// Define SARGAME_EDITOR for editor usage (Editor)


#if SARGAME_EDITOR
using CliWrap;
using SkiaSharp;
using Tabula.PWGClient;
using System.Text.RegularExpressions;
using Tabula.PWG.SARGAME.Utils;
#endif

namespace Tabula.PWG.SARGAME
{
	[AddINotifyPropertyChangedInterface]
	public class SARGAME : BasePropertyChanged
	{
		public const int Version = 10;   // protocol version, server will valorize it in the model

#if !UNITY_EDITOR && !UNITY_STANDALONE

		// the view of the Model, when active and received
		private static ModelView _view;
		public static ModelView View
		{
			get
			{
				if (_view == null)
				{
					System.Diagnostics.Debug.WriteLine("SARGAME.View is not already set!");
					//throw new Exception("SARGAME.View is not already set!");
					return null;
				}

				return _view;
			}

			set => _view = value;
			
		}

		private static Model _model;
		public static Model		Model
		{
			get
			{
				if (_model == null)
				{
					System.Diagnostics.Debug.WriteLine("SARGAME.Model is not already set!");
					//throw new Exception("SARGAME.View is not already set!");
					return null;
				}

				return _model;
			}

			set => _model = value;
		}

		public static dynamic			iMainWindowUI { get; set; } // complete bridge to UI/App

		// Json setup
		public static JsonSerializerSettings JsonSettings = new JsonSerializerSettings
		{
			Formatting = Formatting.Indented,
			TypeNameHandling = TypeNameHandling.Auto,
			NullValueHandling = NullValueHandling.Ignore
			,
			SerializationBinder = new IgnoreAssemblySerializationBinder()
		};

		public class IgnoreAssemblySerializationBinder : Newtonsoft.Json.Serialization.DefaultSerializationBinder
		{
			public override Type BindToType(string assemblyName, string typeName)
			{
				return Type.GetType(_type_fix(typeName));
			}

			public override void BindToName(Type serializedType, out string assemblyName, out string typeName)
			{
				assemblyName = null;

				// Special handling for lists
				string typeFullName = serializedType.FullName;
				typeName = _type_fix(typeFullName);
			}

			string _type_fix(string typeFullName)
			{
				if (typeFullName.StartsWith("System.Collections.Generic.List`1[["))
				{
					// make sure contained type is only serialized as name not fully qualified
					string pattern = @"(?<=\[\[).+?(?=,)";

					Match match = Regex.Match(typeFullName, pattern);

					if (match.Success)
					{
						return Regex.Replace(typeFullName, @"\[\[.+?\]\]", $"[[{match.Value}]]");
					}
				}

				return typeFullName;
			}
		}
#endif

		public static LocalAppConfig App { get; private set; }

		// Store URL (valorized at runtime)
		public string StoreUrl;

		public const string StoreUrlWin = "https://sargame-cdn.tabulatouch.com/win_x64/store/store.json";
		public const string StoreUrlMac = "https://sargame-cdn.tabulatouch.com/macos_x64/store/store.json";

		public const string StoreMediaUrlWin = "https://sargame-cdn.tabulatouch.com/win_x64/store/media";
		public const string StoreMediaUrlMac = "https://sargame-cdn.tabulatouch.com/macos_x64/store/media";

		public const string TutorialsBaseUrl = "https://sargame-cdn.tabulatouch.com/common/tutorials";
		public const string TutorialsListUrl = TutorialsBaseUrl + "/tutorials.json";

		public string EditorApplicationSettingsPath => Path.Combine(App.RootFolder, "settings.json");
		public string StoreFolder => Path.Combine(App.RootFolder, "_store");
		public string StorePath => Path.Combine(StoreFolder, "store.json");
		public string StoreCacheFolder => Path.Combine(StoreFolder, "_cache");  // cache for installers
		public string StoreMediaModulesCacheFolder => Path.Combine(StoreFolder, "_cache", "_modulescache");  // cache for downloaded media (modules thumbnails)
		public string TutorialsMediaCacheFolder => Path.Combine(App.RootFolder, "_tutorials");  // cache for downloaded tutorials thumbs


		public string EditorShippedStoreWinPath => Path.Combine("_sargame", "store_win.json");
		public string EditorShippedStoreMacPath => Path.Combine("_sargame", "store_mac.json");

		public Store CurrentStore { get; private set; }

		public List<TutorialEntry> Tutorials { get; set; } = new List<TutorialEntry>();

		// Local media cache used for avoid downloading all thumbnails / guides, shipped along editor
		public string EditorShippedPackagesCacheFolder => Path.Combine("_sargame", "_packagescache");
		public string EditorShippedModulesCacheFolder => Path.Combine("_sargame","_modulescache");


		// names of the package, to avoid depth searches
		public readonly List<string> PackageNames = new List<string>()
		{
			"S-ARGAME_StarterPack",
			"S-ARGAME_KinetikPack",
			"S-ARGAME_RoadPack",
			"S-ARGAME_TabletopPack",

			"S-ARGAME_HalloweenXP",
			"S-ARGAME_HolidayXP",
			"S-ARGAME_DanceXP",

			"S-ARGAME_80sPack",
			"S-ARGAME_90sPack",
			"S-ARGAME_InteractiveWall"
		};

		// Folder containing the USER projects (copied once from the package.ProjectsFolder)
		public string UserProjectsFolder => Path.Combine(App.RootFolder, "_projects");

		// Folder containing saved scenes calibrations
		public string ScenariosFolder => Path.Combine(App.RootFolder, "_scenarios");


		public ObservableCollection<ScenarioEntry> Scenarios { get; private set; } = new ObservableCollection<ScenarioEntry>();

		public ObservableCollection<PackageEntry> Packages { get; private set; } = new ObservableCollection<PackageEntry>();

		// Helper to quickly go through all installed modules (not checking for license)
		public ObservableCollection<PackageModule> Modules { get; private set; } = new ObservableCollection<PackageModule>();

		// Only licensed modules (using wildcards) will be shown
		[DependsOn(nameof(Modules))]
		public ObservableCollection<PackageModule> LicensedModules
		{
			get
			{
				var licensed_modules = new ObservableCollection<PackageModule>();

				foreach (var m in Modules)
				{
					// License feature will be checked with wildcard, so pack1/game1 will be licensed if license contains feature pack1/*
					// NOTE: keep handling null strings for now...
					if (string.IsNullOrEmpty(m.license_feature) || App.CheckLicenseFeature(m.license_feature, wildcard: true))
						licensed_modules.Add(m);
				}

				return licensed_modules;
			}
		}

		public string ModuleProcessInfoPath => Path.Combine(App.RootFolder,"module_process.json");

		public Process CurrentModuleProcess { get; set; }

		// UnityTool
		public string UnityToolPath { get; set; }
		public string UnityToolOutputPath => Path.Combine(App.RootFolder, "UnityTool", "unitytool.json");


		public bool IsLicenseError { get; set; } = false;

		// Events
		public static Action<PackageModule, int> onPackageModuleExited;

		public const int ERROR_NoProjectFile = -91;
		public const int ERROR_NoProjectTemplateFile = -92;
		public const int ERROR_ExceptionCopyingProjectTemplateFile = -93;
		public const int ERROR_MissingLicenseFeature = -101;

		public static SARGAME Instance { get; private set; }

		#region Constructor

		public SARGAME()
		{
#if !UNITY_EDITOR && !UNITY_STANDALONE
			JsonConvert.DefaultSettings = () => SARGAME.JsonSettings;
#endif
		}

		#endregion

		#region Load

		// RETURN VALUES:
		// 1:ok
		// -100: no LocalAppConfig
		// -101: exception creating base folders
		// -102: exception loading scenarios
		// -103: exception loading packages

		public int Load()
		{
			if (LocalAppConfig.Instance == null)
			{
				SentrySdk.CaptureMessage("SARGAME.Load() needs Local config to be setup");
				return -100;
			}

			App = LocalAppConfig.Instance;

			try
			{
				Directory.CreateDirectory(StoreFolder);
				Directory.CreateDirectory(UserProjectsFolder);
				Directory.CreateDirectory(ScenariosFolder);
				Directory.CreateDirectory(StoreCacheFolder);
				Directory.CreateDirectory(StoreMediaModulesCacheFolder);
				Directory.CreateDirectory(TutorialsMediaCacheFolder);
			}
			catch(Exception ex) 
			{
				// Filesystem permission problems
				App.logException("SARGAME.Load() create folders", ex);
				return -101;
			}

#if SARGAME_EDITOR

			// Platform-specific paths
			if (OperatingSystem.IsWindows())
			{
				StoreUrl = StoreUrlWin;
				UnityToolPath =  Path.Combine("_unitytool", "UnityTool.exe");
			}
			else if (OperatingSystem.IsMacOS())
			{
				StoreUrl = StoreUrlMac;
				UnityToolPath = Path.Combine("_unitytool", "UnityTool.app", "Contents", "MacOS", "UnityTool");
			}

			StoreUrl = OperatingSystem.IsWindows() ? StoreUrlWin : StoreUrlMac;

			App.log($"Load() StoreUrl={StoreUrl}");

			// Copy default scenarios if none are present
			if (Directory.GetDirectories(ScenariosFolder).Length == 0)
			{
				try
				{
					if (Directory.Exists(Path.Combine("_sargame", "_scenarios")))
					{
						OSUtilities.copyDirectory(
							Path.Combine("_sargame", "_scenarios"),
							ScenariosFolder);
					}
				}
				catch { }
			}
#endif
			// Scenarios are needed in games
			try
			{
				LoadScenarios();
			}
			catch (Exception ex)
			{
				App.logException("SARGAME.Load().LoadScenarios()", ex);
				return -102;
			}

#if SARGAME_EDITOR

			// Packages should no be needed in games
			/*
			try
			{
				LoadInstalledPackagesAndModules();
			}
			catch (Exception ex)
			{
				App.logException("SARGAME.Load().LoadPackages()", ex);
				return -103;
			}
			*/
#endif

			Instance = this;

#if SARGAME_EDITOR
			LoadEditorApplicationSettings();
#endif
			// catch all module exits
			onPackageModuleExited = PackageModule.OnPackageModuleExited;

			return 1;
		}

		public void LoadScenarios()
		{
			Scenarios.Clear();

			// parse all scenarios
			string[] scenes = Directory.GetFiles(ScenariosFolder, "scenario.json", SearchOption.AllDirectories);

			foreach (string s in scenes)
			{
				var scenario = new ScenarioEntry();

				try
				{
					scenario.Load(s);
					Scenarios.Add(scenario);
				}
				catch(Exception ex)
				{
					// cannot load or other problem
					App.logException("LoadScenarios()", ex);
				}
			}
		}

		public void LoadInstalledPackagesAndModules()
		{
			Packages.Clear();
			Modules.Clear();

			string InstallationPath = "";

			// NOTE: WINDOWS: packages are now installed as normal programs in %LocalAppData%\Programs
			//		 MAC: packages are installed in /Users/<USER>

			if (OperatingSystem.IsWindows())
				InstallationPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs");
			else if (OperatingSystem.IsMacOS())
				InstallationPath = "/Users/<USER>";
			else
				throw new Exception("Unknown OperatingSystem detected!");

			App.log($"LoadPackages() installationPath={InstallationPath}");


			// serach for S-ARGAME installed packages
			
			// NOTE: using this search can lead to exception in file permissions (Mac in particular)
			//string[] packages = Directory.GetFiles(InstallationPath, "sargame_package.json", SearchOption.AllDirectories);
			List<string> packages = new List<string>();

			foreach (var pname in PackageNames)
			{
				try
				{
					if (Directory.Exists(Path.Combine(InstallationPath, pname)))
						if (File.Exists(Path.Combine(InstallationPath, pname, "sargame_package.json")))
							packages.Add(Path.Combine(InstallationPath, pname, "sargame_package.json"));
				}
				catch { }
			}

			foreach (string p in packages)
			{
				var package = new PackageEntry();

				try
				{
					package.Load(p);
					Packages.Add(package);

					// Also reference in Modules for ease of access
					// NOTE: the license_feature will be checked only when showing it
					foreach (var m in package.Package.modules)
						Modules.Add(m);
				}
				catch (Exception ex)
				{
					// cannot load or other problem
					App.logException("LoadPackages()", ex);
				}
			}

			// STORE
			// Now ADD modules that are not already installed, so that user can start trial etc..
			if (CurrentStore != null)
			{
				var modules_installed = (from m in Modules where m.IsInstalled select m.id).ToList();
				foreach (var p in CurrentStore.packages)
					if (p.modules != null)
						foreach (var m in p.modules)
						{
							if (!modules_installed.Contains(m.id))
							{
								// OK this is to be added as not installed
								Modules.Add(new PackageModule()
								{
									id = m.id,
									package_id = p.id,	// necessary because not installed
									name = m.name,
									license_feature = m.license_feature
								});
							}
						}
			}

			RaisePropertyChanged(nameof(Modules));
			RaisePropertyChanged(nameof(LicensedModules));
		}

#endregion

		#region Current process information

		// Once the MODULE application has really started, the process info is writte in the "sargame" folder
		public void WriteProcessInfo(string package_name="", string module_name="")
		{
			var current = Process.GetCurrentProcess();

			var procinfo = new ProcessInformation()
			{
				process_id = current.Id,
				process_name = current.ProcessName,
				package_name = package_name,
				module_name = module_name
			};

			File.WriteAllText(ModuleProcessInfoPath, JsonConvert.SerializeObject(procinfo));
		}

		public (DateTime date, ProcessInformation processinfo) ReadProcessInfo()
		{
			try
			{
				if (!File.Exists(SARGAME.Instance.ModuleProcessInfoPath))
					return (default, null);

				var procinfo = JsonConvert.DeserializeObject<ProcessInformation>(File.ReadAllText(SARGAME.Instance.ModuleProcessInfoPath));
				return (date: new FileInfo(SARGAME.Instance.ModuleProcessInfoPath).LastWriteTime,
						processinfo: procinfo);
			}
			catch(Exception ex)
			{
				App.logException("ReadProcessInfo()", ex);

				// delete for safety
				try
				{
					File.Delete(SARGAME.Instance.ModuleProcessInfoPath);
				}
				catch { }

				return (default, null);
			}
		}

		public PackageModule GetRunningModule()
		{
			var pinfo = SARGAME.Instance.ReadProcessInfo();

			if (pinfo.processinfo == null)
				return null;

			/*
			if (pinfo.processinfo.process_name.Equals("Unity", StringComparison.OrdinalIgnoreCase))
				return null;
			*/

			try
			{
				// will make sure both id and name match in the process
				var process_module = pinfo.processinfo.GetProcess();
				if (process_module == null)
					return null;

				// Return the running module
				return SARGAME.Instance.GetModule(pinfo.processinfo.module_name);
			}
			catch(Exception ex)
			{
				App.logException("GetRunningModule()", ex);
				return null;
			}
		}

		#endregion

#if SARGAME_EDITOR

		#region Editor Application Settings

		public class EditorApplicationSettings
		{
			public string SupportRequestEmail;
		}

		public void LoadEditorApplicationSettings()
		{
			try
			{
				if (File.Exists(Instance.EditorApplicationSettingsPath))
				{
					var settings = JsonConvert.DeserializeObject<EditorApplicationSettings>(File.ReadAllText(Instance.EditorApplicationSettingsPath));

					// apply
					iMainWindowUI.SupportRequestEmail = settings.SupportRequestEmail;
				}
			}
			catch (Exception ex)
			{
				App.logException("LoadEditorApplicationSettings()", ex);
			}
		}

		public void SaveEditorApplicationSettings()
		{
			try
			{
				var settings = new EditorApplicationSettings()
				{
					SupportRequestEmail = iMainWindowUI.SupportRequestEmail
				};

				File.WriteAllText(Instance.EditorApplicationSettingsPath, JsonConvert.SerializeObject(settings));
			}
			catch (Exception ex)
			{
				App.logException("SaveEditorApplicationSettings()", ex);
			}
		}

#endregion

#endif

		#region Store

#if SARGAME_EDITOR

		private bool _get_store_in_progress = false;
		public async Task GetStore()
		{
			// TODO: should be fast in retrieving current store, and parallelize downloading media...

			Store local_store = null;

			// Copy editor-shipped store.json if it doesn't exist yet? in order to have it handy
			if (!File.Exists(StorePath))
			{
				string shipped_store_path = "";
				if (OperatingSystem.IsWindows())
					shipped_store_path = EditorShippedStoreWinPath;
				else
					shipped_store_path = EditorShippedStoreMacPath;

				try
				{
					if (File.Exists(shipped_store_path))
						File.Copy(shipped_store_path, StorePath, true);
				}
				catch (Exception ex)
				{
					App.logException("GetStore() copying shipping store", ex);
				}
			}

			if (File.Exists(StorePath))
				try
				{
					local_store = JsonConvert.DeserializeObject<Store>(File.ReadAllText(StorePath));
				}
				catch(Exception ex)
				{
					App.logException("GetStore() reading local store.json, deleting it", ex);
					File.Delete(StorePath);
				}

			if (_get_store_in_progress && local_store != null)
			{
				CurrentStore = local_store;
				return;
			}

			_get_store_in_progress = true;

			try
			{
				CurrentStore = await DownloadJson<Store>(StoreUrl);

				File.WriteAllText(StorePath, JsonConvert.SerializeObject(CurrentStore));

				App.log($"GetStore(): Downloaded {StoreUrl}");

				// Downloading media for Packages
				foreach (var p in CurrentStore.packages)
				{
					bool has_feature = App.CheckLicenseFeature(p.license_feature, partial_match: true);

					App.log($"GetStore(): found package {p.id} feature={p.license_feature} has_feature={has_feature} media_path={p.MediaPath} media_exist={File.Exists(p.MediaPath)}");

					// download package media
					/*
					bool download_media = false;

					if (string.IsNullOrEmpty(p.license_feature))
						download_media = false;
					else
						download_media = has_feature;
					*/

					// First check if we can copy the editor shipped package media
					if (!File.Exists(p.MediaPath))
					{
						string editor_shipped_package_media = Path.Combine(EditorShippedPackagesCacheFolder, $"{p.id}.png");
						if (File.Exists(editor_shipped_package_media))
							try
							{
								File.Copy(editor_shipped_package_media, p.MediaPath, true);
								p.RaisePropertyChanged(nameof(p.MediaPath));
							}
							catch (Exception ex)
							{
								App.logException("GetStore(): exception copying editor shipped package media", ex);
							}
					}

					//if (download_media && !File.Exists(p.MediaPath))
					if (!File.Exists(p.MediaPath))
					{
						App.log($"GetStore(): Downloading media for package {p.id}: {p.MediaPath}");
						try
						{
							App.log($"GetStore(): downloading {p.media_url} to {p.MediaPath}");

							await DownloadBinary(p.media_url, p.MediaPath);

							p.RaisePropertyChanged(nameof(p.MediaPath));

							App.log($"GetStore(): downloaded {p.media_url} to {p.MediaPath}");
						}
						catch(Exception ex)
						{
							App.logException("GetStore(): exception downloading media", ex);
						}
					}
				}

				// Download media for modules, if not already present
				foreach (var p in CurrentStore.packages)
					if (p.modules!=null)
						foreach (var m in p.modules)
						{
							// check if this thumbnail is already in editor cache
							if (File.Exists(Path.Combine(EditorShippedModulesCacheFolder, $"{m.id.ToLower()}.png")))
								continue;

							// or in local store cache
							string media_path = Path.Combine(StoreMediaModulesCacheFolder, $"{m.id.ToLower()}.png");
							if (File.Exists(media_path))
								continue;

							// otherwise download in local store cache folder
							string media_url = "";
							if (OperatingSystem.IsWindows())
								media_url = StoreMediaUrlWin + $"/{m.id.ToLower()}.png";
							else
								media_url = StoreMediaUrlMac + $"/{m.id.ToLower()}.png"; ;

							App.log($"GetStore(): Downloading media for module {m.id}: {media_path}");
							try
							{
								App.log($"GetStore(): downloading {media_url} to {media_path}");

								await DownloadBinary(media_url, media_path);

								App.log($"GetStore(): downloaded {media_url} to {media_path}");
							}
							catch (Exception ex)
							{
								App.logException($"GetStore(): exception downloading media for module {m.id}", ex);
							}

						}

				_get_store_in_progress = false;

				// Refresh all
				LoadInstalledPackagesAndModules();

				return;
			}
			catch(Exception ex)
			{
				App.logException("GetStore()", ex);
				_get_store_in_progress = false;
				CurrentStore = local_store;
				return;
			}
			finally
			{
				_get_store_in_progress = false;
			}
		}

		public enum StoreOperationResult
		{
			None = 0,
			NoUpdates,
			DownloadSuccesful,
			DownloadError,
			DownloadCanceled,
			AlreadyInstalled
		}

		// Downloads (with progress) to the store cache
		public async Task<(StoreOperationResult result, string filename)> DownloadPackageFromStore(StorePackage package, Version editor_version, IProgress<double> progress=null, CancellationToken token=default)
		{
			string GetFilenameFromUrl(string url)
			{
				// Create a Uri instance from the URL
				Uri uri = new Uri(url);

				// Get the last segment of the URL path
				string lastSegment = uri.Segments[^1];

				// Decode URL-encoded characters and return the filename
				return Uri.UnescapeDataString(lastSegment);
			}

			// will already check the installed version

			// Previous version using Sparkle
			/*
			AppCastItem last_version = await package.CheckForStoreUpdatesUsingSparkle();

			if (last_version == null)
			{
				App.log($"DownloadPackageFromStore(): no updates");
				return (StoreOperationResult.NoUpdates, null);
			}

			App.log($"DownloadPackageFromStore(): update found for package {package.id}, version {last_version.Version}");

			string download_filepath = Path.Combine(StoreCacheFolder,GetFilenameFromUrl(last_version.DownloadLink));
			string download_url = last_version.DownloadLink;
			*/

			var ret = await package.CheckForStoreUpdatesUsingXML(editor_version);
			
			if (ret.url == null)
			{
				App.log($"DownloadPackageFromStore(): no updates");
				return (StoreOperationResult.NoUpdates, null);
			}

			string download_filepath = Path.Combine(StoreCacheFolder, GetFilenameFromUrl(ret.url));

			// TODO: Check if we already have this in our cache and the filesize is ok
			bool skip_download = false;

			if (File.Exists(download_filepath))
			{
				if (new FileInfo(download_filepath).Length == ret.size)
					skip_download = true;
			}

			if (!skip_download)
			{
				try
				{
					await DownloadBinary(ret.url, download_filepath, progress, token);
				}
				catch (OperationCanceledException ex)
				{
					App.logException("DownloadPackageFromStore() download canceled", ex);
					return (StoreOperationResult.DownloadCanceled, null);
				}
				catch (Exception ex)
				{
					App.logException("DownloadPackageFromStore() download exception", ex);
					return (StoreOperationResult.DownloadError, null);
				}

				App.log($"DownloadPackageFromStore(): downloaded {ret.url} in {download_filepath}");
			}
			else
			{
				App.log($"DownloadPackageFromStore(): package already in cache {download_filepath}");
			}

			return (StoreOperationResult.DownloadSuccesful, download_filepath);
		}

		// installs the exe (output will be in %LocalAppData%\Programs )
		public async Task<int> InstallPackage(string filename_exe, bool delete_cache=false)
		{
			int result = 0;

			App.log($"InstallPackage(): {filename_exe}");

			if (OperatingSystem.IsWindows())
			{
				// Just execute the installer in verysilent mode
				var cli_result = await Cli.Wrap(filename_exe)
						.WithArguments(new string[] { "/verysilent" })
						.ExecuteAsync();

				result = cli_result.ExitCode;
			}
			else if (OperatingSystem.IsMacOS())
			{	
				// Let's open finder in the /Users/<USER>
				/*
				string folder = new FileInfo(filename_exe).Directory.FullName;
				App.log($"InstallPackage(): opening folder on Mac {folder}");
				Process.Start("open", $"\"{folder}\"");
				*/

				// Open the package directly
				App.log($"InstallPackage(): opening pkg on Mac {filename_exe}");
				Process.Start("open", $"\"{filename_exe}\"");

				result = 1; // just simulate
			}

			// DELETE cache?
			if (delete_cache)
				File.Delete(filename_exe);

			return result;
		}	

#endif

#endregion

		#region UnityTool

#if SARGAME_EDITOR

		// reads last data, if exists
		public UnityToolData GetLastUnityToolData()
		{
			try
			{
				var last_data = JsonConvert.DeserializeObject<UnityToolData>(File.ReadAllText(UnityToolOutputPath));
				return last_data;
			}
			catch 
			{
				return null;
			}
		}

		public async Task<UnityToolData> GetLastUnityToolDataOrInvoke(bool displays = true, bool systeminfo = true)
		{
			var last_data = GetLastUnityToolData();
			if (last_data != null)
				return last_data;
			else
				return await UnityTool(displays, systeminfo);
		}

		public async Task<UnityToolData> UnityTool(bool displays=true, bool systeminfo=true)
		{
			List<string> args = new List<string>();

			args.Add("-product"); args.Add("sargame");
			args.Add("-module"); args.Add("UnityTool");
			args.Add("-monitor"); args.Add("1");		// NOTE: this will not ensure the desktop monitor

			if (displays)
				args.Add("-displays");

			if (systeminfo)
				args.Add("-systeminfo");

			// remove previous output (only if existing!)
			try
			{
				if (File.Exists(UnityToolOutputPath))
					File.Delete(UnityToolOutputPath);
			}
			catch { }

			var result = await Cli.Wrap(UnityToolPath)
					.WithArguments(args)
					.ExecuteAsync();

			try
			{
				if (result.ExitCode >= 0)
				{
					var output = JsonConvert.DeserializeObject<UnityToolData>(File.ReadAllText(UnityToolOutputPath));

					// some cross reference 
					foreach (var d in output.displays)
						d.data = output;

					App.log($"UnityTool() return={result.ExitCode} displays={output.displays.Count}");

					return output;
				}
				else
				{
					App.logError($"UnityTool() return={result.ExitCode}");

					return null;
				}
			}
			catch (Exception ex)
			{
				App.logException($"UnityTool() return={result.ExitCode}", ex);
				return null;
			}
		}
#endif

		#endregion

		#region Scenarios

		public ScenarioEntry GetScenario(string id)
			=> (from s in Scenarios where s.Id.Equals(id) select s).FirstOrDefault();

		// Creates a new scenario with unique Id
		public ScenarioEntry CreateScenario(string name)
		{
			string new_id = System.Guid.NewGuid().ToString("N");

			Directory.CreateDirectory(Path.Combine(ScenariosFolder, new_id));

			PMCore.Model new_scene = new PMCore.Model();
			new_scene.SetDefaults();

			new_scene.Cursor = null;
			new_scene.Polygon = null;
			new_scene.Templates = null;

			new_scene.ProjectName = name;
			new_scene.ScenarioId = null;

			string scenario_file = Path.Combine(ScenariosFolder, new_id, "scenario.json");

			SerializeToJson(new_scene, scenario_file);

			// Create the new Entry

			var entry = new ScenarioEntry();
			entry.Load(scenario_file);

			Scenarios.Add(entry);

			return entry;
		}

		public void SaveScenariosIfNeeded()
		{
			foreach (var s in Scenarios)
			{
				if (s.IsDirty)
					s.Save();
			}
		}

		#endregion

		#region Packages

		public PackageEntry GetPackage(string id)
			=> (from p in Packages where p.Package.id.Equals(id, StringComparison.OrdinalIgnoreCase) select p).FirstOrDefault();


		#endregion

		#region Helpers

		public PackageModule GetUnityModule(ProcessInformation pinfo) 
		{
			return new PackageModule()
			{
				id = pinfo.module_name,
				name = pinfo.module_name
			};
		}

		public PackageModule GetModule(string module_name)
			=> (from m in Modules where m.id.Equals(module_name, StringComparison.OrdinalIgnoreCase) select m).FirstOrDefault();

		// Redundant but needed for cross-project
		public static void SerializeToJson(object obj, string filename)
		{
			string json = JsonConvert.SerializeObject(obj);

			json = SharedObjectMap.SharedObjectMap.CleanJsonFromGuids(json);

			File.WriteAllText(filename, json);
		}

		public static async Task<T> DownloadJson<T>(string url, bool no_cache = true)
		{
			using HttpClient httpClient = new HttpClient();

			if (no_cache)
			{
				httpClient.DefaultRequestHeaders.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
				{
					NoCache = true
				};

				url += $"?t={DateTimeOffset.Now.ToUnixTimeSeconds()}";
			}

			HttpResponseMessage response = await httpClient.GetAsync(url);

			// Ensure that the request was successful, otherwise throw an exception
			response.EnsureSuccessStatusCode();

			// Read the response content as a string
			string json = await response.Content.ReadAsStringAsync();

			return JsonConvert.DeserializeObject<T>(json);
		}

		public static async Task<string> DownloadText(string url, bool no_cache = true)
		{
			using HttpClient httpClient = new HttpClient();

			if (no_cache)
			{
				httpClient.DefaultRequestHeaders.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
				{
					NoCache = true
				};

				url += $"?t={DateTimeOffset.Now.ToUnixTimeSeconds()}";
			}


			HttpResponseMessage response = await httpClient.GetAsync(url);

			// Ensure that the request was successful, otherwise throw an exception
			response.EnsureSuccessStatusCode();

			// Read the response content as a string
			return await response.Content.ReadAsStringAsync();
		}

		public static async Task DownloadBinary(string url, string filename, bool no_cache = true)
		{
			using HttpClient httpClient = new HttpClient();

			if (no_cache)
			{
				httpClient.DefaultRequestHeaders.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
				{
					NoCache = true
				};

				url += $"?t={DateTimeOffset.Now.ToUnixTimeSeconds()}";
			}


			HttpResponseMessage response = await httpClient.GetAsync(url);

			// Ensure that the request was successful, otherwise throw an exception
			response.EnsureSuccessStatusCode();

			// Read the response content as a byte array
			byte[] imageData = await response.Content.ReadAsByteArrayAsync();

			// Save the byte array to a file
			await File.WriteAllBytesAsync(filename, imageData);
		}

		static async Task DownloadBinary(string url, string filePath, IProgress<double> progress, CancellationToken cancellationToken, bool no_cache = true)
		{
			using HttpClient httpClient = new HttpClient();

			if (no_cache)
			{
				httpClient.DefaultRequestHeaders.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue
				{
					NoCache = true
				};

				url += $"?t={DateTimeOffset.Now.ToUnixTimeSeconds()}";
			}


			// Send a GET request with the provided CancellationToken
			using HttpResponseMessage response = await httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

			// Ensure that the request was successful, otherwise throw an exception
			response.EnsureSuccessStatusCode();

			// Get the total content length
			long contentLength = response.Content.Headers.ContentLength.GetValueOrDefault();

			// Create a stream to read the response content
			using Stream contentStream = await response.Content.ReadAsStreamAsync();

			// Create a stream to write the content to a file
			using FileStream fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize: 8192, useAsync: true);

			// Buffer for reading the content
			byte[] buffer = new byte[8192];
			int bytesRead;
			long totalBytesRead = 0;

			// Read the content and write it to the file
			while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) != 0)
			{
				// Write the buffer to the file stream
				await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);

				// Update the total bytes read
				totalBytesRead += bytesRead;

				// Report the progress
				if (progress != null && contentLength > 0)
				{
					double percent = (double)totalBytesRead / contentLength * 100;
					progress.Report(percent);
				}
			}
		}

		#endregion
	}

	[AddINotifyPropertyChangedInterface]
	public class ScenarioEntry : BasePropertyChanged
	{ 
		public string Name
		{
			get => Model.ProjectName;
			set
			{
				Model.ProjectName = value;  // TODO: enforce name?
				IsDirty = true;
			}
		}

		public string Path { get; private set; }    // path to folder

		public string Filename => System.IO.Path.Combine(Path, "scenario.json");

		public string Id { get; private set; }  // it's a guid, name of the folder

		public string ThumbnailPath
			=> System.IO.Path.Combine(Path, "scenario.png");

		public string CalibrationImagePath
			=> System.IO.Path.Combine(Path, "calibration.jpg");

		public PMCore.Model Model { get; private set; } // this should contain only structural data

		public bool IsDirty { get; private set; } = false;

		public void Load(string filename)
		{
			Path = new FileInfo(filename).Directory.FullName;

			Model = JsonConvert.DeserializeObject<PMCore.Model>(File.ReadAllText(filename));
			Model.Normalize();
			Id = System.IO.Path.GetFileName(Path);

			// Create thumbnail only on client, if datetimes are different?
#if SARGAME_EDITOR
			if (!File.Exists(ThumbnailPath) ||
				new FileInfo(Filename).LastWriteTime != new FileInfo(ThumbnailPath).LastWriteTime)
			{
				CreateThumbnail();
			}

			new FileInfo(ThumbnailPath).LastWriteTime = new FileInfo(Filename).LastWriteTime;
#endif
		}

		public void Save()
		{
			var json = JsonConvert.SerializeObject(Model);

			File.WriteAllText(Filename, SharedObjectMap.SharedObjectMap.CleanJsonFromGuids(json));

			IsDirty = false;

			Load(Filename);
		}

		public void Delete()
		{			
			Directory.Delete(Path, true);

			SARGAME.Instance.Scenarios.Remove(this);
		}

		public void UpdateFromModel(PMCore.Model other_model)
		{
			Model.Screen = JsonConvert.DeserializeObject<PMCore.Screen>(JsonConvert.SerializeObject(other_model.Screen));
			Model.OutputSurfaces = JsonConvert.DeserializeObject<List<PMCore.OutputSurface>>(JsonConvert.SerializeObject(other_model.OutputSurfaces));
			Model.Structures = JsonConvert.DeserializeObject<List<PMCore.Structure>>(JsonConvert.SerializeObject(other_model.Structures));
		}

		public void ApplyToModel(PMCore.Model other_model)
		{
			other_model.Screen = JsonConvert.DeserializeObject<PMCore.Screen>(JsonConvert.SerializeObject(Model.Screen));
			other_model.OutputSurfaces = JsonConvert.DeserializeObject<List<PMCore.OutputSurface>>(JsonConvert.SerializeObject(Model.OutputSurfaces));
			other_model.Structures = JsonConvert.DeserializeObject<List<PMCore.Structure>>(JsonConvert.SerializeObject(Model.Structures));
		}

#if SARGAME_EDITOR

		#region Scenario thumbnail generation

		public void CreateThumbnail(int width=512, int height=288)
		{
			SKPoint _get_structure_vertex(Structure s, int index)
			{
				var v = new SKPoint(s.vertices[index].x, s.vertices[index].y);
				v.X += s.position.x;
				v.Y += s.position.y;

				return v;
			}

			SKPoint _get_structure_center(Structure s)
			{
				SKPoint center = new SKPoint(0, 0);
				for (int i = 0; i < s.vertices.Count; i++)
				{
					var p = _get_structure_vertex(s, i);

					center += p;
				}

				center.X /= (float)s.vertices.Count;
				center.Y /= (float)s.vertices.Count;

				return center;
			}

			void _draw_structure(SKCanvas c, Structure s, SKColor color)
			{
				var path = new SKPath();

				path.Reset();

				path.MoveTo(_get_structure_vertex(s, 0));

				for (int i = 1; i < s.vertices.Count; i++)
				{
					path.LineTo(_get_structure_vertex(s, i));
				}

				path.Close();

				c.DrawPath(path, new SKPaint()
				{
					// BlendMode = BlendMode,
					IsAntialias = true,

					Color = color
				});
			}

			var image = new SKBitmap(Model.Screen.size.width, Model.Screen.size.height, false);

			using (SKCanvas canvas = new SKCanvas(image))
			{
				canvas.Clear(SKColors.DarkGray);

				foreach (var s in Model.Structures)
				{
					_draw_structure(canvas, s, SKColors.GhostWhite);
				}
			}

			// resize
			var resized_bitmap = image.Resize(new SKSizeI() { Width = width, Height = height }, SKFilterQuality.High);

			// save
			File.Delete(ThumbnailPath);

			using (var data = resized_bitmap.Encode(SKEncodedImageFormat.Png, 100))
			using (var stream = File.OpenWrite(ThumbnailPath))
			{
				// save the data to a stream
				data.SaveTo(stream);
			}

			resized_bitmap.Dispose();
			image.Dispose();
		}

#endregion

#endif
	}

	[AddINotifyPropertyChangedInterface]
	public class PackageEntry : BasePropertyChanged
	{
		public string Path { get; private set; }

		public Package Package { get; private set; }

		public void Load(string filename)
		{
			Path = new FileInfo(filename).Directory.FullName;

			Package = JsonConvert.DeserializeObject<Package>(File.ReadAllText(filename));
			Package.Load(this);
		}
	}

	[AddINotifyPropertyChangedInterface]
	public class Package : BasePropertyChanged
	{
		// id name es: starterpack
		public string id { get; set; }

		// Verbose name
		public string name { get; set; }

		public int protocol { get;set; }	//this is the minimum protocol, so editor can give notice before starting

		// Current version (NOTE: deprecated is now in StorePackage)
		// public string version { get; set; }	

		// URL where to check for new versions
		public string appcast_url { get; set; }

		// the executable application
		public string application_exe { get; set; }

		public List<PackageModule> modules { get; set; }

		[JsonIgnore]
		public PackageEntry Entry { get; private set; }

		[JsonIgnore]
		public string ExecutableFolder => Path.Combine(Entry.Path, "app");

		[JsonIgnore]
		public string IconsFolder => Path.Combine(Entry.Path, "icons");

		[JsonIgnore]
		public string ExecutablePath => Path.Combine(Entry.Path, "app", application_exe);

		// NOTE: this is the projects folder of the installed package, copied once (or if there are file version upgrades)
		[JsonIgnore]
		public string TemplateProjectsFolder => Path.Combine(Entry.Path, "projects");

		[JsonIgnore]
		public string ProcessName => Path.GetFileNameWithoutExtension(application_exe);

		[JsonIgnore]
		public StorePackage StorePackage => (from p in SARGAME.Instance.CurrentStore.packages where p.id == id select p).FirstOrDefault();

		[JsonIgnore]
		public bool IsInstalled => File.Exists(ExecutablePath);	// MacOS: in json we walk in the .app folder until the real executable
		/*
		{
			get
			{
				if (OperatingSystem.IsWindows())
					return File.Exists(ExecutablePath);
				else if (OperatingSystem.IsMacOS())
					return Directory.Exists(ExecutablePath);
				else
					throw new Exception("Unknown platform");
			}
		}
		*/

		public void Load(PackageEntry entry)
		{
			Entry = entry;

			foreach (var m in modules)
				m.Load(this);
		}
	}

	[AddINotifyPropertyChangedInterface]
	public class PackageModule : BasePropertyChanged
	{
		public string id { get; set; }	// es: walleroids

		// Verbose UI name
		public string name { get; set; }

		// License feature to check (only in UI, game will check it really)
		public string license_feature { get; set; }

		// Scene to be called on package (this is the codified project name)
		public string scene { get; set; }

		// extra arguments
		public List<string> arguments { get; set; }

		// Every module has its own project file
		// The template is in the package folder, copied in the UserProjectsFolder once

		[JsonIgnore]
		public string TemplateProjectFile
			=> Path.Combine(Package.TemplateProjectsFolder, $"{scene}.json");

		[JsonIgnore]
		public string UserProjectFile
			=> Path.Combine(SARGAME.Instance.UserProjectsFolder, $"{scene}.json");

		[JsonIgnore]
		public string ThumbnailPath
		{
			get
			{
				// give precedence to installed packages
				string path = "";
				if (IsInstalled)
				{
					path = Path.Combine(Package.Entry.Path, "media", $"{id.ToLower()}.png");
					if (File.Exists(path))
						return path;
				}

				// Not installed or not found, precedence to store
				path = Path.Combine(SARGAME.Instance.StoreMediaModulesCacheFolder, $"{id.ToLower()}.png");
				if (File.Exists(path))
					return path;

				path = Path.Combine(SARGAME.Instance.EditorShippedModulesCacheFolder, $"{id.ToLower()}.png");
				if (File.Exists(path))
					return path;

				//=> Package!=null ? File.Exists(Path.Combine(Package.Entry.Path, "media", $"{id.ToLower()}.png")) ? Path.Combine(Package.Entry.Path, "media", $"{id.ToLower()}.png") : null : null;
				return null;
			}
		}	

		[JsonIgnore]
		public bool HasGuide => IsInstalled && File.Exists(GuideHtmlPath);

		[JsonIgnore]
		public string GuideFolderPath => Package!=null ? System.IO.Path.Combine(Package.Entry.Path, "guides", id) : null;

		[JsonIgnore]
		public string GuideHtmlPath => System.IO.Path.Combine(GuideFolderPath, $"{id}.html");

		[JsonIgnore]
		public string package_id { get; set; }	// necessary for uninstalled packages

		[JsonIgnore]
		public Package Package { get; private set; }

		// Scenario selected for starting this module
		[JsonIgnore]
		public ScenarioEntry SelectedScenario { get; set; }

		[JsonIgnore]
		public bool IsInstalled => Package != null ? Package.IsInstalled : false;

		[JsonIgnore]
		public bool IsLicensed => string.IsNullOrEmpty(license_feature) || LocalAppConfig.Instance.CheckLicenseFeature(license_feature, wildcard: true);

		[JsonIgnore]
		public bool IsNew => false; // TODO

		[JsonIgnore]
		public int Order { get; set; } = 0;

		// TODO: IsLicensed

		//[JsonIgnore]
		//public string ProcessInfoPath => Path.Combine(Package.ExecutableFolder, "process.json");

		public void Load(Package package)
		{
			Package = package;
		}

		// changes the screen size of the selected scenario and/or project
		// TODO: better handle pure json
		public void UpdateScreenSize(int width, int height, ScenarioEntry scenario)
		{
			void _change_screen_size(string filename)
			{
				var model = JsonConvert.DeserializeObject<PMCore.Model>(File.ReadAllText(filename));

				model.Screen.size.width = width;
				model.Screen.size.height = height;

				var json = JsonConvert.SerializeObject(model);

				File.WriteAllText(filename, SharedObjectMap.SharedObjectMap.CleanJsonFromGuids(json));
			}

			// change in project file, should not be needed as we are using scenario
			try
			{
				_change_screen_size(UserProjectFile);

				if (scenario != null)
					_change_screen_size(scenario.Filename);
			}
			catch(Exception ex)
			{
				SARGAME.App.logException("UpdateScreenSize()", ex);
			}
		}

		// Checks if project must be copied (not exiting or higher template version)
		public bool CopyOrUpgradeTemplateProjectToUserProjectsFolder()
		{
			Model model_template=null, model_project=null;

			bool copy_template = false;

			if (File.Exists(TemplateProjectFile))
				try
				{
					model_template = JsonConvert.DeserializeObject<PMCore.Model>(File.ReadAllText(TemplateProjectFile));
				}
				catch(Exception ex)
				{
					// This should not happen, we must always be able to read the current official template files
					SARGAME.App.logException("CopyOrUpgradeTemplateProjectToUserProjectsFolder (1)", ex);
					return false;
				}

			if (File.Exists(UserProjectFile))
				try
				{
					model_project = JsonConvert.DeserializeObject<PMCore.Model>(File.ReadAllText(UserProjectFile));
				}
				catch 
				{
					// Exception reading current project file could happen if the model has changed.
					// (or user edited it with errors..)
					model_project = null;
				}

			if (model_template == null)
			{
				// error
				SARGAME.App.logError($"Error in package module ({scene}) Start, missing template project file: {TemplateProjectFile}");
				SARGAME.onPackageModuleExited(this, SARGAME.ERROR_NoProjectTemplateFile);
				return false;
			}

			if (model_project == null)
				copy_template = true;
			else
			{
				// check if template is higher
				if (model_template.TemplateVersion > model_project.TemplateVersion)
				{
					copy_template = true;
					SARGAME.App.log($"Upgrading project template for package module ({scene})");
				}
			}

			if (copy_template)
			{
				try
				{
					File.Copy(TemplateProjectFile, UserProjectFile, true);
				}
				catch(Exception ex)
				{
					SARGAME.App.logException($"Error copying template project file: {TemplateProjectFile}", ex);
					SARGAME.onPackageModuleExited(this, SARGAME.ERROR_ExceptionCopyingProjectTemplateFile);

					return false;
				}
			}

			return true;
		}

#if SARGAME_EDITOR

		// Gets the first running process
		public Process GetProcess()
		=> (from p in Process.GetProcesses() where p.ProcessName.Equals(Package.ProcessName, StringComparison.OrdinalIgnoreCase) select p).FirstOrDefault();



		// status reported in onStartStatus
		//	-10: error in copying template project
		//	-11: missing project file
		//  -20: general exception
		public async Task Start(
			bool windowed=false, 
			Display monitor=null, 
			bool popupwindow=false,	// for custom resolutions
			bool spout=false, 
			Action<int> onStartStatus=null)
		{
			try
			{
				SARGAME.Instance.IsLicenseError = false;

				// Check if project file must be copied from template folder (higher TemplateVersion)
				if (!CopyOrUpgradeTemplateProjectToUserProjectsFolder())
				{
					// error is reported inside the function
					onStartStatus?.Invoke(-10);
					return;
				}

				// last sanity check
				if (!File.Exists(UserProjectFile))
				{
					SARGAME.App.logError($"Error in package module ({scene}) Start, missing user project file: {UserProjectFile}");

					// re-use standard event
					SARGAME.onPackageModuleExited(this, SARGAME.ERROR_NoProjectFile);

					onStartStatus?.Invoke(-11);
					return;
				}

				var args = new List<string>()
				{
					"-scene", scene,
					"-project", "\"" + UserProjectFile + "\""
				};

				try
				{
					if (monitor != null)
					{
						args.Add("-monitor");
						args.Add(monitor.MonitorIndex.ToString());
					}


					if (SelectedScenario != null)
					{
						args.Add("-scenario");
						args.Add("\"" + SelectedScenario.Id + "\"");
					}

					if (windowed)
					{
						args.Add("-screen-fullscreen"); args.Add("0");
						args.Add("-screen-width"); args.Add("1024");
						args.Add("-screen-height"); args.Add("576");

						// and rewrite project/scenario screen size
						UpdateScreenSize(1024, 576, SelectedScenario);
					}
					else if (spout)
					{
						args.Add("-screen-fullscreen"); args.Add("0");
						// screen is now just for the window, the smaller the better
						args.Add("-screen-width"); args.Add("100");
						args.Add("-screen-height"); args.Add("100");
						// spout is taken from the custom resolution
						args.Add("-spout-width"); args.Add(monitor.system_width.ToString());
						args.Add("-spout-height"); args.Add(monitor.system_height.ToString());

						// and rewrite project/scenario screen size
						UpdateScreenSize(monitor.system_width, monitor.system_height, SelectedScenario);
					}
					else if (monitor != null)
					{
						if (popupwindow)
						{
							args.Add("-screen-fullscreen"); args.Add("0");
							args.Add("-popupwindow");
						}
						else
						{
							args.Add("-screen-fullscreen"); args.Add("1");
						}

						// get resolution from monitor
						args.Add("-screen-width"); args.Add(monitor.system_width.ToString());
						args.Add("-screen-height"); args.Add(monitor.system_height.ToString());

						// and rewrite project/scenario screen size
						UpdateScreenSize(monitor.system_width, monitor.system_height, SelectedScenario);
					}
				}
				catch (Exception ex)
				{
					// Some errors! but go on anyway
					SARGAME.App.logException("Setting monitor size in project/scenario", ex);
				}

				// extra args
				if (arguments != null)
					foreach (var a in arguments)
						args.Add(a);

				SARGAME.App.log($"START: {Package.ExecutablePath} {(string.Join(" ", args))}");

				ProcessStartInfo startInfo = new ProcessStartInfo
				{
					FileName = Package.ExecutablePath,
					WorkingDirectory = Package.ExecutableFolder,
					Arguments = string.Join(" ", args),
					CreateNoWindow = true,
					UseShellExecute = false
					// Optional: Set this to 'true' if you want to hide the console window
					//CreateNoWindow = true,
					// Optional: Set this to 'true' if you don't want to use the shell to execute the process
					//UseShellExecute = false
				};

				var process = new Process { StartInfo = startInfo };

				process.Start();

				onStartStatus?.Invoke(1);

				SARGAME.Instance.CurrentModuleProcess = process;

				while (true)
				{
					if (process.HasExited)
					{
						if (process.ExitCode < 0)
							SARGAME.App.logError($"PackageModule.Start() return={process.ExitCode}");
						else
							SARGAME.App.log($"PackageModule.Start() return={process.ExitCode}");

						SARGAME.onPackageModuleExited?.Invoke(this, process.ExitCode);

						SARGAME.Instance.CurrentModuleProcess = null;

						return;
					}
					else
						await Task.Delay(100);
				}
			}
			catch(Exception ex)
			{
				// Some errors! but go on anyway
				SARGAME.App.logException("PackageModule.Start(), general exception", ex);
				onStartStatus?.Invoke(-20);
			}
		}
#endif

		public async Task<ProcessInformation> WaitProcessInfo(DateTime date_min, int timeout)
		{
			return await Task.Run(async () =>
			{
				var sw = new Stopwatch();
				sw.Start();

				while (true)
				{
					if (sw.ElapsedMilliseconds > timeout)
					{
						return null;
					}

					var p = SARGAME.Instance.ReadProcessInfo();

					if (p.processinfo != null)
					{
						// only new dates are valid
						if (p.date > date_min)
						{
							// OK but the process name must be the same as the module
							if (Package.ProcessName.Equals(p.processinfo.process_name, StringComparison.OrdinalIgnoreCase))
							{
								return p.processinfo;
							}							
							
						}
					}

					await Task.Delay(500);
				}
			});
		}
	
	
		public static void OnPackageModuleExited(PackageModule module, int exit_code)
		{
			// cannot start dialogs because messages can be opened

			// -1: module not found/installed
			// -666: license not found
			// -667: license not correct
			// -668: license feature not correct
			// -669: license check exception
			if (exit_code < 0)
			{
				if (exit_code <= -666)
				{
					// License problem
					SARGAME.Instance.IsLicenseError = true;					
				}
			}
			else
			{
				SARGAME.Instance.IsLicenseError = false;
			}
		}
	}


	[AddINotifyPropertyChangedInterface]
	public class ProcessInformation : BasePropertyChanged
	{
		public int		process_id;
		public string	process_name;
		public string	package_name;		// optional
		public string	module_name;        // optional
		public string	network_address;	// future use
		public int		network_port;		// future use

		public bool	IsProcessAlive()
		{
			var p = Process.GetProcessById(process_id);
			if (p != null && !p.HasExited)
			{
				return true;
			}
			else
				return false;
		}

		public Process GetProcess()
		{
			try
			{
				var proc = Process.GetProcessById(process_id);

				// ok the process id exists, but is the name the same?
				if (proc.ProcessName != process_name)
					throw new Exception();

				return proc;
			}
			catch
			{
				return null;
			}
		}
	}

	#region Store

	[AddINotifyPropertyChangedInterface]
	public class StorePackage : BasePropertyChanged
	{
		public string name { get; set; }
		public string id { get; set; }
		public string description { get; set; }
		public string license_feature { get; set; }
		public string media_url { get; set; }
		public string appcast_url { get; set; }

		public List<PackageModule> modules { get; set; }	// addition

		[JsonIgnore]
		public string NameAndVersion => $"{name} ({Version})";

		[JsonIgnore]
		public Version Version
		{
			get
			{
				try
				{
					if (Version.TryParse(File.ReadAllText(Path.Combine(InstalledPackage.Package.ExecutableFolder, "version.txt")), out Version _version))
					{
						return _version;
					}
					else
						return default;
				}
				catch
				{
					return default;
				}
			}
		}
		

		[JsonIgnore]
		public string MediaPath => Path.Combine(SARGAME.Instance.StoreFolder, $"{id}.png");

		[JsonIgnore]
		public bool IsInstalled => InstalledPackage != null && InstalledPackage.Package.IsInstalled;

		[JsonIgnore]
		public bool IsUpdateAvailable { get; set; } = false;

		[JsonIgnore]
		public PackageEntry InstalledPackage => SARGAME.Instance.GetPackage(id);


#if SARGAME_EDITOR

		public async Task<AppCastItem> CheckForStoreUpdatesUsingSparkle()
		{
			SparkleUpdater sparkle = new SparkleUpdater(appcast_url, new Ed25519Checker(SecurityMode.Unsafe))
			{
				UIFactory = null,
				IgnoreCurrentAssemblyConfiguration = true
			};

			sparkle.UpdateCheckStarted += (s) => SARGAME.App?.log("store: UpdateCheckStarted");
			sparkle.UpdateDetected += (s, a) => SARGAME.App?.log($"store: UpdateDetected, latest_version={a.LatestVersion.Version}");
			sparkle.UpdateCheckFinished += (s, status) => SARGAME.App?.log($"store: UpdateCheckFinished, status={status}");

			sparkle.DownloadStarted += (item, path) => SARGAME.App?.log($"store: DownloadStarted, item={item.Version} path={path}");
			sparkle.DownloadHadError += (item, path, ex) => SARGAME.App?.log($"store: DownloadHadError, item={item.Version} path={path} ex={ex}");
			sparkle.DownloadedFileIsCorrupt += (item, path) => SARGAME.App?.log($"store: DownloadedFileIsCorrupt, item={item.Version} path={path}");
			sparkle.DownloadFinished += (item, path) => SARGAME.App?.log($"store: DownloadFinished, item={item.Version} path={path}");
			sparkle.DownloadCanceled += (item, path) => SARGAME.App?.log($"store: DownloadCanceled, item={item.Version} path={path}");

			try
			{
				SARGAME.App.log($"CheckForStoreUpdatesUsingSparkle(): appcast_url={appcast_url}");

				UpdateInfo update = await sparkle.CheckForUpdatesQuietly(true);

				if (update == null || (update!=null && update.Updates.Count == 0))
				{
					SARGAME.App.log($"CheckForStoreUpdatesUsingSparkle(): updates null or zero");

					Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
					return null;
				}

				// last reported version
				var last_version = update.Updates.First();

				SARGAME.App.log($"CheckForStoreUpdatesUsingSparkle(): current_version={(Version!=null ? Version.ToString() : "none")} last_version={last_version.Version}");

				// check our version (if existing)
				if (Version != null)
				{
					if (Version >= System.Version.Parse(last_version.Version))
					{
						Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
						return null;
					}
				}
				
				Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = true);

				return last_version;
			}
			catch(Exception ex)
			{
				Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
				SARGAME.App.logException("StorePackage.CheckForStoreUpdatesUsingSparkle()", ex);
				return null;
			}
		}

		// this version will download sparkle XML and parse direcly comparing with local application version
		// returns directly the download url
		public async Task<(string url, long size)> CheckForStoreUpdatesUsingXML(Version editor_version, bool notify_update = false)
		{
			try
			{
				SARGAME.App.log($"CheckForStoreUpdatesUsingXML(): appcast_url={appcast_url}");

				var sparke_xml = await DownloadText(appcast_url);

				// last compatible with the current editor version
				var (last_version, last_version_url, last_version_size) = VersionChecker.GetLastCompatibleVersion(sparke_xml, editor_version);

				if (last_version == default)
				{
					SARGAME.App.log($"CheckForStoreUpdatesUsingXML(): no compatible versions found (Editor too new?)");
					if (notify_update)
						Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
					return default;
				}

				SARGAME.App.log($"CheckForStoreUpdatesUsingXML(): package={this.id} current_version={(Version != null ? Version.ToString() : "none")} last_version={last_version.ToString()}");

				// check if it is an update
				if (last_version > Version)
				{
					// there is an updated version
					if (notify_update)
						Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = true);
					return (last_version_url, last_version_size);
				}
				else
				{
					// nothing to do
					if (notify_update)
						Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
					return default;
				}
			}
			catch (Exception ex)
			{
				if (notify_update)
					Dispatcher.UIThread.Invoke(() => IsUpdateAvailable = false);
				SARGAME.App.logException("StorePackage.CheckForStoreUpdatesUsingXML()", ex);
				return default;
			}
		}

		public async Task<int> Uninstall()
		{
			if (InstalledPackage == null)
				return -100;

			// Start the uninstaller in the folder
			var uninstaller = Path.Combine(InstalledPackage.Path, "unins000.exe");
			if (!File.Exists(uninstaller))
				return -200;

			var result = await Cli.Wrap(uninstaller)
					.WithArguments(new string[] { "/verysilent" })
					.ExecuteAsync();

			//Directory.Delete(InstalledPackage.Path, true)

			SARGAME.Instance.LoadInstalledPackagesAndModules();

			return result.ExitCode;
		}
#endif
	}

	[AddINotifyPropertyChangedInterface]	
	public class Store : BasePropertyChanged
	{
		public ObservableCollection<StorePackage> packages { get; set; }

		[JsonIgnore]
		public ObservableCollection<StorePackage> LicensedPackages
		{
			get
			{
				var o = new ObservableCollection<StorePackage>();

				foreach (var p in packages)
				{
					if (string.IsNullOrEmpty(p.license_feature))
						continue;

					// Check features with partial matching, so that anything that begins with "pack" or "pack/" considers the package
					if (SARGAME.App.CheckLicenseFeature(p.license_feature, partial_match: true))
						o.Add(p);
				}

				return o;
			}
		}
	}

	#endregion

	#region Tutorials

	public class TutorialEntry : BasePropertyChanged
	{		
		public string id { get; set; }				// tutorial slug, media thumbnail is the same
		public int	  order { get; set; }              // order in the list
		public string title { get; set; }			
		public string description { get; set; }
		public string categories { get; set; }		// comma separated categories url
		public string tutorial_url { get; set; }	// open in a webpage
		
		[JsonIgnore]
		public string ThumbnailPath => Path.Combine(SARGAME.Instance.TutorialsMediaCacheFolder, $"{id.ToLower()}.jpg");

#if SARGAME_EDITOR

		private static bool _get_tutorials_in_progress = false;

		private static string _local_tutorials_list => Path.Combine(SARGAME.Instance.TutorialsMediaCacheFolder, "tutorials.json");

		public static async Task<List<TutorialEntry>> DownloadTutorials()
		{
			if (_get_tutorials_in_progress)
			{
				if (File.Exists(_local_tutorials_list))
				{
					SARGAME.Instance.Tutorials = JsonConvert.DeserializeObject<List<TutorialEntry>>(File.ReadAllText(_local_tutorials_list));
					return SARGAME.Instance.Tutorials;
				}
				else
					return null;
			}

			_get_tutorials_in_progress = true;

			SARGAME.App.log($"DownloadTutorials(): url={SARGAME.TutorialsListUrl}");

			try
			{
				var tutorials = await DownloadJson<List<TutorialEntry>>(SARGAME.TutorialsListUrl);

				File.WriteAllText(_local_tutorials_list, JsonConvert.SerializeObject(tutorials));

				if (tutorials == null)
				{
					App.logError($"DownloadTutorials(): returned null");
					_get_tutorials_in_progress = false;
					return null;
				}

				App.log($"DownloadTutorials(): Downloaded {tutorials.Count} tutorials entries");

				// Downloading media for tutorials
				foreach (var t in tutorials)
				{
					if (File.Exists(t.ThumbnailPath))
						continue;

					App.log($"DownloadTutorials(): Downloading media for tutorial {t.id}: {t.ThumbnailPath}");
					try
					{
						// This was for the server-provided media
						/*
						string media_url = SARGAME.TutorialsBaseUrl + $"/{t.id.ToLower()}.jpg";
						await DownloadBinary(media_url, t.ThumbnailPath);
						*/

						// Directly download youtube thumb
						string media_url = t.GetYoutubeThumbnailUrl();
						await DownloadBinary(media_url, t.ThumbnailPath);

						t.RaisePropertyChanged(nameof(t.ThumbnailPath));

						App.log($"DownloadTutorials(): downloaded {media_url} to {t.ThumbnailPath}");
					}
					catch (Exception ex)
					{
						App.logException("DownloadTutorials(): exception downloading media", ex);
					}

				}

				_get_tutorials_in_progress = false;

				// Refresh all
				SARGAME.Instance.Tutorials = tutorials;			

				return tutorials;
			}
			catch (Exception ex)
			{
				App.logException("DownloadTutorials()", ex);
				_get_tutorials_in_progress = false;
				return null;
			}
			finally
			{
				_get_tutorials_in_progress = false;
				if (File.Exists(_local_tutorials_list))
					SARGAME.Instance.Tutorials = JsonConvert.DeserializeObject<List<TutorialEntry>>(File.ReadAllText(_local_tutorials_list));				
			}

			return SARGAME.Instance.Tutorials;
		}

		public string GetYoutubeThumbnailUrl()
		{
			// Regular expression to extract the video ID from the URL
			string pattern = @"(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})";
			Regex regex = new Regex(pattern, RegexOptions.IgnoreCase);
			Match match = regex.Match(tutorial_url);

			if (match.Success)
			{
				// Extract the video ID
				string videoId = match.Groups[1].Value;

				// Construct the URL for the mqdefault.jpg thumbnail
				string thumbnailUrl = $"https://img.youtube.com/vi/{videoId}/mqdefault.jpg";
				return thumbnailUrl;
			}
			else
			{
				return null;
			}
		}

#endif
	}

	#endregion
}


// inherited from PolygonUtils.cs
namespace Tabula.PWG.SARGAME.Utils
{
	public class Polygon
	{
		public Polygon()
		{ }

		public Polygon(Vector2f[] points)
		{
			Points = points;
		}

		// static
		public static bool isPolygonConvex(Vector2f[] points)
		{			
			Polygon p = new Polygon(points);
			return p.PolygonIsConvex();
		}

		public Vector2f[] Points;

		// Find the polygon's centroid.
		public Vector2f FindCentroid()
		{
			// Add the first point at the end of the array.
			int nuPoints = Points.Length;
			Vector2f[] pts = new Vector2f[nuPoints + 1];
			Points.CopyTo(pts, 0);
			pts[nuPoints] = Points[0];

			// Find the centroid.
			float X = 0;
			float Y = 0;
			float second_factor;
			for (int i = 0; i < nuPoints; i++)
			{
				second_factor =
					pts[i].x * pts[i + 1].y -
					pts[i + 1].x * pts[i].y;
				X += (pts[i].x + pts[i + 1].x) * second_factor;
				Y += (pts[i].y + pts[i + 1].y) * second_factor;
			}

			// Divide by 6 times the polygon's area.
			float polygon_area = PolygonArea();
			X /= (6 * polygon_area);
			Y /= (6 * polygon_area);

			// If the values are negative, the polygon is
			// oriented counterclockwise so reverse the signs.
			if (X < 0)
			{
				X = -X;
				Y = -Y;
			}

			return new Vector2f(X, Y);
		}

		// Return True if the point is in the polygon.
		public bool PointInPolygon(float X, float Y)
		{
			// Get the angle between the point and the
			// first and last vertices.
			int max_point = Points.Length - 1;
			float total_angle = GetAngle(
				Points[max_point].x, Points[max_point].y,
				X, Y,
				Points[0].x, Points[0].y);

			// Add the angles from the point
			// to each other pair of vertices.
			for (int i = 0; i < max_point; i++)
			{
				total_angle += GetAngle(
					Points[i].x, Points[i].y,
					X, Y,
					Points[i + 1].x, Points[i + 1].y);
			}

			// The total angle should be 2 * PI or -2 * PI if
			// the point is in the polygon and close to zero
			// if the point is outside the polygon.
			return (Math.Abs(total_angle) > 0.000001);
		}

		#region "Orientation Routines"
		// Return True if the polygon is oriented clockwise.
		public bool PolygonIsOrientedClockwise()
		{
			return (SignedPolygonArea() < 0);
		}

		// If the polygon is oriented counterclockwise,
		// reverse the order of its points.
		private void OrientPolygonClockwise()
		{
			if (!PolygonIsOrientedClockwise())
				Array.Reverse(Points);
		}
		#endregion // Orientation Routines

		#region "Area Routines"
		// Return the polygon's area in "square units."
		// Add the areas of the trapezoids defined by the
		// polygon's edges dropped to the X-axis. When the
		// program considers a bottom edge of a polygon, the
		// calculation gives a negative area so the space
		// between the polygon and the axis is subtracted,
		// leaving the polygon's area. This method gives odd
		// results for non-simple polygons.
		public float PolygonArea()
		{
			// Return the absolute value of the signed area.
			// The signed area is negative if the polyogn is
			// oriented clockwise.
			return Math.Abs(SignedPolygonArea());
		}

		// Return the polygon's area in "square units."
		// Add the areas of the trapezoids defined by the
		// polygon's edges dropped to the X-axis. When the
		// program considers a bottom edge of a polygon, the
		// calculation gives a negative area so the space
		// between the polygon and the axis is subtracted,
		// leaving the polygon's area. This method gives odd
		// results for non-simple polygons.
		//
		// The value will be negative if the polyogn is
		// oriented clockwise.
		private float SignedPolygonArea()
		{
			// Add the first point to the end.
			int nuPoints = Points.Length;
			Vector2f[] pts = new Vector2f[nuPoints + 1];
			Points.CopyTo(pts, 0);
			pts[nuPoints] = Points[0];

			// Get the areas.
			float area = 0;
			for (int i = 0; i < nuPoints; i++)
			{
				area +=
					(pts[i + 1].x - pts[i].x) *
					(pts[i + 1].y + pts[i].y) / 2;
			}

			// Return the result.
			return area;
		}
		#endregion // Area Routines

		// Return True if the polygon is convex.
		public bool PolygonIsConvex()
		{
			// For each set of three adjacent points A, B, C,
			// find the dot product AB · BC. If the sign of
			// all the dot products is the same, the angles
			// are all positive or negative (depending on the
			// order in which we visit them) so the polygon
			// is convex.
			bool got_negative = false;
			bool got_positive = false;
			int nuPoints = Points.Length;
			int B, C;
			for (int A = 0; A < nuPoints; A++)
			{
				B = (A + 1) % nuPoints;
				C = (B + 1) % nuPoints;

				float cross_product =
					CrossProductLength(
						Points[A].x, Points[A].y,
						Points[B].x, Points[B].y,
						Points[C].x, Points[C].y);
				if (cross_product < 0)
				{
					got_negative = true;
				}
				else if (cross_product > 0)
				{
					got_positive = true;
				}
				if (got_negative && got_positive) return false;
			}

			// If we got this far, the polygon is convex.
			return true;
		}

		#region "Cross and Dot Products"
		// Return the cross product AB x BC.
		// The cross product is a vector perpendicular to AB
		// and BC having length |AB| * |BC| * Sin(theta) and
		// with direction given by the right-hand rule.
		// For two vectors in the X-Y plane, the result is a
		// vector with X and Y components 0 so the Z component
		// gives the vector's length and direction.
		public static float CrossProductLength(float Ax, float Ay,
			float Bx, float By, float Cx, float Cy)
		{
			// Get the vectors' coordinates.
			float BAx = Ax - Bx;
			float BAy = Ay - By;
			float BCx = Cx - Bx;
			float BCy = Cy - By;

			// Calculate the Z coordinate of the cross product.
			return (BAx * BCy - BAy * BCx);
		}

		// Return the dot product AB · BC.
		// Note that AB · BC = |AB| * |BC| * Cos(theta).
		private static float DotProduct(float Ax, float Ay,
			float Bx, float By, float Cx, float Cy)
		{
			// Get the vectors' coordinates.
			float BAx = Ax - Bx;
			float BAy = Ay - By;
			float BCx = Cx - Bx;
			float BCy = Cy - By;

			// Calculate the dot product.
			return (BAx * BCx + BAy * BCy);
		}
		#endregion // Cross and Dot Products

		// Return the angle ABC.
		// Return a value between PI and -PI.
		// Note that the value is the opposite of what you might
		// expect because Y coordinates increase downward.
		public static float GetAngle(float Ax, float Ay, float Bx, float By, float Cx, float Cy)
		{
			// Get the dot product.
			float dot_product = DotProduct(Ax, Ay, Bx, By, Cx, Cy);

			// Get the cross product.
			float cross_product = CrossProductLength(Ax, Ay, Bx, By, Cx, Cy);

			// Calculate the angle.
			return (float)Math.Atan2(cross_product, dot_product);
		}

		#region "Triangulation"
		// Find the indexes of three points that form an "ear."
		private void FindEar(ref int A, ref int B, ref int C)
		{
			int nuPoints = Points.Length;

			for (A = 0; A < nuPoints; A++)
			{
				B = (A + 1) % nuPoints;
				C = (B + 1) % nuPoints;

				if (FormsEar(Points, A, B, C)) return;
			}

			// We should never get here because there should
			// always be at least two ears.
			//Debug.Assert(false);
		}

		// Return True if the three points form an ear.
		private static bool FormsEar(Vector2f[] points, int A, int B, int C)
		{
			// See if the angle ABC is concave.
			if (GetAngle(
				points[A].x, points[A].y,
				points[B].x, points[B].y,
				points[C].x, points[C].y) > 0)
			{
				// This is a concave corner so the triangle
				// cannot be an ear.
				return false;
			}

			// Make the triangle A, B, C.
			Triangle triangle = new Triangle(
				points[A], points[B], points[C]);

			// Check the other points to see 
			// if they lie in triangle A, B, C.
			for (int i = 0; i < points.Length; i++)
			{
				if ((i != A) && (i != B) && (i != C))
				{
					if (triangle.PointInPolygon(points[i].x, points[i].y))
					{
						// This point is in the triangle 
						// do this is not an ear.
						return false;
					}
				}
			}

			// This is an ear.
			return true;
		}

		// Remove an ear from the polygon and
		// add it to the triangles array.
		private void RemoveEar(List<Triangle> triangles)
		{
			// Find an ear.
			int A = 0, B = 0, C = 0;
			FindEar(ref A, ref B, ref C);

			// Create a new triangle for the ear.
			triangles.Add(new Triangle(Points[A], Points[B], Points[C]));

			// Remove the ear from the polygon.
			RemoveVector2fromArray(B);
		}

		// Remove point target from the array.
		private void RemoveVector2fromArray(int target)
		{
			Vector2f[] pts = new Vector2f[Points.Length - 1];
			Array.Copy(Points, 0, pts, 0, target);
			Array.Copy(Points, target + 1, pts, target, Points.Length - target - 1);
			Points = pts;
		}

		// Triangulate the polygon.
		//
		// For a nice, detailed explanation of this method,
		// see Ian Garton's Web page:
		// http://www-cgrl.cs.mcgill.ca/~godfried/teaching/cg-projects/97/Ian/cutting_ears.html
		public List<Triangle> Triangulate()
		{
			// Copy the points into a scratch array.
			Vector2f[] pts = new Vector2f[Points.Length];
			Array.Copy(Points, pts, Points.Length);

			// Make a scratch polygon.
			Polygon pgon = new Polygon(pts);

			// Orient the polygon clockwise.
			pgon.OrientPolygonClockwise();

			// Make room for the triangles.
			List<Triangle> triangles = new List<Triangle>();

			// While the copy of the polygon has more than
			// three points, remove an ear.
			while (pgon.Points.Length > 3)
			{
				// Remove an ear from the polygon.
				pgon.RemoveEar(triangles);
			}

			// Copy the last three points into their own triangle.
			triangles.Add(new Triangle(pgon.Points[0], pgon.Points[1], pgon.Points[2]));

			return triangles;
		}
		#endregion // Triangulation

		#region "Bounding Rectangle"
		private int m_NumPoints = 0;

		// The points that have been used in test edges.
		private bool[] m_EdgeChecked;

		// The four caliper control points. They start:
		//       m_ControlPoints(0)      Left edge       xmin
		//       m_ControlPoints(1)      Bottom edge     ymax
		//       m_ControlPoints(2)      Right edge      xmax
		//       m_ControlPoints(3)      Top edge        ymin
		private int[] m_ControlPoints = new int[4];

		// The line from this point to the next one forms
		// one side of the next bounding rectangle.
		private int m_CurrentControlPoint = -1;

		// The area of the current and best bounding rectangles.
		private float m_CurrentArea = float.MaxValue;
		private Vector2f[] m_CurrentRectangle = null;
		private float m_BestArea = float.MaxValue;
		private Vector2f[] m_BestRectangle = null;

		// Get ready to start.
		private void ResetBoundingRect()
		{
			m_NumPoints = Points.Length;

			// Find the initial control points.
			FindInitialControlPoints();

			// So far we have not checked any edges.
			m_EdgeChecked = new bool[m_NumPoints];

			// Start with this bounding rectangle.
			m_CurrentControlPoint = 1;
			m_BestArea = float.MaxValue;

			// Find the initial bounding rectangle.
			FindBoundingRectangle();

			// Remember that we have checked this edge.
			m_EdgeChecked[m_ControlPoints[m_CurrentControlPoint]] = true;
		}

		// Find the initial control points.
		private void FindInitialControlPoints()
		{
			for (int i = 0; i < m_NumPoints; i++)
			{
				if (CheckInitialControlPoints(i)) return;
			}
			//Debug.Assert(false, "Could not find initial control points.");
		}

		// See if we can use segment i --> i + 1 as the base for the initial control points.
		private bool CheckInitialControlPoints(int i)
		{
			// Get the i -> i + 1 unit vector.
			int i1 = (i + 1) % m_NumPoints;
			float vix = Points[i1].x - Points[i].x;
			float viy = Points[i1].y - Points[i].y;

			// The candidate control point indexes.
			for (int num = 0; num < 4; num++)
			{
				m_ControlPoints[num] = i;
			}

			// Check backward from i until we find a vector
			// j -> j+1 that points opposite to i -> i+1.
			for (int num = 1; num < m_NumPoints; num++)
			{
				// Get the new edge vector.
				int j = (i - num + m_NumPoints) % m_NumPoints;
				int j1 = (j + 1) % m_NumPoints;
				float vjx = Points[j1].x - Points[j].x;
				float vjy = Points[j1].y - Points[j].y;

				// Project vj along vi. The length is vj dot vi.
				float dot_product = vix * vjx + viy * vjy;

				// If the dot product < 0, then j1 is
				// the index of the candidate control point.
				if (dot_product < 0)
				{
					m_ControlPoints[0] = j1;
					break;
				}
			}

			// If j == i, then i is not a suitable control point.
			if (m_ControlPoints[0] == i) return false;

			// Check forward from i until we find a vector
			// j -> j+1 that points opposite to i -> i+1.
			for (int num = 1; num < m_NumPoints; num++)
			{
				// Get the new edge vector.
				int j = (i + num) % m_NumPoints;
				int j1 = (j + 1) % m_NumPoints;
				float vjx = Points[j1].x - Points[j].x;
				float vjy = Points[j1].y - Points[j].y;

				// Project vj along vi. The length is vj dot vi.
				float dot_product = vix * vjx + viy * vjy;

				// If the dot product <= 0, then j is
				// the index of the candidate control point.
				if (dot_product <= 0)
				{
					m_ControlPoints[2] = j;
					break;
				}
			}

			// If j == i, then i is not a suitable control point.
			if (m_ControlPoints[2] == i) return false;

			// Check forward from m_ControlPoints[2] until
			// we find a vector j -> j+1 that points opposite to
			// m_ControlPoints[2] -> m_ControlPoints[2]+1.

			i = m_ControlPoints[2] - 1;//@
			float temp = vix;
			vix = viy;
			viy = -temp;

			for (int num = 1; num < m_NumPoints; num++)
			{
				// Get the new edge vector.
				int j = (i + num) % m_NumPoints;
				int j1 = (j + 1) % m_NumPoints;
				float vjx = Points[j1].x - Points[j].x;
				float vjy = Points[j1].y - Points[j].y;

				// Project vj along vi. The length is vj dot vi.
				float dot_product = vix * vjx + viy * vjy;

				// If the dot product <=, then j is
				// the index of the candidate control point.
				if (dot_product <= 0)
				{
					m_ControlPoints[3] = j;
					break;
				}
			}

			// If j == i, then i is not a suitable control point.
			if (m_ControlPoints[0] == i) return false;

			// These control points work.
			return true;
		}

		// Find the next bounding rectangle and check it.
		private void CheckNextRectangle()
		{
			// Increment the current control point.
			// This means we are done with using this edge.
			if (m_CurrentControlPoint >= 0)
			{
				m_ControlPoints[m_CurrentControlPoint] =
					(m_ControlPoints[m_CurrentControlPoint] + 1) % m_NumPoints;
			}

			// Find the next point on an edge to use.
			float dx0, dy0, dx1, dy1, dx2, dy2, dx3, dy3;
			FindDxDy(out dx0, out dy0, m_ControlPoints[0]);
			FindDxDy(out dx1, out dy1, m_ControlPoints[1]);
			FindDxDy(out dx2, out dy2, m_ControlPoints[2]);
			FindDxDy(out dx3, out dy3, m_ControlPoints[3]);

			// Switch so we can look for the smallest opposite/adjacent ratio.
			float opp0 = dx0;
			float adj0 = dy0;
			float opp1 = -dy1;
			float adj1 = dx1;
			float opp2 = -dx2;
			float adj2 = -dy2;
			float opp3 = dy3;
			float adj3 = -dx3;

			// Assume the first control point is the best point to use next.
			float bestopp = opp0;
			float bestadj = adj0;
			int best_control_point = 0;

			// See if the other control points are better.
			if (opp1 * bestadj < bestopp * adj1)
			{
				bestopp = opp1;
				bestadj = adj1;
				best_control_point = 1;
			}
			if (opp2 * bestadj < bestopp * adj2)
			{
				bestopp = opp2;
				bestadj = adj2;
				best_control_point = 2;
			}
			if (opp3 * bestadj < bestopp * adj3)
			{
				bestopp = opp3;
				bestadj = adj3;
				best_control_point = 3;
			}

			// Use the new best control point.
			m_CurrentControlPoint = best_control_point;

			// Remember that we have checked this edge.
			m_EdgeChecked[m_ControlPoints[m_CurrentControlPoint]] = true;

			// Find the current bounding rectangle
			// and see if it is an improvement.
			FindBoundingRectangle();
		}

		// Find the current bounding rectangle and
		// see if it is better than the previous best.
		private void FindBoundingRectangle()
		{
			// See which point has the current edge.
			int i1 = m_ControlPoints[m_CurrentControlPoint];
			int i2 = (i1 + 1) % m_NumPoints;
			float dx = Points[i2].x - Points[i1].x;
			float dy = Points[i2].y - Points[i1].y;

			// Make dx and dy work for the first line.
			switch (m_CurrentControlPoint)
			{
				case 0: // Nothing to do.
					break;
				case 1: // dx = -dy, dy = dx
					float temp1 = dx;
					dx = -dy;
					dy = temp1;
					break;
				case 2: // dx = -dx, dy = -dy
					dx = -dx;
					dy = -dy;
					break;
				case 3: // dx = dy, dy = -dx
					float temp2 = dx;
					dx = dy;
					dy = -temp2;
					break;
			}

			float px0 = Points[m_ControlPoints[0]].x;
			float py0 = Points[m_ControlPoints[0]].y;
			float dx0 = dx;
			float dy0 = dy;
			float px1 = Points[m_ControlPoints[1]].x;
			float py1 = Points[m_ControlPoints[1]].y;
			float dx1 = dy;
			float dy1 = -dx;
			float px2 = Points[m_ControlPoints[2]].x;
			float py2 = Points[m_ControlPoints[2]].y;
			float dx2 = -dx;
			float dy2 = -dy;
			float px3 = Points[m_ControlPoints[3]].x;
			float py3 = Points[m_ControlPoints[3]].y;
			float dx3 = -dy;
			float dy3 = dx;

			// Find the points of intersection.
			m_CurrentRectangle = new Vector2f[4];
			FindIntersection(px0, py0, px0 + dx0, py0 + dy0, px1, py1, px1 + dx1, py1 + dy1, ref m_CurrentRectangle[0]);
			FindIntersection(px1, py1, px1 + dx1, py1 + dy1, px2, py2, px2 + dx2, py2 + dy2, ref m_CurrentRectangle[1]);
			FindIntersection(px2, py2, px2 + dx2, py2 + dy2, px3, py3, px3 + dx3, py3 + dy3, ref m_CurrentRectangle[2]);
			FindIntersection(px3, py3, px3 + dx3, py3 + dy3, px0, py0, px0 + dx0, py0 + dy0, ref m_CurrentRectangle[3]);

			// See if this is the best bounding rectangle so far.
			// Get the area of the bounding rectangle.
			float vx0 = m_CurrentRectangle[0].x - m_CurrentRectangle[1].x;
			float vy0 = m_CurrentRectangle[0].y - m_CurrentRectangle[1].y;
			float len0 = (float)Math.Sqrt(vx0 * vx0 + vy0 * vy0);

			float vx1 = m_CurrentRectangle[1].x - m_CurrentRectangle[2].x;
			float vy1 = m_CurrentRectangle[1].y - m_CurrentRectangle[2].y;
			float len1 = (float)Math.Sqrt(vx1 * vx1 + vy1 * vy1);

			// See if this is an improvement.
			m_CurrentArea = len0 * len1;
			if (m_CurrentArea < m_BestArea)
			{
				m_BestArea = m_CurrentArea;
				m_BestRectangle = m_CurrentRectangle;
			}
		}

		// Find the slope of the edge from point i to point i + 1.
		private void FindDxDy(out float dx, out float dy, int i)
		{
			int i2 = (i + 1) % m_NumPoints;
			dx = Points[i2].x - Points[i].x;
			dy = Points[i2].y - Points[i].y;
		}

		// Find the point of intersection between two lines.
		private bool FindIntersection(float X1, float Y1, float X2, float Y2, float A1, float B1, float A2, float B2, ref Vector2f intersect)
		{
			float dx = X2 - X1;
			float dy = Y2 - Y1;
			float da = A2 - A1;
			float db = B2 - B1;
			float s, t;

			// If the segments are parallel, return False.
			if (Math.Abs(da * dy - db * dx) < 0.001) return false;

			// Find the point of intersection.
			s = (dx * (B1 - Y1) + dy * (X1 - A1)) / (da * dy - db * dx);
			t = (da * (Y1 - B1) + db * (A1 - X1)) / (db * dx - da * dy);
			intersect = new Vector2f(X1 + t * dx, Y1 + t * dy);
			return true;
		}

		// Find a smallest bounding rectangle.
		public Vector2f[] FindSmallestBoundingRectangle()
		{
			// This algorithm assumes the polygon
			// is oriented counter-clockwise.
			//Debug.Assert(!this.PolygonIsOrientedClockwise());

			// Get ready;
			ResetBoundingRect();

			// Check all possible bounding rectangles.
			for (int i = 0; i < Points.Length; i++)
			{
				CheckNextRectangle();
			}

			// Return the best result.
			return m_BestRectangle;
		}
		#endregion // Bounding Rectangle

	}

	public class Triangle : Polygon
	{
		public Triangle(Vector2f p0, Vector2f p1, Vector2f p2)
		{
			Points = new Vector2f[] { p0, p1, p2 };
		}
	}

	public class VersionChecker
	{
		public static (Version version, string url, long size) GetLastCompatibleVersion(string xmlFile, Version localVersion)
		{
			XDocument doc = XDocument.Parse(xmlFile);

			var items = doc.Descendants("item")
						   .Select(item => new
						   {
							   Version = System.Version.Parse(item.Element("enclosure")?.Attribute(XNamespace.Get("http://www.andymatuschak.org/xml-namespaces/sparkle") + "version")?.Value),
							   Url = item.Element("enclosure")?.Attribute("url")?.Value,
							   Length = long.Parse(item.Element("enclosure")?.Attribute("length")?.Value)
						   })
						   .Where(x => x.Version != null)
						   .ToList();

			var compatibleVersions = items.Where(x => x.Version.Major == localVersion.Major && x.Version.Minor == localVersion.Minor)
										  .OrderByDescending(x => x.Version.Build)
										  .ThenByDescending(x => x.Version.Revision)
										  .ToList();

			var latestVersionItem = compatibleVersions.FirstOrDefault();

			if (latestVersionItem != null)
			{
				return (latestVersionItem.Version, latestVersionItem.Url, latestVersionItem.Length);
			}

			return (null, null, 0);
		}

		public static bool IsCompatibleVersion(Version editorVersion, Version packageVersion)
			=> (editorVersion.Major == packageVersion.Major && editorVersion.Minor == packageVersion.Minor);

		public static bool IsEditorNewer(Version editorVersion, Version packageVersion)
			=> (editorVersion.Major == packageVersion.Major && editorVersion.Minor == packageVersion.Minor &&
				(editorVersion.Build > packageVersion.Build ||
				 (editorVersion.Build == packageVersion.Build && editorVersion.Revision > packageVersion.Revision)));

		public static bool IsPackageNewer(Version editorVersion, Version packageVersion)
			=> (editorVersion.Major == packageVersion.Major && editorVersion.Minor == packageVersion.Minor &&
				(packageVersion.Build > editorVersion.Build ||
				 (packageVersion.Build == editorVersion.Build && packageVersion.Revision > editorVersion.Revision)));

		public static string GetVersionMismatchMessage(Version editorVersion, Version packageVersion)
		{
			if (!IsCompatibleVersion(editorVersion, packageVersion))
			{
				return "This application is not compatible with the current Editor.\nMajor and minor versions must match.";
			}
			else if (IsPackageNewer(editorVersion, packageVersion))
			{
				return "This application requires a newer Editor version.\nPlease update the Editor.";
			}
			else if (IsEditorNewer(editorVersion, packageVersion))
			{
				return "This application is outdated for the current Editor version.\nPlease update the application from the Packages page.";
			}
			return null; // Versions are compatible
		}

	}
}
