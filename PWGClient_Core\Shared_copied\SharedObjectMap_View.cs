﻿//TABULA_GUID:{AADEBB2C-5908-4B69-AD7B-00CE62467987}
#if !SHAREDOBJECTMAP_CLIENT_LIGHT
using PropertyChanged;
#endif
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Tabula.Log;

#if SHAREDOBJECTMAP_CLIENT_LIGHT
public class AddINotifyPropertyChangedInterfaceAttribute : Attribute { };
public class DoNotCheckEqualityAttribute : Attribute { };
namespace PropertyChanged { };
#endif

namespace Tabula.SharedObjectMap
{

    // Additional methods for views
    public static partial class SharedObjectMap
    {
        // the RootView is used to build the property paths when getting views directly from the model
        public static IGuidObjectSyncView RootView { get; set; }        

        // This handles is called both when receiving from server, or when a view sets a value and sends to the server
        // if the view is a collection of non-guidobjects also item_index_or_key will be valorized
        public static Action<bool, IGuidObjectSyncView, string, object, UpdateType> notifyViewUpdate;   // from_server, view, fieldname, item_index_or_key, update_type

        public static V GetRootView<V>() where V : IGuidObjectSyncView, new()
        {
            if (RootView == null || !(RootModel.Equals(RootView.GetModel())))
            {
                RootView = new V();
                RootView.SetModel(RootModel);
            }

            return (V) RootView;
        }

        public static void SetRootView(IGuidObjectSyncView root)
        {
            if (RootModel == null)
                throw new ArgumentException("RootModel not set");

            RootView = root;
            RootView.SetModel(RootModel);
        }

        public static IGuidObjectSyncView getViewFromGuid(long guid)
        {
            GuidObject obj;
            if (TryGetObject(guid, out obj))
                return getViewFromModel(obj);
            else
            {
                Logger.DefaultLog.logException("", new ArgumentException($"Cannot get object from guid guid={guid}"));
                return null;
            }
        }

        public static void InitializeAllViews()
        {
            foreach (var kvp in object_map)
            {
                var model = kvp.Value;

                if (model.__view == null)
                {
                    try
                    {
                        var view = SharedObjectMap.getViewFromModel(model);

                        System.Diagnostics.Debug.WriteLine($"InitializeAllViews guid:{model.__guid} view:{view}");
                    }
                    catch(Exception ex)
                    {
                        // cannot initialize view
                    }
                }
			}
        }

        // returns the model's view, if not already store in __view will use reflection to query the  path 
        // NOTE: since a path is to be built, the RootModel is used
        public static IGuidObjectSyncView getViewFromModel(GuidObject model)
        {
            if (model.__view != null)
                return model.__view;

            if (RootView == null)
                throw new ArgumentException("No RootView defined");

            // View has not been created yet, so query the model path and then evaluate the view property path
            // this will ensure the full hierarchy of views with containers is created an assigned
            // TODO: this technique is better than having a static creator like wrapper?
            string model_path = FindGuidObjectPathAsString(model.__guid);

            // WARNING: View operations must happen in UI dispatcher! so the main message pump must be called from there
            IGuidObjectSyncView view = GetViewFromPath(RootView, model_path);

            if (view == null)
            {
                // problem!                
                log(model,$"getViewFromModel() view not found! Path={model_path}");
                return null;
            }

            return  view;            
        }

        // uses reflection to evaluate a path of properties from a root object, example: Model.List1[3].Prop1.Prop2
        public static IGuidObjectSyncView GetViewFromPath(object root, string propertypath)
        {
            // TEST:
            if (propertypath == "")
                return null;

            var path = propertypath.Split('.');

            object current_obj = root;
            try
            {
                foreach (var property in path)
                {
                    string name = property;
                    string index = string.Empty;

                    // isolate name and indexer
                    Regex re = new Regex(@"(.*)\[(.*)\]");
                    var match = re.Match(property);
                    if (match.Success)
                    {
                        name = match.Groups[1].Value;
                        index = match.Groups[2].Value;
                    }

                    var p_info = current_obj.GetType().GetProperty(name, BindingFlags.Public | BindingFlags.Instance);
                    current_obj = p_info.GetValue(current_obj);

                    if (!string.IsNullOrEmpty(index))
                    {
                        // https://stackoverflow.com/questions/937224/propertyinfo-getvalue-how-do-you-index-into-a-generic-parameter-using-reflec
                        String indexerName = ((DefaultMemberAttribute) current_obj.GetType().GetCustomAttributes(typeof(DefaultMemberAttribute),true)[0]).MemberName;
                        PropertyInfo pi2 = current_obj.GetType().GetProperty(indexerName);


                        // index can be int or dict key!
                        try
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { Convert.ToInt32(index) });
                        }
                        catch (ArgumentException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch (FormatException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch (Exception ex)
                        {
                            Logger.DefaultLog.logException(log($"GetViewFromPath() property_path={propertypath}"), ex);
                            return null;
                        }
                                                
                    }
                }
            }
            catch(Exception ex)
            {
                Log.Logger.DefaultLog.logException($"GetViewFromPath({propertypath})", ex);
                throw new ArgumentException($"Exception getting view from path={propertypath} current_obj={current_obj} type={current_obj.GetType()}");
            }

            return (IGuidObjectSyncView) current_obj;
        }

        // will wait until a guid is present and return it
        public static async Task<V> waitForView<V>(long guid, int timeout = 5000, int delay = 16) where V : IGuidObjectSyncView
        {
            var obj = await waitForGuidObject(guid);
            if (obj == null)
                return default(V);

            return (V) getViewFromModel(obj);
        }

        public static void CommitBegin_View(GuidObject model)
        {
            // In order to avoid starvation of pending commits, each begin acts as an end first
            CommitEnd_View(model);

            model.CommitBegin();
        }

        // Static helpers for view
        // NOTE: CommitEnd implementations for wrapper/view are the same, just sending differently
        public static void CommitEnd_View(GuidObject model)
        {
            if (Settings.HasFlag(SettingsFlags.DisableCommit))
                return;

            // Invokes the action to commit for this and all guidobjects children
            // Gathers all the accumualated updates
            List<GuidUpdate> commit_updates = new List<GuidUpdate>();

            // Invokes the action to commit for this and all guidobjects children
            var action = new Action<GuidObject>((o) =>
            {
                if (/*!o.__is_in_commit || */o.__delayed_updates == null)
                {
                    // can be in commit but without updates, so commit must be always cleared!
                    o.CommitEnd();
                    return;
                }

                lock (o.__delayed_updates)
                {
                    foreach (var u in o.__delayed_updates)
                        commit_updates.Add(u);

                    if (Debug)
                        log($"VIEW: CommitEnd_View ENQUEUE commit_guid={model.__guid}");
                }

                o.CommitEnd();
            });

            _scanGuidObjects(model,
                new Operation()
                {
                    OperationType = OperationType.CallAction,
                    Action = action,
                });

            // process all updates at once, implementation should handle it as a batch
            // TODO: what to do with returned results? nothing!
            if (commit_updates.Count>0)
                RootView.onViewSyncBatch?.Invoke(commit_updates, false);
        }        

        // Syncs with server, needs a local implementation for the real data transfer on the onViewSync method
        public static SingleUpdateResult Sync(GuidUpdate update, bool need_result)
        {
            // skip syncs without guid (the ones coming from out-of-model views)
            if (update.Guid == -1)
            {
                if (Debug)
                    log(update, $"Skipped for null Guid");

                return SingleUpdateResult.Skip;
            }

            // flag update if they need results
            if (need_result)
                update.Flags |= UpdateFlags.NeedResult;

            // TODO: check existing guid?
            if (!object_map.ContainsKey(update.Guid))
                return SingleUpdateResult.ObjectNotFound;

            GuidObject model = object_map[update.Guid];

            if (model.IsInCommit)
            {
                model.AddDelayedUpdate(update);               

                return SingleUpdateResult.InCommit;
            }
            else
            {
                if (Debug)
                    log(update);

                return RootView.onViewSync(update, need_result);
            }
        }

        public static SingleUpdateResult[] SyncBatch(List<GuidUpdate> updates, bool get_results = false)
        {
            return RootView.onViewSyncBatch(updates, get_results);
        }

        // DirectSync methods

        // DEPRECATED
        /*
        public static void SyncDirect(long guid, string fieldname, int value, int index=-1) => RootView.onViewSyncDirectInt?.Invoke(guid, fieldname, value, index);
        public static void SyncDirect(long guid, string fieldname, float value, int index=-1) => RootView.onViewSyncDirectFloat?.Invoke(guid, fieldname, value, index);
        public static void SyncDirect(long guid, string fieldname, double value, int index=-1) => RootView.onViewSyncDirectDouble?.Invoke(guid, fieldname, value, index);
        */

        // catch-all method if value is not a static type
        public static void SyncDirect(long guid, string fieldname, object value, int index = -1)
        {
            Type type = value.GetType();
            if (value is System.Int32)
            {
                SyncDirect(guid, fieldname, (int) value, index);
            }
            else if (value is System.Single)
            {
                SyncDirect(guid, fieldname, (float) value, index);
            }
            else if (value is System.Double)
            {
                SyncDirect(guid, fieldname, (double) value, index);
            }
            else
                throw new ArgumentException("SyncDirect() cannot determine field type");
        }
    }

    // for View extension methods
    public static partial class SharedObjectMapExtensions
    {
        public static IGuidObjectSyncView getView(this long guid)
        {
            return SharedObjectMap.getViewFromGuid(guid);
        }

        public static V getView<V>(this long guid)
        {
            return (V) SharedObjectMap.getViewFromGuid(guid);
        }

        public static async Task<V> waitForView<V>(this long guid, int timeout = 5000, int delay = 16) where V : IGuidObjectSyncView
        {
            return await SharedObjectMap.waitForView<V>(guid, timeout, delay);
        }
    }

    [AddINotifyPropertyChangedInterface]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class GuidObjectSyncView<T> : IGuidObjectSyncView, INotifyPropertyChanged where T : GuidObject, new()
    {       
        // reference to model
        public T Model { get; private set; }
        public void SetModel(T _model, IGuidObjectSyncView _parent = null, object _container = null)
        {
            Model = _model;
            if (Model != null)
            {
                Model.__view = this;
                Model.__view_parent = _parent;
                Model.__view_container = _container;

                // Invalidate all contained collections so their models are updated
                SharedObjectMap._invalidateCollection(this, null);

                /*
                if (SharedObjectMap.Debug)
                    SharedObjectMap.log(Model, "ViewCreated");
                */
                    
            }
            RaiseAllPropertyChanged();
        }

        // Used in partial classes to define UI specific behaviour (binding to commands etc..)
        public virtual void OnCreate()
        { }

        public Func<GuidUpdate, bool, SingleUpdateResult> onViewSync { get; set; }
        public Func<List<GuidUpdate>, bool, SingleUpdateResult[]> onViewSyncBatch { get; set; }
        public Action<long, string, int, int> onViewSyncDirectInt { get; set; }
        public Action<long, string, float, int> onViewSyncDirectFloat { get; set; }
        public Action<long, string, double, int> onViewSyncDirectDouble { get; set; }
        public Func<bool, IGuidObjectSyncView, string, object, UpdateType, bool> onNotifyUpdate { get; set; }


        public void SetModel(object _model, IGuidObjectSyncView _parent = null, object _container = null) => SetModel((T)_model, _parent, _container);

        public GuidObject GetModel() => Model;

        public IGuidObjectSyncView GetParent() => (IGuidObjectSyncView) __Parent;

        public object GetContainerIndexOrKey() => __ContainerIndex;

		public override string ToString() => $"GuidObjectSyncView guid:{Model.__guid}";

        // batched updates
        private List<GuidUpdate> delayed_updates;
        private bool _is_in_commit = false;

        // Updates the whole object by calling sync on parent object with the right fieldname in path
        public void Update()
        {
            var parent_fieldname = Model.GetParentFieldName();
            var update = GuidUpdate.ToServer(Model.__guid_parent, parent_fieldname, Model.GetType(), Model);
            var ret = SharedObjectMap.Sync(update, true);
            if (ret.IsValid)
            {
                update.FieldValue = (T)ret.GetFinalValue(Model);
                SharedObjectMap.UpdateField(null, update, true);
            }
        }

        public void CommitBegin() => SharedObjectMap.CommitBegin_View(Model); // Model.CommitBegin();

        public void CommitEnd() => SharedObjectMap.CommitEnd_View(Model);

        // VALIDATION: view subclasses can override and implement validation rules

		public virtual object GetValidatedValue(string name, object value) => value; //?


		/*
        public async Task<bool> CommitEndAndWait(bool apply_updates = true, int timeout = 5000, int delay = 16)
        {
            if (!SharedObjectMap.IsCommitEnabled)
                return false;

            CommitEnd();
            return await SharedObjectMap.waitForCommit(timeout, delay);
        }        
        */

		// PropertyChanged

		public event PropertyChangedEventHandler PropertyChanged;

        public virtual void RaisePropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public virtual void RaiseAllPropertyChanged()
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(string.Empty));
        }

        public void OnItemUpdated(bool from_server , object item_key_or_index, UpdateType update_type = UpdateType.SetField)
		{
            throw new NotSupportedException("OnItemUpdated() wrong specialized called on GuidObject collection");
		}

        // different semantics, it is called on the parent view, with the name of the collection
        public void OnItemUpdated(bool from_server, string __collection_fieldname, object item_key_or_index, UpdateType update_type = UpdateType.SetField)
        {
            RaisePropertyChanged(__collection_fieldname);
            SharedObjectMap.notifyViewUpdate?.Invoke(from_server, this, __collection_fieldname, item_key_or_index, update_type);
        }

        // Notifies the update calling methods up to the parent until one catches it, plus call global handlers
        public void NotifyViewUpdate(bool from_server, IGuidObjectSyncView view, string fieldname, object item_index_or_key = null, UpdateType update_type = UpdateType.SetField)
        {
            IGuidObjectSyncView v = this;
            while (v != null)
            {
                if (v.onNotifyUpdate != null)
                {
                    if (v.onNotifyUpdate.Invoke(from_server, view, fieldname, item_index_or_key, update_type))
                        break;
                }

                v = v.GetParent();
            }

            // global handler
            SharedObjectMap.notifyViewUpdate?.Invoke(from_server, view, fieldname, item_index_or_key, update_type);
        }

        // Helpers

        public long __Guid => Model.__guid;

        // TODO: change in object and support dictionary key?
        public int __ContainerIndex
        {
            get
            {
                if (Model.__view_container != null)
                {
                    if (Model.__view_container is IList)
                    {
                        return (Model.__view_container as IList).IndexOf(Model.__view);
                    }
                    else if (Model.__view_container is IDictionary)
                    {
                        // TODO:
                        return -1;
                    }
                    else
                        return -1;
                }
                else
                    return -1;
            }
        }

        public object __Parent => Model.__view_parent;

        public object __This => Model.__view;

        protected long _return_guid(SingleUpdateResult res)
		{
            try
            {
                if (res.Flags.HasFlag(UpdateResultFlags.InCommit))
                    return -1L;
                else if (res.ChangedValue != null)
                    return (long) res.ChangedValue;
                else
                    return -1L;
            }
            catch
			{
                return -1L;
			}
		}
    }

    // Base class for List/Array items views (NOT GuidObjects, that will be handled with an ObservableCollection / ObservableDictionary)

    [AddINotifyPropertyChangedInterface]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public abstract class CollectionSyncView<T> : IGuidObjectSyncView, INotifyPropertyChanged
    {
        public abstract GuidObject GetModel();

        public abstract IGuidObjectSyncView GetParent();

        public abstract object GetContainerIndexOrKey();

        public void SetModel(object _model, IGuidObjectSyncView _parent = null, object _container = null)
        {
            throw new NotImplementedException("CollectionSyncView does not implement SetModel");
        }

        public Func<GuidUpdate, bool, SingleUpdateResult> onViewSync { get; set; }
        public Func<List<GuidUpdate>, bool, SingleUpdateResult[]> onViewSyncBatch { get; set; }
        public Action<long, string, int, int> onViewSyncDirectInt { get; set; }
        public Action<long, string, float, int> onViewSyncDirectFloat { get; set; }
        public Action<long, string, double, int> onViewSyncDirectDouble { get; set; }
        public Func<bool, IGuidObjectSyncView, string, object, UpdateType, bool> onNotifyUpdate { get; set; }

        public void CommitBegin() => SharedObjectMap.CommitBegin_View(GetModel()); // GetModel().CommitBegin();

        public void CommitEnd() => SharedObjectMap.CommitEnd_View(GetModel());

        // PropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        public virtual void RaisePropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public virtual void RaiseAllPropertyChanged()
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(string.Empty));
        }

        // Syncs with server, needs a local implementation for the real data transfer on the onViewSync method
        public SingleUpdateResult Sync(GuidUpdate update, bool need_result)
        {
            return SharedObjectMap.Sync(update, need_result);

            /*
            long commit_guid = -1;
            if (SharedObjectMap.IsCommitDelayed(update.Guid, out commit_guid))
            {
                // TODO: check existing guid?
                lock (SharedObjectMap.delayed_updates)
                    SharedObjectMap.delayed_updates[commit_guid].Add(update);

                if (SharedObjectMap.Debug)
                    SharedObjectMap.log(update, $"AddUpdate Delayed count={SharedObjectMap.delayed_updates[commit_guid].Count}");

                return SingleUpdateResult.InCommit;
            }
            else
            {
                if (SharedObjectMap.Debug)
                    SharedObjectMap.log(update);

                return SharedObjectMap.RootView.onViewSync(update, need_result);
            }*/
        }

        public SingleUpdateResult[] SyncBatch(List<GuidUpdate> updates, bool get_results = false)
        {
            return SharedObjectMap.RootView.onViewSyncBatch(updates, get_results);
        }

        // public abstract void OnFieldUpdated(bool from_server, string fieldname);

        public abstract void OnItemUpdated(bool from_server, object item_key_or_index, UpdateType update_type = UpdateType.SetField);   
        
        public abstract void OnItemUpdated(bool from_server, string collection_fieldname, object item_key_or_index, UpdateType update_type = UpdateType.SetField);

        // added, to be tested
        public abstract void NotifyViewUpdate(bool from_server, IGuidObjectSyncView view, string fieldname, object item_index_or_key = null, UpdateType update_type = UpdateType.SetField);
    }

    [AddINotifyPropertyChangedInterface]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class SyncListItemView<T> : CollectionSyncView<T>
    {
        public long     __parent_guid;
        public string   __collection_fieldname;
        public int      __item_index;

        public IGuidObjectSyncView __parent_view;
        private bool    __direct_sync = false;
                
        public SyncListItemView(long parent_guid, string collection_fieldname, object item_index)
        {
            __parent_guid = parent_guid;
            __collection_fieldname = collection_fieldname;
            __item_index = (int) item_index;    // cast from object to have same constructor signature with dictionary

            __parent_view = __parent_guid.getView();
            if (__parent_view == null)
                throw new ArgumentException($"SyncListItemView: cannot get parent view, parent_guid={__parent_guid}");

            __direct_sync = (Attribute.IsDefined(__parent_view.GetModel().GetType().GetField(__collection_fieldname), typeof(GuidObjectFieldAttribute)));
        }

        public override GuidObject GetModel() => SharedObjectMap.object_map[__parent_guid];

        public override IGuidObjectSyncView GetParent() => __parent_view;

		public override object GetContainerIndexOrKey() => __item_index;

		public override string ToString() => $"SyncListItemView field:{__collection_fieldname} index:{__item_index}";

        // The single collection item value
        public T __Value
        {
            get
            {
                return (T) SharedObjectMap.GetFieldValue(__parent_guid, __collection_fieldname, __item_index);
            }

            set
            {
                var update = GuidUpdate.ToServer(__parent_guid, __collection_fieldname, null, value, __item_index);

                if (__direct_sync)
                {
                    /*
                    SharedObjectMap.SyncDirect(__parent_guid, __collection_fieldname, value, __item_index);
                    SharedObjectMap.UpdateFieldDirect(null, __parent_guid, __collection_fieldname, value, __item_index);
                    */

                    update.Flags |= Tabula.SharedObjectMap.UpdateFlags.DirectSync;
                }

                   
                Sync(update, false);
                SharedObjectMap.UpdateField(null, update, true);                
            }
        }

        /*
        public override void OnFieldUpdated(bool from_server, string fieldname)
        {
            throw new ArgumentException("OnFieldUpdated() cannot happen for a SyncListItemView");
        }*/

        public override void OnItemUpdated(bool from_server, object item_key_or_index, UpdateType update_type = UpdateType.SetField)
        {
            RaiseAllPropertyChanged();
            SharedObjectMap.notifyViewUpdate?.Invoke(from_server, __parent_view, __collection_fieldname, item_key_or_index, update_type);
        }

        public override void OnItemUpdated(bool from_server, string collection_fieldname, object item_key_or_index, UpdateType update_type = UpdateType.SetField)
        {
            throw new NotSupportedException("OnItemUpdated() wrong specialized called on a non-GuidObject collection");
        }

		//HACK:
		public override void NotifyViewUpdate(bool from_server, IGuidObjectSyncView view, string fieldname, object item_index_or_key = null, UpdateType update_type = UpdateType.SetField)
		{
			throw new NotSupportedException("NotifyViewUpdate() not implemented");
		}
	}


    [AddINotifyPropertyChangedInterface]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class SyncDictionaryItemView<T> : CollectionSyncView<T>
    {
        public long __parent_guid;
        public string __collection_fieldname;
        public object __item_key;

        public IGuidObjectSyncView __parent_view;

        public SyncDictionaryItemView(long parent_guid, string collection_fieldname, object item_key)
        {
            __parent_guid = parent_guid;
            __collection_fieldname = collection_fieldname;
            __item_key = item_key;

            __parent_view = __parent_guid.getView();
            if (__parent_view == null)
                throw new ArgumentException($"SyncDictionaryItemView: cannot get parent view, parent_guid={__parent_guid}");
        }

        public override GuidObject GetModel() => SharedObjectMap.object_map[__parent_guid];

        public override IGuidObjectSyncView GetParent() => __parent_view;

		public override object GetContainerIndexOrKey() => __item_key;

		public override string ToString() => $"SyncDictionaryItemView field:{__collection_fieldname} key:{__item_key}";

        // The single collection item value
        public T __Value
        {
            get
            {
                return (T) SharedObjectMap.GetFieldValue(__parent_guid, __collection_fieldname, -1, __item_key);
            }

            set
            {
                var update = GuidUpdate.ToServer(__parent_guid, __collection_fieldname, null, value, __item_key);
                Sync(update, false);
                SharedObjectMap.UpdateField(null, update, true);
                /*
                if (ret.IsValid)
                {
                    update.FieldValue = (T)ret.GetFinalValue(value);
                    SharedObjectMap.UpdateField(update, true);
                }*/
            }
        }

        /*
        public override void OnFieldUpdated(bool from_server, string fieldname)
        {
            throw new ArgumentException("OnFieldUpdated() cannot happen for a SyncDictionaryItemView");
        }*/

        public override void OnItemUpdated(bool from_server, object item_key_or_index, UpdateType update_type = UpdateType.SetField)
        {
            RaiseAllPropertyChanged();
            SharedObjectMap.notifyViewUpdate?.Invoke(from_server, __parent_view, __collection_fieldname, item_key_or_index, update_type);
        }

        public override void OnItemUpdated(bool from_server, string collection_fieldname, object item_key_or_index, UpdateType update_type = UpdateType.SetField)
        {
            throw new NotSupportedException("OnItemUpdated() wrong specialized called on a non-GuidObject collection");
        }

        //TODO: this is for view notifies from server, to be tested
		public override void NotifyViewUpdate(bool from_server, IGuidObjectSyncView view, string fieldname, object item_index_or_key = null, UpdateType update_type = UpdateType.SetField)
        {
			throw new NotSupportedException("NotifyViewUpdate() not implemented");
		}
	}

    // TEST: EXPERIMENTAL: Extended ObservableCollection that allows for adding/removing/clearing items directly from view with auto-sync
    // NOTE: it does not implement IList, so a restricted set of actions is implemented for model M with special names
    // M: is the model used for the view VT 
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class ObservableCollectionSyncView<M,VT> : System.Collections.ObjectModel.ObservableCollection<VT>
    {
        public long __parent_guid;
        public string __collection_fieldname;
        public object __collection_model;

        public ObservableCollectionSyncView(long parent_guid, string collection_fieldname, object collection_model)
        {
            __parent_guid = parent_guid;
            __collection_fieldname = collection_fieldname;
            __collection_model = collection_model;
        }

        public void Update()
        {
            var update = GuidUpdate.ToServer(__parent_guid, __collection_fieldname, __collection_model.GetType(), __collection_model);
            var ret = SharedObjectMap.Sync(update, true);
            if (ret.IsValid)
            {
                update.FieldValue = ret.GetFinalValue(__collection_model);
                SharedObjectMap.UpdateField(null, update, true);
            }
        }        

        // M can be a guidobject, or not
        public void AddModel(M item)
        {
            FieldInfo f;
            GuidObject parent_obj;
            if (!SharedObjectMap.GetField(__parent_guid, __collection_fieldname, out f, out parent_obj))
                return; // TODO: exception

            if (typeof(IList).IsAssignableFrom(f.FieldType))
            {
                IList list = f.GetValue(parent_obj) as IList;
                list.Add(item);                
            }
            else
            {
                // Only list supported?
                throw new NotSupportedException("Only IList models are supported");
            }

            // sync all
            Update(); 
        }

        public void RemoveModelAt(int index)
        {            
            throw new NotImplementedException();
        }

        public void ClearModels()
        {
            throw new NotImplementedException();
        }
    }

    //TODO: ObservableDictionarySyncView : ObservableDictionary
}

namespace System.Collections.ObjectModel
{
    [Tabula.SharedObjectMap.Obfuscation_Skip]
    public class ObservableDictionary<TKey, TValue> : IDictionary<TKey, TValue>, INotifyCollectionChanged, INotifyPropertyChanged
    {
        private const string CountString = "Count";
        private const string IndexerName = "Item[]";
        private const string KeysName = "Keys";
        private const string ValuesName = "Values";

        public IDictionary<TKey, TValue> Dictionary { get; private set; }

#region Constructors
        public ObservableDictionary()
        {
            Dictionary = new Dictionary<TKey, TValue>();
        }
        public ObservableDictionary(IDictionary<TKey, TValue> dictionary)
        {
            Dictionary = new Dictionary<TKey, TValue>(dictionary);
        }
        public ObservableDictionary(IEqualityComparer<TKey> comparer)
        {
            Dictionary = new Dictionary<TKey, TValue>(comparer);
        }
        public ObservableDictionary(int capacity)
        {
            Dictionary = new Dictionary<TKey, TValue>(capacity);
        }
        public ObservableDictionary(IDictionary<TKey, TValue> dictionary, IEqualityComparer<TKey> comparer)
        {
            Dictionary = new Dictionary<TKey, TValue>(dictionary, comparer);
        }
        public ObservableDictionary(int capacity, IEqualityComparer<TKey> comparer)
        {
            Dictionary = new Dictionary<TKey, TValue>(capacity, comparer);
        }
#endregion

#region IDictionary<TKey,TValue> Members

        public void Add(TKey key, TValue value)
        {
            Insert(key, value, true);
        }

        public bool ContainsKey(TKey key)
        {
            return Dictionary.ContainsKey(key);
        }

        public ICollection<TKey> Keys
        {
            get { return Dictionary.Keys; }
        }

        public bool Remove(TKey key)
        {
            if (key == null) throw new ArgumentNullException("key");

            TValue value;
            Dictionary.TryGetValue(key, out value);
            var removed = Dictionary.Remove(key);
            if (removed)
                //OnCollectionChanged(NotifyCollectionChangedAction.Remove, new KeyValuePair<TKey, TValue>(key, value));
                OnCollectionChanged();

            return removed;
        }

        public bool TryGetValue(TKey key, out TValue value)
        {
            return Dictionary.TryGetValue(key, out value);
        }

        public ICollection<TValue> Values
        {
            get { return Dictionary.Values; }
        }

        public TValue this[TKey key]
        {
            get
            {
                if (Dictionary.ContainsKey(key))
                    return Dictionary[key];
                else
                    return default(TValue); // added by panda
            }
            set
            {
                Insert(key, value, false);
            }
        }

#endregion

#region ICollection<KeyValuePair<TKey,TValue>> Members

        public void Add(KeyValuePair<TKey, TValue> item)
        {
            Insert(item.Key, item.Value, true);
        }

        public void Clear()
        {
            if (Dictionary.Count > 0)
            {
                Dictionary.Clear();
                OnCollectionChanged();
            }
        }

        public bool Contains(KeyValuePair<TKey, TValue> item)
        {
            return Dictionary.Contains(item);
        }

        public void CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
        {
            Dictionary.CopyTo(array, arrayIndex);
        }

        public int Count
        {
            get { return Dictionary.Count; }
        }

        public bool IsReadOnly
        {
            get { return Dictionary.IsReadOnly; }
        }

        public bool Remove(KeyValuePair<TKey, TValue> item)
        {
            return Remove(item.Key);
        }

#endregion

#region IEnumerable<KeyValuePair<TKey,TValue>> Members

        public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
        {
            return Dictionary.GetEnumerator();
        }

#endregion

#region IEnumerable Members

        IEnumerator IEnumerable.GetEnumerator()
        {
            return ((IEnumerable)Dictionary).GetEnumerator();
        }

#endregion

#region INotifyCollectionChanged Members

        public event NotifyCollectionChangedEventHandler CollectionChanged;

#endregion

#region INotifyPropertyChanged Members

        public event PropertyChangedEventHandler PropertyChanged;

#endregion

        public void AddRange(IDictionary<TKey, TValue> items)
        {
            if (items == null) throw new ArgumentNullException("items");

            if (items.Count > 0)
            {
                if (Dictionary.Count > 0)
                {
                    if (items.Keys.Any((k) => Dictionary.ContainsKey(k)))
                        throw new ArgumentException("An item with the same key has already been added.");
                    else
                        foreach (var item in items) Dictionary.Add(item);
                }
                else
                    Dictionary = new Dictionary<TKey, TValue>(items);

                OnCollectionChanged(NotifyCollectionChangedAction.Add, items.ToArray());
            }
        }

        private void Insert(TKey key, TValue value, bool add)
        {
            if (key == null) throw new ArgumentNullException("key");

            TValue item;
            if (Dictionary.TryGetValue(key, out item))
            {
                if (add) throw new ArgumentException("An item with the same key has already been added.");
                if (Equals(item, value)) return;
                Dictionary[key] = value;

                OnCollectionChanged(NotifyCollectionChangedAction.Replace, new KeyValuePair<TKey, TValue>(key, value), new KeyValuePair<TKey, TValue>(key, item));
            }
            else
            {
                Dictionary[key] = value;

                OnCollectionChanged(NotifyCollectionChangedAction.Add, new KeyValuePair<TKey, TValue>(key, value));
            }
        }

        private void OnPropertyChanged()
        {
            OnPropertyChanged(CountString);
            OnPropertyChanged(IndexerName);
            OnPropertyChanged(KeysName);
            OnPropertyChanged(ValuesName);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void OnCollectionChanged()
        {
            OnPropertyChanged();
            CollectionChanged?.Invoke(this, new NotifyCollectionChangedEventArgs(NotifyCollectionChangedAction.Reset));
        }

        private void OnCollectionChanged(NotifyCollectionChangedAction action, KeyValuePair<TKey, TValue> changedItem)
        {
            OnPropertyChanged();
            CollectionChanged?.Invoke(this, new NotifyCollectionChangedEventArgs(action, changedItem));
        }

        private void OnCollectionChanged(NotifyCollectionChangedAction action, KeyValuePair<TKey, TValue> newItem, KeyValuePair<TKey, TValue> oldItem)
        {
            OnPropertyChanged();
            CollectionChanged?.Invoke(this, new NotifyCollectionChangedEventArgs(action, newItem, oldItem));
        }

        private void OnCollectionChanged(NotifyCollectionChangedAction action, IList newItems)
        {
            OnPropertyChanged();
            CollectionChanged?.Invoke(this, new NotifyCollectionChangedEventArgs(action, newItems));
        }
    }
}
