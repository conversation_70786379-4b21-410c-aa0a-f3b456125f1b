<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.PointerTestPage">
	<Grid RowDefinitions="Auto,*">
		<WrapPanel x:Name="panel" Grid.Row="0" Background="Gray">
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerEnter"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerEnter}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerMoved"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerMoved}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerLeave"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerLeave}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerPressed"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerPressed}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerReleased"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerReleased}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerWheelChanged"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerWheelChanged}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="PointerCaptureLost"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding PointerCaptureLost}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="Tapped"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding Tapped}"/>
			</Grid>
			<Grid ColumnDefinitions="*,*">
				<TextBlock Grid.Column="0" Margin="10" Text="DoubleTapped"/>
				<CheckBox Grid.Column="1" Margin="10" IsChecked="{Binding DoubleTapped}"/>
			</Grid>
		</WrapPanel>

		<ItemsControl Grid.Row="1" ItemsSource="{Binding events}" Background="White">
			<ItemsControl.ItemTemplate>
				<DataTemplate>
					<TextBlock Text="{Binding}" Foreground="Black" HorizontalAlignment="Stretch"/>
				</DataTemplate>
			</ItemsControl.ItemTemplate>
		</ItemsControl>
	</Grid>
</UserControl>
