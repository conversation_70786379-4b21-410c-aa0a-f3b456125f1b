﻿using System;
using System.Collections.Generic;
using System.IO;
using Avalonia;
using Sentry;

namespace Tabula.PWGClient;

// Original Avalonia xplat desktop
/*
class Program
{
    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static void Main(string[] args) => BuildAvaloniaApp()
        .StartWithClassicDesktopLifetime(args);

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .LogToTrace()
            .UseReactiveUI();
}
*/

class Program
{

	public static bool UseGpu = false;	// TRY forcing it always false

    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static void Main(string[] args)
    {
#if !DEBUG
		// Sentry monitoring
		using (SentrySdk.Init(o =>
		{
			o.Dsn = "https://<EMAIL>/4505012788264960";
			// When configuring for the first time, to see what the SDK is doing:
			o.Debug = false;
			// Set traces_sample_rate to 1.0 to capture 100% of transactions for performance monitoring.
			// We recommend adjusting this value in production.
			o.TracesSampleRate = 1.0;
			// Enable Global Mode if running in a client app
			o.IsGlobalModeEnabled = true;
		}))
#endif
		{
			SentrySdk.StartTransaction("app", "start").Finish();

			/*
			try
			{
				UseGpu = !File.Exists("disable_gpu");
				File.WriteAllText("use_gpu.txt", UseGpu ? "true" : "false");
			}
			catch { }
			*/

			// App code goes here. Dispose the SDK before exiting to flush events.
			BuildAvaloniaApp()
			.StartWithClassicDesktopLifetime(args);
		}

		
    }

	// Avalonia configuration, don't remove; also used by visual designer.
	public static AppBuilder BuildAvaloniaApp()
		=> AppBuilder.Configure<App>()
			.UsePlatformDetect()
			.With(new AvaloniaNativePlatformOptions()
			{
				RenderingMode = (UseGpu ? new List<AvaloniaNativeRenderingMode>()
				{ AvaloniaNativeRenderingMode.OpenGl,
				  AvaloniaNativeRenderingMode.Software
				} :
				new List<AvaloniaNativeRenderingMode>()
				{ AvaloniaNativeRenderingMode.Software
				})
			})	
			.LogToTrace();
}
