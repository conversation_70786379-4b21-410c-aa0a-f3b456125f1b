﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Platform;
using Avalonia.Controls.Shapes;
using Avalonia.Platform.Storage;
using Avalonia.VisualTree;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Tabula.PWGClient;

// A wrapper for xplat

internal static class FileSystem
{
	public enum Platform
	{
		None,
		Windows,
		Android
	}

	private static Platform platform;
	private static TopLevel toplevel;

	private static string root_path = string.Empty;

	public static void Configure(Visual visual)
	{
		_check_platform();

		

		switch (platform)
		{
			case Platform.Android:
				toplevel = TopLevel.GetTopLevel(visual);
				var folder = toplevel.StorageProvider.TryGetWellKnownFolderAsync(Avalonia.Platform.Storage.WellKnownFolder.Documents).Result;
				root_path = folder.Path.AbsolutePath;
				break;
		}
	}


	private static void _check_platform()
	{
		if (platform != Platform.None)
			return;

		if (RuntimeInformation.RuntimeIdentifier.Contains("android"))
		{
			platform = Platform.Android;
		}
		else
			platform = Platform.Windows;
	}

	private static string _final_folder(string path)
		=> System.IO.Path.Combine(root_path, path);

	public static bool Exists(string path)
		=> System.IO.File.Exists(_final_folder(path));

	public static void WriteAllText(string path, string contents)
		=> System.IO.File.WriteAllText(_final_folder(path), contents);

	public static void WriteAllText(string path, string contents, Encoding encoding)
		=> System.IO.File.WriteAllText(_final_folder(path), contents, encoding);

	public static StreamWriter AppendText(string path)
		=> System.IO.File.AppendText(_final_folder(path));

	public static void WriteAllBytes(string path, byte[] bytes)
		=> System.IO.File.WriteAllBytes(_final_folder(path), bytes);

	public static Task WriteAllBytesAsync(string path, byte[] bytes, CancellationToken token = default)
		=> System.IO.File.WriteAllBytesAsync(path, bytes, token);

	public static string ReadAllText(string path)
		=> System.IO.File.ReadAllText(_final_folder(path));

	public static string ReadAllText(string path, Encoding encoding)
		=> System.IO.File.ReadAllText(_final_folder(path), encoding);

	public static byte[] ReadAllBytes(string path)
		=> System.IO.File.ReadAllBytes(_final_folder(path));

	public static FileStream Create(string path)
		=> System.IO.File.Create(_final_folder(path));

	public static StreamWriter CreateText(string path)
		=> System.IO.File.CreateText(_final_folder(path));

	public static void Delete(string path)
		=> System.IO.File.Delete(_final_folder(path));

	public static FileStream Open(string path, FileMode mode)
		=> System.IO.File.Open(_final_folder(path), mode);

	public static FileStream Open(string path, FileMode mode, FileAccess access)
		=> System.IO.File.Open(_final_folder(path), mode, access);

	public static FileStream Open(string path, FileMode mode, FileAccess access, FileShare share)
		=> System.IO.File.Open(_final_folder(path), mode, access, share);

	public static FileStream OpenRead(string path)
		=> System.IO.File.OpenRead(_final_folder(path));

	public static FileStream OpenWrite(string path)
		=> System.IO.File.OpenWrite(_final_folder(path));

	public static void Copy(string src, string dst, bool overwrite = false)
		=> System.IO.File.Copy(src, dst, overwrite);
}

