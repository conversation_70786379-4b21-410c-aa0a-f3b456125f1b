﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;


namespace Tabula.PMCore
{
  

    public class PMOutputSurfaceRegionView : PMView<Tabula.PMCore.OutputSurface, Tabula.PMCore.OutputSurfaceView>
    {
        private SKRectangleEdit rect_edit;
        private SizeI screen_size;

        public PMOutputSurfaceRegionView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }

        // This model is recreated when the whole warp is reset
        public override void CreateVisual()
        {
            bool was_selected = Visual!=null ? Visual.IsSelected : false;

            if (Visual != null)
                Visual.Scene.Remove(Visual);

            screen_size = SARGAME.Model.Screen.size;

		    rect_edit = new SKRectangleEdit(Scene, new SKRect(0,0,screen_size.width,screen_size.height))
		    {
                IsDraggable = false,
			    LockMove = true,
			    Simmetrical = true,
			    MinimumSize = new SKSize(100,100),
			    MaximumRect = new SKRect(0,0, screen_size.width, screen_size.height)
		    };

		    rect_edit.Layer = SKScene.LayerContours;
            rect_edit.OnVertexMove += OnVertexMove;

            Visual = rect_edit;

            Visual.onMove += (o, pos) => OnMove(pos);

            //VisualPoly.onKey += OnKey;

            Visual.onSelected += OnSelected;
            Visual.onUnSelected += OnUnSelected;

            if (was_selected)
                Visual.Scene.SelectObject(Visual);

            VisualIsDirty = true;

            base.CreateVisual();
        }

        public override void BeforeVisualUpdate()
        {
            // if it is dirty reconfigure the visual
            if (VisualIsDirty)
            {
                rect_edit.Rect.Left = Model.x_offset * (float) screen_size.width;
                rect_edit.Rect.Top = Model.y_offset * (float) screen_size.height;

                rect_edit.SetSize(new SKSize()
                {
                    Width = (float) screen_size.width * Model.x_tiling,
                    Height = (float) screen_size.height * Model.y_tiling
                });

                rect_edit.SyncToControlPoints();
                

				VisualIsDirty = false;
            }

            base.BeforeVisualUpdate();
        }
        
        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            lock (this)
            {
                if (from_server)
                {
                    //TODO: optimize just for the tiling?

                    // every vertex x,y will be notified, so just ask for an update in BeforeVisualUpdate()
                    //if (view.GetParent().GetModel() is OutputSurface) // good example, but not needed
                    {
                        VisualIsDirty = true;
                    }

                }
            }

            return true;
        }
        

        public void OnMove(SKPoint pos)
        {
            /*
            var center = Visual.GetPosition();            

            View.CommitBegin();
            
            for (int i=0; i<View.vertices.Count; i++)
            {                                        
                VisualPoly.SyncFromControlPoint(i);
                View.vertices[i].x = (int)VisualPoly.Vertices[i].X + (int) center.X;
                View.vertices[i].y = (int)VisualPoly.Vertices[i].Y + (int) center.Y;
            }
            
            View.CommitEnd();
            */
        }

        
        public void OnVertexMove(SKControlPoint cp, int index, SKPoint pos)
        {
            UpdateRegion();

            /*
            var center = Visual.GetPosition();

            // optimize syncing only right segment
            VisualPoly.SyncFromControlPoint(index);

            View.CommitBegin();
                View.vertices[index].x = (int) VisualPoly.Vertices[index].X + (int)center.X;
                View.vertices[index].y = (int) VisualPoly.Vertices[index].Y + (int)center.Y;
            View.CommitEnd();   
            */
        }

        // calculates region and sends to server
        public void UpdateRegion()
        {
            var x_offset = (float)rect_edit.Rect.Left / (float) screen_size.width;
			var y_offset = (float)rect_edit.Rect.Top / (float) screen_size.height;

            var x_tiling = (float)rect_edit.Rect.Width / (float)screen_size.width;
			var y_tiling = (float)rect_edit.Rect.Height / (float)screen_size.height;

            View.CommitBegin();
                View.x_offset = x_offset;
                View.y_offset = y_offset;
                View.x_tiling = x_tiling;
                View.y_tiling = y_tiling;
            View.CommitEnd();
		}
        
        public void OnSelected()
        {
            //SelectControlPoint(null);
        }

        public void OnUnSelected()
        {
            //SelectControlPoint(null);
        }
    }
}
