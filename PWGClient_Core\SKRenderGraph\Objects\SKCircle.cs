﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

namespace Tabula.SKRenderGraph
{
    public class SKCircle : SKObject
    {
        public SKPoint  Center = SKPoint.Empty;
        public float    Radius = 10;        // visual radius
        public float    HitRadius = -1;     // used as a hit radius if specified

        public override bool Contains(SKPoint pos) => (pos - Center).Length < (HitRadius>0 ? HitRadius : Radius);

        public SKCircle(SKScene scene, SKPoint pos, float radius) : base(scene)
        {
            Center = pos;
            Radius = radius;
        }

        public override SKPoint GetPosition() => Center;

        public override void SetPosition(SKPoint pos)
        {
            Center = pos;

            base.SetPosition(pos);
        }

        public override void SetSize(SKSize size)
        {
            // TODO: ellipse?
            Radius = size.Width;

            base.SetSize(size);
        }

        public override SKSize GetSize() => new SKSize(Radius, Radius);

		public override void Move(SKPoint pos_rel)
        {
            Center.Offset(pos_rel);

            base.Move(pos_rel);
        }

        public override void Update()
        {
            if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsLeftMouseStartDragging)
            {
                Paint.Color = ColorHover;

				// TODO: doesn't support IsSelectableWithLeftClick yet
				if (Scene.MouseLeftPressed)
                {
                    DragStart();
                }
            }
            else
                Paint.Color = Color;
        }

        public override void Draw()
        {
            Canvas.DrawCircle(Center, Radius, Paint);
        }
    }
}
