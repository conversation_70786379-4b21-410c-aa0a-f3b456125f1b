﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;

namespace Tabula.AvaloniaUtils
{
	public static class AvaloniaUtils
	{
		public static T FindAncestor<T>(this Control ctl) where T : Control
		{
			var parent = ctl.Parent;

			while (parent != null)
			{
				if (parent.GetType() == typeof(T))
					return (T) parent;
				else
					parent = parent.Parent;
			}

			return default(T);
		}
		

	}
}
