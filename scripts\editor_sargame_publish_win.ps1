c:
cd "C:\G\Codice\TABULA.PMCORE\PWGClient_xplat"

# Main dotnet compile, as a single file (only some dlls are external)
dotnet publish PWGClient.Desktop -c Release /p:PublishProfile=PWGClient.Desktop\Properties\PublishProfiles\Windows.pubxml

# Extract the "FileVersion" string and save it to version.txt
versioninfo -j PWGClient.Desktop\bin\Release\net7.0\win-x64\S-ARGAME_Editor.dll >PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\version.json
$jsonData = Get-Content -Raw -Path "PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\version.json" | ConvertFrom-Json
$jsonData[0].FileVersion | Out-File -FilePath "PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\version.txt" -Encoding UTF8

# Protect
dotNET_Reactor.Console.exe -project "Reactor_S-ARGAME_Editor_dotnet_win.nrproj"

# Copy important Dlls not in the single file
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\*.dll deploy\win-x64\ /Y
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\win-x64\version.txt deploy\win-x64\ /Y

# Remove unwanted dlls
del deploy\win-x64\S-ARGAME_Editor.dll

# Copy the deploy (exe and dll) also to OneDrive build folder (for devteam testing)
xcopy deploy\win-x64\S-ARGAME_Editor.exe "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_latest" /Y
xcopy deploy\win-x64\*.dll "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_latest" /Y
xcopy deploy\win-x64\version.txt "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_latest" /Y

# Create the installer (in D:\OneDrive\_DEPLOY\s-argame\editor)
iscc.exe S-ARGAME_Editor.iss

# Copy the changelogs to _DEPLOY\s-argame\editor
xcopy installer\changelogs "D:\OneDrive\_DEPLOY\s-argame\win_x64\editor\changelogs\" /Y

d:



