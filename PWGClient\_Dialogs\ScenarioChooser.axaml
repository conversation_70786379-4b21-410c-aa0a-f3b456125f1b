<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 Width="{Binding DialogBigSize.Width}" Height="{Binding DialogBigSize.Height}"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.ScenarioChooser" >

	<dialogHost:DialogHost Identifier="scenariochooser_dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True">
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

		<Border BorderBrush="White" BorderThickness="1" Background="{DynamicResource ExpanderBackground}">
			<Grid RowDefinitions="Auto,*,Auto">
				<StackPanel Grid.Row="0"  Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
					<materialIcons:MaterialIcon Width="30" Height="30" Kind="DrawingBox" />
					<Label  Content="Choose a Scenario" FontSize="14" FontWeight="Bold" Margin="10" HorizontalAlignment="Center"/>
				</StackPanel>
				
				<Grid Grid.Row="1" Margin="10">
					<ScrollViewer>
						<ItemsControl ItemsSource="{Binding SARGAME.Scenarios}">

							<ItemsControl.ItemsPanel>
								<ItemsPanelTemplate>
									<WrapPanel/>
								</ItemsPanelTemplate>
							</ItemsControl.ItemsPanel>

							<ItemsControl.ItemTemplate>
								<DataTemplate DataType="{x:Type sargame:ScenarioEntry}">
									<Grid RowDefinitions="Auto,Auto" Margin="5">
										<Button Grid.Row="0" Click="bt_choose_Click" Width="256" Height="144">
											<Grid>
												<Image   Source="{Binding ThumbnailPath, Converter={StaticResource BitmapValueConverter}}" Margin="5" RenderOptions.BitmapInterpolationMode="HighQuality"/>
											</Grid>
										</Button>
										<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="5" HorizontalAlignment="Stretch">
											<TextBox Grid.Column="0" Text="{Binding Name}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="0,2,0,0"/>
											<Button Grid.Column="1" Classes="Accent" Content="{materialIcons:MaterialIconExt Kind=DeleteOutline}" VerticalAlignment="Center" Margin="2" Padding="0" Click="bt_delete_Click"/>
										</Grid>
									</Grid>
								</DataTemplate>
							</ItemsControl.ItemTemplate>

						</ItemsControl> 
					</ScrollViewer>
				</Grid>

				<StackPanel  Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
					<Button Margin="10" Classes="Rounded" HorizontalAlignment="Center" Click="bt_create_new_Click">
						<TextBlock Text="Create new Scenario"/>
					</Button>
					<Button Margin="10" Classes="Rounded" HorizontalAlignment="Center" Click="bt_cancel_Click">
						<TextBlock Text="Cancel"/>
					</Button>
				</StackPanel>
			</Grid>

		</Border>
	</dialogHost:DialogHost>
</UserControl>
