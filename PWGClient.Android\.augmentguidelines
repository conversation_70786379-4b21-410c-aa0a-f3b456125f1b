This is the Editor GUI application for S-ARGAME platform.
The Editor is used to start individual S-ARGAME Unity applications that are compiled and installed independently.
The Editor and the application will then communicate through a network protocol (based on KCP).

The "generated" external folder added as external context contains auto generated code, views, wrapper classes that the Editor uses.
So the Editor and the applications must have the same "protocol" (which is the SARGAME.Version field) to be compatible.