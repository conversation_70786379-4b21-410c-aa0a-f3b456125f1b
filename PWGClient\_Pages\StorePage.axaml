<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.StorePage">
	<Border Classes="Card">
		<Grid RowDefinitions="Auto,*">

		
			<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto">
				<TextBlock Grid.Row="0" Text="Packages" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
				<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
				<TextBlock Grid.Row="2" Margin="5,10,0,10">	
					Packages are installable bundles that contain <Bold>games</Bold>, <Bold>apps</Bold> and <Bold>experiences</Bold>.
				</TextBlock>
			</Grid>
			

			<ScrollViewer Grid.Row="1" Margin="0,10,0,0">
				<ItemsControl ItemsSource="{Binding SARGAME.CurrentStore.packages}">

					<ItemsControl.ItemsPanel>
						<ItemsPanelTemplate>
							<WrapPanel/>
						</ItemsPanelTemplate>
					</ItemsControl.ItemsPanel>
						
					<ItemsControl.ItemTemplate>
						<DataTemplate DataType="{x:Type sargame:StorePackage}">
							<Border Classes="Card" Margin="10" >
								<Grid RowDefinitions="Auto,Auto">
									<Button  Grid.Row="0" BorderThickness="0" BorderBrush="Transparent" Width="256" Height="144" Margin="10" Padding="0" Click="bt_download_Click">
										<Grid>
											<Image  Source="{Binding MediaPath, Converter={StaticResource BitmapValueConverter}}" RenderOptions.BitmapInterpolationMode="HighQuality" />
										</Grid>
									</Button>
									<Grid ColumnDefinitions="Auto,Auto" Grid.Row="1" Margin="10" HorizontalAlignment="Stretch">
										<StackPanel Grid.Column="0" Orientation="Vertical" Margin="10">
											<Label  Content="{Binding name}" FontWeight="Bold" VerticalAlignment="Center" />
											<Label  Content="{Binding Version}" Foreground="LightGray" FontWeight="ExtraLight" VerticalAlignment="Center"/>
										</StackPanel>
										<StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Stretch" Margin="5">
											<Button IsVisible="{Binding !IsInstalled}" Classes="Primary" Content="{materialIcons:MaterialIconExt Kind=CloudDownload}" ToolTip.Tip="Download and Install" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="10" Click="bt_download_Click"/>
											<Button IsVisible="{Binding IsUpdateAvailable}" Classes="Primary" Content="{materialIcons:MaterialIconExt Kind=CloudRefreshVariant}" ToolTip.Tip="Update" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="10" Click="bt_download_Click"/>
											<Button IsVisible="{Binding IsInstalled}" Content="{materialIcons:MaterialIconExt Kind=DeleteForever}"   ToolTip.Tip="Remove" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="10" Click="bt_remove_Click"/>
										</StackPanel>
									
									</Grid>
								</Grid>
							</Border>
						</DataTemplate>
					</ItemsControl.ItemTemplate>
					
				</ItemsControl>
			</ScrollViewer>
		</Grid>
	</Border>
</UserControl>
