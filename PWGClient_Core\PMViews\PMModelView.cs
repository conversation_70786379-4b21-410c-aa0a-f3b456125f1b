﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

// This is the view for the whole Model

namespace Tabula.PMCore
{
    public class PMModelView : PMView<Tabula.PMCore.Model, Tabula.PMCore.ModelView>
    {
        public PMModelView(Tabula.SharedObjectMap.GuidObject model) : base(model) { }        

        public override void CreateVisual()
        {
            // NO Visual

            return;
        }

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            if (from_server)
            {
                Debug.WriteLine($"PMModelVIew.OnUpdate() fieldname={fieldname}");

                switch (fieldname)
                {
                    case nameof(PMCore.Model.ModuleFocus):

                        SARGAME.iMainWindowUI.RaisePropertyChanged("IsApplicationInFocus");
						Debug.WriteLine($"ModuleFocus={Model.ModuleFocus}");

						break;
                }

            }

            

            return true;
        }       
    }
}
