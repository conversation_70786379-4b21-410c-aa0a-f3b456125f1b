{"packages": [{"name": "Starter Pack", "id": "starterpack", "description": "Starter Pack games: Walleroids, Ponkanoid, Neon Raiders, Room Raiders, TableRace", "license_feature": "starterpack", "media_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/starterpack/media.png", "appcast_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/starterpack/appcast.xml", "modules": [{"id": "calibrator", "name": "Calibrator", "license_feature": null}, {"id": "walleroids", "name": "Walleroids", "license_feature": "starterpack/walleroids"}, {"id": "ponkanoid", "name": "Ponkanoid", "license_feature": "starterpack/ponkanoid"}, {"id": "neonraiders", "name": "NeonRaiders", "license_feature": "starterpack/neonraiders"}, {"id": "tablerace", "name": "TableRace", "license_feature": "starterpack/tablerace"}]}, {"name": "Halloween XP", "id": "halloweenxp", "description": "Halloween XP: PumpShoot", "license_feature": "halloweenxp", "media_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/halloweenxp/media.png", "appcast_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/halloweenxp/appcast.xml", "modules": [{"id": "pumpshoot", "name": "PumpShoot", "license_feature": "halloweenxp/pumpshoot"}]}, {"name": "Holiday XP", "id": "holidayxp", "description": "Holiday XP: Snow<PERSON><PERSON><PERSON><PERSON>", "license_feature": "holidayxp", "media_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/holidayxp/media.png", "appcast_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/holidayxp/appcast.xml", "modules": [{"id": "snowballfrenzy", "name": "Snowball Frenzy", "license_feature": "holidayxp/snowballfrenzy"}, {"id": "holidayxp1", "name": "Holiday XP", "license_feature": "holidayxp/holidayxp1"}]}, {"name": "Kinetik Pack", "id": "kinetikpack", "description": "Kinetik Pack: KinetikPlayground", "license_feature": "kinetikpack", "media_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/kinetikpack/media.png", "appcast_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/kinetikpack/appcast.xml", "modules": [{"id": "kinetikplayground", "name": "Kinetik Playground", "license_feature": "kinetikpack/kinetikplayground"}]}, {"name": "80s Pack", "id": "80spack", "description": "80s Pack: SnakePit", "license_feature": "80pack", "media_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/80spack/media.png", "appcast_url": "https://sargame-cdn.tabulatouch.com/macos_x64/packages/80spack/appcast.xml", "modules": [{"id": "snakepit", "name": "SnakePit", "license_feature": "80pack/snakepit"}, {"id": "bubblefi", "name": "Bubble-Fi", "license_feature": "80pack/bubblefi"}, {"id": "superpop", "name": "SuperPop", "license_feature": "80pack/superpop"}, {"id": "footwall", "name": "FootWall", "license_feature": "80pack/footwall"}]}]}