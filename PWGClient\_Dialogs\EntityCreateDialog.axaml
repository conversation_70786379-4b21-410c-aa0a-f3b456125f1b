<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME;assembly=PWGClient_Core"
			 xmlns:pmcore="clr-namespace:Tabula.PMCore;assembly=PWGClient_Core"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 Width="{Binding DialogBigSize.Width}" Height="{Binding DialogBigSize.Height}"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.EntityCreateDialog" >

	<dialogHost:DialogHost Identifier="entitycreate_dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True">
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

		<Border BorderBrush="White" BorderThickness="1" Background="{DynamicResource ExpanderBackground}">
			<Grid RowDefinitions="Auto,*,Auto">
				<StackPanel Grid.Row="0"  Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
					<materialIcons:MaterialIcon Width="30" Height="30" Kind="DrawingBox" />
					<Label  Content="Add Object" FontSize="14" FontWeight="Bold" Margin="10" HorizontalAlignment="Center"/>
				</StackPanel>

				<Button HorizontalAlignment="Right" VerticalAlignment="Top" Margin="10" Padding="5" ToolTip.Tip="Close"
						Click="bt_closedialog_click">
					<materialIcons:MaterialIcon Kind="Close" Classes="Basic" Width="30" Height="30"/>
				</Button>

				<TabControl Grid.Row="1" Margin="10" 
						Name="entitycreate_tab"
						Padding="0,10,10,0"
						Focusable="False" KeyboardNavigation.TabNavigation="None"
						Background="{DynamicResource ExpanderBackground}"
							TabIndex="1">


					<!-- Recent Objects -->
					<TabItem Name="tab_objects_recents" IsVisible="{Binding HasRecentObjects}" Header="Recent Objects" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_ObjectsRecent}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="60"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
												<!--
												<Button Grid.Column="1" Classes="Accent" Content="{materialIcons:MaterialIconExt Kind=DeleteOutline}" VerticalAlignment="Center" Margin="2" Padding="0" Click="bt_delete_Click"/>
												-->
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>
					
					<!-- Objects -->
					<TabItem Name="tab_objects" Header="Objects" IsVisible="{Binding EntitiesToCreate_Objects, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_Objects}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}" 
																	  Width="60"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>		
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
												<!--
												<Button Grid.Column="1" Classes="Accent" Content="{materialIcons:MaterialIconExt Kind=DeleteOutline}" VerticalAlignment="Center" Margin="2" Padding="0" Click="bt_delete_Click"/>
												-->
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<!-- Characters -->
					<TabItem Name="tab_characters" Header="Characters" IsVisible="{Binding EntitiesToCreate_Characters, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_Characters}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="60"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
												<!--
												<Button Grid.Column="1" Classes="Accent" Content="{materialIcons:MaterialIconExt Kind=DeleteOutline}" VerticalAlignment="Center" Margin="2" Padding="0" Click="bt_delete_Click"/>
												-->
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<!-- Effects -->
					<TabItem Name="tab_effects" Header="Effects" IsVisible="{Binding EntitiesToCreate_Effects, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_Effects}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="60"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<!-- Media -->
					<TabItem Name="tab_media" Header="Media" IsVisible="{Binding EntitiesToCreate_Media, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_Media}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="100" Height="100">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="60" 
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<!-- Backgrounds -->
					<TabItem Name="tab_backgrounds" Header="Backgrounds" IsVisible="{Binding EntitiesToCreate_Backgrounds, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_Backgrounds}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="256" Height="144">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="200" Height="112"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>

					<!-- Scene Effects -->
					<TabItem Name="tab_scene_effects" Header="Scene Effects" IsVisible="{Binding EntitiesToCreate_SceneEffects, Converter={StaticResource CollectionHasElementsConverter}}" HorizontalContentAlignment="Right" Focusable="False" KeyboardNavigation.TabNavigation="None">
						<ScrollViewer>
							<ItemsControl ItemsSource="{Binding EntitiesToCreate_SceneEffects}">

								<ItemsControl.ItemsPanel>
									<ItemsPanelTemplate>
										<WrapPanel/>
									</ItemsPanelTemplate>
								</ItemsControl.ItemsPanel>

								<ItemsControl.ItemTemplate>
									<DataTemplate DataType="{x:Type pmcore:EntityCreate}">
										<Grid RowDefinitions="Auto,Auto" Margin="5">
											<Button Grid.Row="0" Click="bt_choose_Click" Width="256" Height="144">
												<Grid>
													<local:UserBitmap Path="{Binding Icon}"
																	  Width="200" Height="112"
																	  HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
												</Grid>
											</Button>
											<Grid ColumnDefinitions="*,Auto" Grid.Row="1" Margin="0" HorizontalAlignment="Stretch">
												<TextBlock Grid.Column="0" Text="{Binding NameDesc}" Classes="BottomBar" VerticalAlignment="Center" HorizontalAlignment="Stretch" Margin="20,2,0,0"/>
											</Grid>
										</Grid>
									</DataTemplate>
								</ItemsControl.ItemTemplate>

							</ItemsControl>
						</ScrollViewer>
					</TabItem>
				</TabControl>
				
				
			</Grid>

		</Border>
	</dialogHost:DialogHost>

	<!-- OLD from EntityCreateView -->
	<!-- Group and Create -->

	<!--
	<Expander Grid.Row="1" Header="Create" ExpandDirection="Down" IsExpanded="True" HorizontalAlignment="Stretch" IsVisible="{Binding CanCreateObjects}">
		<ItemsControl ItemsSource="{Binding create}" Background="{DynamicResource ExpanderBackground}">

			<ItemsControl.ItemsPanel>
				<ItemsPanelTemplate>
					<StackPanel Orientation="Vertical"/>
				</ItemsPanelTemplate>
			</ItemsControl.ItemsPanel>

			<ItemsControl.ItemTemplate>
				<DataTemplate DataType="{x:Type pmcore:EntityCreateView}">
					<Grid ColumnDefinitions="64,*" Height="70">


						<Button x:Name="bt_entity_create" Grid.Column="0" Width="64" Height="64" HorizontalAlignment="Center" >
							<i:Interaction.Behaviors>
								<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding #bt_entity_create}">
									<ia:CallMethodAction TargetObject="{Binding}" MethodName="Create"/>
								</ia:EventTriggerBehavior>
							</i:Interaction.Behaviors>
							<local:UserBitmap Path="{Binding Icon}" Width="30" Height="30" HorizontalAlignment="Center" HorizontalContentAlignment="Center" VerticalAlignment="Center" VerticalContentAlignment="Center"/>		
						</Button>

						<TextBlock Grid.Column="1" Text="{Binding template}" HorizontalAlignment="Left" VerticalAlignment="Center"/>
					</Grid>
				</DataTemplate>
			</ItemsControl.ItemTemplate>

		</ItemsControl>
	</Expander>
	-->
</UserControl>
