using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using Tabula.Licensing.LicenseActivator;
using Sentry;
using System.Text.RegularExpressions;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class SupportPage : UserControl
	{
		public SupportPage()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{

			base.OnLoaded(e);
		}

		async void bt_send_request(object sender, RoutedEventArgs args)
		{
			// check email
			bool email_valid = Regex.IsMatch(MainWindowUI.Instance.SupportRequestEmail, @"\A(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)\Z", RegexOptions.IgnoreCase);

			if (!email_valid)
			{
				await ProgressDialog.ShowMessageAsync("Please enter a valid email");
				return;
			}

			if (string.IsNullOrEmpty(MainWindowUI.Instance.SupportRequestMessage))
			{
				await ProgressDialog.ShowMessageAsync("Please enter a message");
				return;
			}

			var cts = new CancellationTokenSource();

			var msgbox = ProgressDialog.ShowMessage(
				message: "Sending request...",
				progress: true,
				cancel_button_text: "Cancel",
				cancellation_token: cts.Token,
				on_cancel_button_click: () => cts.Cancel()
				);

			bool request_ret = false;

			try
			{
				await Task.Run(async () =>
				{
					request_ret = LicenseActivatorLib.SupportRequest(
						serial: MainWindowUI.Instance.LicenseSerial,
						email: MainWindowUI.Instance.SupportRequestEmail,
						message: MainWindowUI.Instance.SupportRequestMessage,
						version: MainWindowUI.Instance.Version);

				}, cts.Token);
			}
			catch (OperationCanceledException)
			{
				MainWindowUI.Instance.IsBusy = false;
				msgbox.Close();
				return;
			}
			catch (Exception ex)
			{
				msgbox.Close();

				await ProgressDialog.ShowMessageAsync("Exception sending request");
				SARGAME.App.logException("Support Request", ex);
			}

			msgbox.Close();

			await ProgressDialog.ShowMessageAsync("Your request has been sent!");

			// Save the support request in prefs?
			SARGAME.Instance.SaveEditorApplicationSettings();
			
			MainWindowUI.Instance.SupportRequestMessage = "";
		}
	}

	

}
