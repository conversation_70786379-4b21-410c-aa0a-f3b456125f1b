using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Rendering.SceneGraph;
using Avalonia.Skia;
using Avalonia.Threading;
using ReactiveUI;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Tabula;
using Tabula.PMCore;
using Tabula.SKRenderGraph;
using Tabula.AvaloniaUtils;
using System.Reflection;
using System.IO;
using System.Linq;

namespace Tabula.PWGClient
{
    // NOTE: Scene will be populated in App.axaml.cs, when model is available

    public partial class ImageCalibrationEditor : SKSceneUserControl
    {
        public static ImageCalibrationEditor Instance;

        public override bool IsSceneVisible => true; // always embedded in calibratioin window

        private List<SKSprite> CalibPoints = new List<SKSprite>();
        private SKSprite CalibSprite;

        public ImageCalibrationEditor()
        {
            Instance = this;
        }
       
        private void InitializeComponent()
        { 
            AvaloniaXamlLoader.Load(this);

            Dispatcher.UIThread.InvokeAsync(async () => await ConfigureScene());
        }       

        public void Unloaded(object sender, EventArgs args)
		{
            // DOESNOTWORK
		}

        public async override Task ConfigureScene()
        {
            SKImage image = null; // await App.Instance.GetCalibrationImage();

            if (image == null)
			{
                // TODO: error
                return;
			}

            CalibSprite = new SKSprite(Scene, new SKRect(0, 0, image.Width, image.Height))
            {
                Bitmap = image,
                IsHitTestVisible = false
            };

            // The image is a sprite
            Scene.Add(CalibSprite);

            // TODO: center and scale view so that image is in center

            // Check if image points are all set to 0
            var imagecalib_not_initialized = App.Client.Model.Screen.image_markers.Where(c => c.position.x == 0 && c.position.y == 0).Count() == 4;

            // Add 4 calibration points, not model-bound
            for (int i=0; i<4; i++)
			{
                var cpoint = SKSprite.CreateFromEmbeddedResource(Scene, new SKRect(0, 0, 100, 100),
                    $"Tabula.PMCore.Images.embedded.calibration_point_{i+1}.png");

                CalibPoints.Add(cpoint);
                Scene.Add(cpoint);
                SKPoint pos = new SKPoint(0,0);

                if (imagecalib_not_initialized)
                {
                    switch (i)
                    {
                        case 0: pos = new SKPoint(0, 0); break;
                        case 1: pos = new SKPoint(image.Width, 0); break;
                        case 2: pos = new SKPoint(image.Width, image.Height); break;
                        case 3: pos = new SKPoint(0, image.Height); break;
                    }
                }
                else
				{
                    var calib_points = App.Client.Model.Screen.image_markers;
                    pos = new SKPoint(calib_points[i].position.x, calib_points[i].position.y);
                }

                cpoint.SetPosition(pos);
            }

            Scene.CenterAndFit();
        }

        public List<SKPoint> GetCalibrationPoints()
		    => (from c in CalibPoints select c.GetPosition()).ToList();

        public async Task RefreshCalibrationImageIfChanged()
		{
            /*
            if (await App.Instance.IsCalibrationImageChanged())
			{
                var image = await App.Instance.GetCalibrationImage();
                CalibSprite.Bitmap = image;
			}
            */
		}
		
    }
}
