﻿using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Interop;

namespace Tabula.SKRenderGraph
{
    public class SKSceneWPF : SKScene
    {
        public FrameworkElement Visual { get; private set; }

        public int KeyboardPanButton = 0x11; // control

        private IntPtr VisualHWND;

        private double DpiScaleX=1, DpiScaleY=1;

        public SKSceneWPF(FrameworkElement visual, double dpi_scale_x, double dpi_scale_y)
        {
            Visual = visual;
            DpiScaleX = dpi_scale_x;
            DpiScaleY = dpi_scale_y;

            HwndSource source = (HwndSource) HwndSource.FromVisual(Visual);
            VisualHWND = source.Handle;
        }

        private bool _lmouse_outside = false;
        public override void GetMouseState()
        {            
            // Process input, using native mouse coordinate (faster and independent)
            // BUT we need to process "focus" on the window, also when dragging corners
            var mpos = GetClientCursorPosition();

            MousePosition.X = (float) (mpos.X /* * DpiScaleX */);
            MousePosition.Y = (float) (mpos.Y /* * DpiScaleY */);

            if (MousePosition.X >=0 && MousePosition.X <= CanvasSize.Width 
                && MousePosition.Y >=0 && MousePosition.Y <= CanvasSize.Height)
            {
                //Debug.WriteLine($"{MousePosition.X} {MousePosition.Y}");

                // Get keys
                /*
                var lb = GetAsyncKeyState(Keys.LButton) != 0;
                if (!lb)
                    _lmouse_outside = false;
                MouseLeftPressed = (!_lmouse_outside && lb);
                */

                MouseLeftPressed = GetAsyncKeyState(Keys.LButton) != 0;
                MouseMiddlePressed = GetAsyncKeyState(Keys.MButton) != 0;
                MouseRightPressed = GetAsyncKeyState(Keys.RButton) != 0;

                KeyboardPanPressed = GetAsyncKeyState((Keys) KeyboardPanButton) != 0;
                Debug.WriteLine(GetAsyncKeyState((Keys) KeyboardPanButton));
            }
            else
            {
                // mouse is outside window, if LeftButton is already pressed we should not allow a dragging operation
                //_lmouse_outside = GetAsyncKeyState(Keys.LButton) != 0;
                MouseLeftPressed = false;
                MouseMiddlePressed = false;
                MouseRightPressed = false;
                KeyboardPanPressed = false;
            }


                       
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int X;
            public int Y;

            public static implicit operator Point(POINT point)
            {
                return new Point(point.X, point.Y);
            }
        }

        /// <see>See MSDN documentation for further information.</see>
        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        private Point GetCursorPosition()
        {
            POINT lpPoint;
            GetCursorPos(out lpPoint);
            return lpPoint;
        }

        private Point GetClientCursorPosition()
        {
            POINT lpPoint;
            GetCursorPos(out lpPoint);

            ScreenToClient(VisualHWND, ref lpPoint);
            
            return lpPoint;
        }

        public const uint WM_MOUSEWHEEL = 0x020A;

        public const uint MOUSEEVENTF_ABSOLUTE = 0x8000;
        public const uint MOUSEEVENTF_LEFTDOWN = 0x0002;
        public const uint MOUSEEVENTF_LEFTUP = 0x0004;
        public const uint MOUSEEVENTF_MIDDLEDOWN = 0x0020;
        public const uint MOUSEEVENTF_MIDDLEUP = 0x0040;
        public const uint MOUSEEVENTF_MOVE = 0x0001;
        public const uint MOUSEEVENTF_RIGHTDOWN = 0x0008;
        public const uint MOUSEEVENTF_RIGHTUP = 0x0010;
        public const uint MOUSEEVENTF_XDOWN = 0x0080;
        public const uint MOUSEEVENTF_XUP = 0x0100;
        public const uint MOUSEEVENTF_WHEEL = 0x0800;
        public const uint MOUSEEVENTF_HWHEEL = 0x01000;

        [DllImport("user32.dll")]
        public static extern short GetAsyncKeyState(System.Windows.Forms.Keys vKey);

        [DllImport("user32.dll")]
        static extern bool ScreenToClient(IntPtr hWnd, ref POINT lpPoint);
    }
}
