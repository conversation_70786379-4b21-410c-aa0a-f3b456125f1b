<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.LicensePage">
	<Border Classes="Card" >
		<Grid RowDefinitions="Auto,Auto,Auto">
			<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto,Auto">
				<TextBlock Grid.Row="0" Text="License" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
				<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />

				<TextBlock Grid.Row="2" Classes="h5" Margin="10">
					Please enter the <Bold>license serial</Bold> to activate the software or activate a <Bold>Trial</Bold>.
				</TextBlock>
				
				<TextBox Grid.Row="3" Classes="BottomBar" Margin="0,10,0,0"
						 Text="{Binding EditedLicenseSerial}"
						 PasswordChar="{Binding EditedLicenseSerialPasswordChar}"
						 IsReadOnly="{Binding !CanEditLicenseSerial}"
						 Background="{Binding LicenseColor}"/>
			</Grid>

			<!--
			<suki:GroupBox Grid.Row="0" Margin="10">
				<suki:GroupBox.Header>
					<TextBlock Text="License" Foreground="White"/>
				</suki:GroupBox.Header>
				<TextBox Classes="BottomBar" Text="{Binding EditedLicenseSerial}"
						 PasswordChar="{Binding EditedLicenseSerialPasswordChar}"
						 IsReadOnly="{Binding !CanEditLicenseSerial}"
						 Background="{Binding LicenseColor}"/>
			</suki:GroupBox>
			-->
			
			<StackPanel Grid.Row="1" Margin="10" Orientation="Horizontal" HorizontalAlignment="Stretch">
				<Button Classes="Rounded" ToolTip.Tip="Activates a license on the current machine. The license will be linked to this hardware." VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10" Click="bt_activate_Click"
						IsVisible="{Binding CanActivateLicense}">
					<TextBlock Text="Activate License"/>
				</Button>
				<Button Classes="Rounded"  ToolTip.Tip="Deactivates the license. The license serial code is free to be activated on another hardware." VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10" Click="bt_deactivate_Click"
						IsVisible="{Binding CanDeactivateLicense}">
					<TextBlock Text="Deactivate License"/>
				</Button>

				<Button Classes="Primary Rounded"  ToolTip.Tip="Updates the license with new features if available." VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10" Click="bt_update_Click"
						IsVisible="{Binding CanUpdateLicense}">
					<TextBlock Text="Update License"/>
				</Button>
				<Button Classes="Primary Rounded"  ToolTip.Tip="Activates a Trial License to test the software." VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10" Click="bt_activate_trial_Click"
						IsVisible="{Binding CanActivateTrial}">
					<TextBlock Text="Activate Trial"/>
				</Button>
			</StackPanel>
			<Border Grid.Row="2" Classes="Card" HorizontalAlignment="Stretch" Background="DarkBlue">
				<TextBlock Text="{Binding LicenseDescription}" Margin="20" HorizontalAlignment="Left"  TextWrapping="Wrap"/>
			</Border>
		</Grid>
	</Border>
	
</UserControl>
