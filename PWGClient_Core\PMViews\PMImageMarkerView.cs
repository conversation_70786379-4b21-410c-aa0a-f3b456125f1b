﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

namespace Tabula.PMCore
{
    public class PMImageMarkerView : PMView<Tabula.PMCore.ImageMarker, Tabula.PMCore.ImageMarkerView>
    {
        public PMImageMarkerView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }      
        
        public string Name
        {
            get
            {
                if (View != null)
                    return $"Detected Marker #{View.Root.Model.Screen.image_markers.IndexOf(View.Model)}";
                else
                    return "Detected Marker";
            }
        }

        public override void CreateVisual()
        {
			/*
            int index = (Model.__Parent as Screen).calibration_points.IndexOf(Model) + 1;

            Visual = SKSprite.CreateFromEmbeddedResource(Scene, new SKRect(0, 0, 100, 100), 
                $"Tabula.PMCore.Images.embedded.calibration_point_{index}.png");
            */

			// NOTE: now a single image is OK because the order of markers is always sorted on the server side
			// TODO: rear projection?

			Visual = SKSprite.CreateFromEmbeddedResource(Scene, new SKRect(0, 0, 100, 100),
			   $"Tabula.PMCore.Images.embedded.image_marker.png");

			Visual.onMove += (o, pos) =>
            {
                var center = Visual.GetPosition();

                View.CommitBegin();
                View.position.x = center.X;
                View.position.y = center.Y;
                View.CommitEnd();
            };            

            base.CreateVisual();
        }

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            var v2 = View.Model.position;
            Visual.SetPosition(new SKPoint(v2.x, v2.y));

            return true;
        }

		public override void BeforeVisualUpdate()
		{
            // Draw only if enabled
            Visual.IsDrawnInScene = View.Model.enabled;
			Visual.IsHitTestVisible = View.Model.enabled;

			base.BeforeVisualUpdate();
		}

        // Invoked from UI
        public async void Delete()
        {
			// never less than 4 markers
			var enabled_image_markers = (from i in SARGAME.View.Screen.image_markers where i.enabled select i);

            if ((enabled_image_markers.Count() - 1) < 4)
            {
				await SARGAME.iMainWindowUI.ShowMessageAsync("At least 4 markers must be detected.");
                return;
			}

            View.enabled = false;

            Scene.UnSelectAllObjects();
			SARGAME.iMainWindowUI.SelectedItem = null;
		}
	}
}
