<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:local="clr-namespace:Tabula.PWGClient"
        xmlns:pmcore="clr-namespace:Tabula.PMCore"
		xmlns:dialogHost="clr-namespace:DialogHost;assembly=DialogHost.Avalonia"
        xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
        xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
        mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="200"
        x:Class="Tabula.PWGClient.MessageBox"
		Width="300" Height="200" WindowStartupLocation="CenterOwner"
		ExtendClientAreaToDecorationsHint="True"
        ExtendClientAreaChromeHints="NoChrome"
        ExtendClientAreaTitleBarHeightHint="-1"
        Title="MessageBox">
	<Grid Margin="1" RowDefinitions="*,Auto" Background="{StaticResource SukiBackground}">
		<Label Content="test" VerticalAlignment="Center" HorizontalAlignment="Center"/>
		<Button Grid.Row="1">
			<TextBlock Text="OK"/>
		</Button>
	</Grid>
</Window>
