﻿//TABULA_GUID:{B6291548-A05D-4390-8331-E7A1149FED96}
using System;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Threading;

namespace Tabula.SharedObjectMap
{
    public static class Dispatcher
    {
        private static Avalonia.Threading.Dispatcher dispatcher;

        private static int dispatcher_thread_id = 0;

        public static void Set(Avalonia.Threading.Dispatcher _dispatcher, int _dispatcher_thread_id)
        {
            dispatcher = _dispatcher;
            dispatcher_thread_id = _dispatcher_thread_id;
        }

		public static void Dispatch(Action action)
		{
            if (Thread.CurrentThread.ManagedThreadId == dispatcher_thread_id || dispatcher==null)
				action.Invoke();
            else
				dispatcher.Invoke(action);
		}

		public static async Task DispatchAsync(Action action)
        {
            if (Thread.CurrentThread.ManagedThreadId == dispatcher_thread_id || dispatcher == null)
                action.Invoke();
            else
                await dispatcher.InvokeAsync(action);
        } 
    }
}

