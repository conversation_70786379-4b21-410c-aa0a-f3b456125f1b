c:
cd "C:\G\Codice\TABULA.PMCORE\PWGClient_xplat"

REM Main dotnet compile, as a single file (only some DYLIBs are external)

dotnet publish PWGClient.Desktop -c Release /p:PublishProfile=PWGClient.Desktop\Properties\PublishProfiles\MacOS.pubxml

REM Protect (no necrobit would be OK on Mac, but randomly somehting else breaks notarization, AntiDebug and Obfuscation is enabled worked once...)
REM SUSPENDED for now
REM REM "C:\Program Files (x86)\Eziriz\.NET Reactor\dotNET_Reactor.Console.exe" -project "Reactor_S-ARGAME_Editor_dotnet_mac.nrproj"

REM (Not Protect) Just copy the compiled executable do deploy
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\S-ARGAME_Editor deploy\osx-x64\ /Y

REM Copy important DYLIBs not in the single file

xcopy PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\*.dylib deploy\osx-x64\ /Y

REM Remove unwanted files

REM REM del deploy\S-ARGAME_Editor.dll

REM Copy the deploy (executable and dylibs) also to OneDrive Mac build folder (for later Mac packaging)

xcopy deploy\osx-x64\S-ARGAME_Editor "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest" /Y
xcopy deploy\osx-x64\*.dylib "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest" /Y

REM Copy the changelogs to _DEPLOY\s-argame\editor

xcopy installer\changelogs "D:\OneDrive\_DEPLOY\s-argame\macos_x64\editor\changelogs\" /Y

REM  Generate NetSparkle appcast in _DEPLOY
REM TODO: This must be done after the packaging on the Mac

REM REM netsparkle.exe -a "D:\OneDrive\_DEPLOY\s-argame\macos_x64\editor" -e exe -b "D:\OneDrive\_DEPLOY\s-argame\macos_x64\editor" -n "S-ARGAME Editor" -u "https://sargame-cdn.tabulatouch.com/macos_x64/editor" -p "D:\OneDrive\_DEPLOY\s-argame\macos_x64\editor\changelogs" -l "https://sargame-cdn.tabulatouch.com/macos_x64/editor/changelogs" --overwrite-old-items --human-readable

REM RSync to KeyCDN using WSL and private key (PLEASE DO MANUALLY ON THE WHOLE FOLDER)




