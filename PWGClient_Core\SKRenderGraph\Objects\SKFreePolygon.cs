﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using DynamicData.Tests;
using SkiaSharp;
using Tabula;
using Tabula.PMCore;

// This is a free editable polygon with multiple segments
// New vertexes can be added on click on contour, or removed with rightclick

// 2nd version: The center is in a Pivot point, and vertices are relative to that point

namespace Tabula.SKRenderGraph
{   
    public class SKFreePolygon : SKObject
    {
        public SKColor ColorError { get; set; } = SKColors.Red;

		public bool CanAddRemoveVertices = true;                // adds/remove vertices on segment contours

		public bool MustBeConvex { get; set; } = false;

        public bool MustBeValid { get; set; } = false;

        private bool IsInError = false;

        public enum SegmentType
        {
            Line,
            Cubic,
            Quad,
            Conic,
            Arc
        }

        private SKRect _bounds = SKRect.Empty;
        // abstract

        // NOTE: Segments take the origin for granted (MoveTo) and specify end point, type and control points
        public abstract class Segment
        {
            // Every segment is defined between two vertexes, but controls are only for the END vertex
            public int start_idx, end_idx;  // indexes in parent's vertices

            public SKFreePolygon Parent;
            public SegmentType Type = SegmentType.Line;

            public SKObject Contour;
            public List<Segment.SegmentControlPoint> ControlPoints = new List<Segment.SegmentControlPoint>();

            // standard contour
            public class SegmentContour : SKContour
            {
                private Segment Segment;

                public SegmentContour(SKScene scene, Segment ls = null) : base(scene)
                {
                    Size = 10;
                    Color = SKColors.Transparent;   // so it is always drawn, and hitcheck works
                    if (ls != null)
                        Segment = ls;
                }

                public override void Update()
                {
                    Path.Reset();
                        Path.MoveTo(Segment.GetPoint(Segment.start_idx));
                        Segment.AddToPath(Path);
                        Path.MoveTo(Segment.GetPoint(Segment.start_idx));
                    Path.Close();                    

                    if (Segment.Parent.CanAddRemoveVertices && Segment.Parent.IsSelected)
                    {
                        // detect left click on contour 
                        if (IsMouseLeftClicked)
                        {
                            Segment.Parent.OnVertexAdd?.Invoke(Segment.end_idx, Scene.MousePosition);
                        }
                    }

                    base.Update();
                }
            }

            public class SegmentControlPoint : SKControlPoint
            {
                private Segment Segment;

                public SegmentControlPoint(SKScene scene, Segment ls) : base(scene) 
                {
                    Segment = ls;
                }

                public override void Update()
                {
                    SKFreePolygon parent_polygon = (Parent as SKFreePolygon);

					if (parent_polygon.CanAddRemoveVertices && parent_polygon.IsSelected)
                    {
                        if (IsMouseRightClicked)
                        {
							parent_polygon.OnVertexRemove?.Invoke(Segment.end_idx);
                        }
                    }

                    base.Update();
                }

				public override void Destroy()
				{
                    // Ask the parent to remove the vertex
                    (Parent as SKFreePolygon).OnVertexRemove?.Invoke(Segment.end_idx);
                }
			}

            public Segment(SKFreePolygon parent, int num_control_points)
            {
                Parent = parent;

                // start and end index will be set later by parent's logic

                // main Control point is just one per segment, on the end
                for (int i = 0; i < num_control_points; i++)
                {
                    var cp = new SegmentControlPoint(Parent.Scene, this);
                    cp.IsEnabled = false;
                    ControlPoints.Add(cp);                    
                    Parent.Scene.Add(cp, Parent);
                }

                var last_cp = ControlPoints.Last();

				last_cp.onDragStart += (s) => parent?.OnVertexDragStart?.Invoke(last_cp);
				last_cp.onDragEnd += (s) => parent?.OnVertexDragEnd?.Invoke(last_cp);

				last_cp.onMove += (s, pos_rel) => parent?.OnVertexMove?.Invoke(last_cp, end_idx, pos_rel);

                Contour = new SegmentContour(Parent.Scene, this);
                Parent.Scene.Add(Contour, Parent); 
            }

            public SKPoint GetPoint(int idx) => Parent.GetVertexPosition(idx);
            public void SetPoint(SKPoint pos, int idx) => Parent.SetVertexPosition(idx,pos);

            // anchors initialization
            public virtual void Initialize() { }

            public virtual void AddToPath(SKPath path) { }

            public virtual void SyncFromControlPoints() { }

            public virtual void SyncToControlPoints() { }

            // this happens in Draw() after shape has been drawn
            public virtual void LastDraw(SKCanvas canvas, SKPaint Paint) { }            
        }

        public class LineSegment : Segment
        {
            public LineSegment(SKFreePolygon obj) : base(obj, 1)
            { }

            public override void AddToPath(SKPath path) => path.LineTo(GetPoint(end_idx));

            public override void SyncFromControlPoints() => SetPoint(ControlPoints[0].GetPosition(), end_idx);

            public override void SyncToControlPoints() => ControlPoints[0].SetPosition(GetPoint(end_idx));
        }

        public class CubicSegment : Segment
        {
            public SKPoint Anchor1 = SKPoint.Empty;
            public SKPoint Anchor2 = SKPoint.Empty;

            public CubicSegment(SKFreePolygon obj) : base(obj, 3)
            { }

            public override void Initialize()
            {
                // Default anchors if possible
                if (end_idx > start_idx)
                {
                    if (Anchor1.IsEmpty)
                        Anchor1 = GetPoint(start_idx) + new SKPoint(30, -30);

                    if (Anchor2.IsEmpty)
                        Anchor2 = GetPoint(end_idx) + new SKPoint(-30, -30);
                }
            }

            public override void AddToPath(SKPath path) => path.CubicTo(Anchor1, Anchor2, GetPoint(end_idx));

            public override void SyncFromControlPoints()
            {
                SetPoint(ControlPoints[0].GetPosition(), end_idx);
                Anchor1 = ControlPoints[1].GetPosition();
                Anchor2 = ControlPoints[2].GetPosition();
            }

            public override void SyncToControlPoints()
            {
                ControlPoints[0].SetPosition(GetPoint(end_idx));
                ControlPoints[1].SetPosition(Anchor1);
                ControlPoints[2].SetPosition(Anchor2);
            }

            public override void LastDraw(SKCanvas canvas, SKPaint Paint)
            {
                canvas.DrawLine(GetPoint(start_idx), Anchor1, Paint);
                canvas.DrawLine(GetPoint(end_idx), Anchor2, Paint);
            }
        }

        public SKPoint Center = new SKPoint(0, 0);              // pivot point

        public List<SKPoint> Vertices = new List<SKPoint>();    // clockwise ordered points, coordinates are relative to pivot
        public List<Segment> Segments = new List<Segment>();    // segments positions are based on vertex indexes        

        private SKPoint[] LastVertices; // last saved vertices before update

        public SKColor ColorAnchorLines = new SKColor(255, 255, 255);

        private SKPath path = new SKPath();

        public override bool Contains(SKPoint pos) => path.Contains(pos.X, pos.Y);

        // Events
        public Action<SKControlPoint>   OnVertexDragStart, OnVertexDragEnd; // <control_point>
		public Action<SKControlPoint, int, SKPoint>     OnVertexMove;   // <control_point>,<end_idx>,<pos>
        public Action<int, SKPoint>     OnVertexAdd;      // <end_idx>,<pos>        
        public Action<int>              OnVertexRemove;   // <end_idx>


        public SKFreePolygon(SKScene scene) : base(scene)
        {
            Color = new SKColor(0, 0, 0, 200);
            ColorHover = new SKColor(0, 0, 255, 200);
            ColorDragging = new SKColor(255, 255, 255, 200);

            Center = new SKPoint(0, 0);
        }               

        public void Clear()
        {
            Vertices.Clear();
            Segments.Clear();
        }

        // adds a new segment at specific index
        public void AddSegment(SKPoint point, SegmentType segtype, int index = -1)
        {
            if (index == -1)
                index = Vertices.Count;

            // Adds a new vertex in position, or at the end
            Vertices.Insert(index, point);

            Segment seg;
            switch (segtype)
            {
                case SegmentType.Cubic:
                    seg = new CubicSegment(this);
                    break;

                default:
                case SegmentType.Line:
                    seg = new LineSegment(this);
                    break;
            }

            Segments.Insert(index, seg);

            // recalculates segment's vertex indexes and re-initializes them if needed
            for (int seg_idx = 0; seg_idx < Vertices.Count; seg_idx++)
            {
                if (seg_idx < Segments.Count)
                {
                    seg = Segments[seg_idx];
                    seg.start_idx = seg_idx;
                    seg.end_idx = (seg_idx + 1) % Vertices.Count;

                    seg.Initialize();
                    seg.SyncToControlPoints();
                }
            }
        }

        public void ToggleSegmentsHitVisibility(bool toggle)
        {
            foreach (var s in Segments)
            {
                s.Contour.IsHitTestVisible = toggle;
            }
        }


        public override SKPoint GetPosition() => Center;

        // relies on the bounds calculated on the path each update
        public override SKSize GetSize() => _bounds.Size;

        // relies on the bounds calculated on the path each update
        public override SKRect GetBoundingBox() => _bounds;

		// Since vertexes are relative to center
		public SKPoint GetVertexPosition(int index) => Center + Vertices[index];

        public void SetVertexPosition(int index, SKPoint world_pos) => Vertices[index] = world_pos - Center;

        public override void SyncFromControlPoints()
        {
            // check is convex if requested
            if (MustBeConvex)
                if (LastVertices != null && !IsPolygonConvex())
                {
                    // restore last vertices
                    Vertices = new List<SKPoint>(LastVertices);

                    SyncToControlPoints();
                    return;
                }

            // check validity (not intersecting, and if triangle in the right order)
            if (MustBeValid)
            {
                bool is_valid = true;

                if (Vertices.Count == 3)
                {
                    var vertices_order = GetVerticesOrder();

                    is_valid = vertices_order <= 0;
                }
                else
                {
                    is_valid = !IsPolygonAutoIntersecting(Vertices);
                }

                if (LastVertices != null && !is_valid)
				{
					// restore last vertices
				    Vertices = new List<SKPoint>(LastVertices);

					SyncToControlPoints();
					return;
				}
			}

            foreach (var s in Segments)
                s.SyncFromControlPoints();
        }

        // specialized and optimize for syncing only the affected segment
        public void SyncFromControlPoint(int index)
        {
            foreach (var s in Segments)
                if (s.end_idx == index)
                    s.SyncFromControlPoints();
        }

        public override void SyncToControlPoints()
        {
            foreach (var s in Segments)
                s.SyncToControlPoints();
        }

        public override void SetPosition(SKPoint pos)
        {
            Center = pos;

            SyncToControlPoints();

            base.SetPosition(pos);
        }

        public override void Move(SKPoint pos_rel)
        {
            Center.Offset(pos_rel);

            SyncToControlPoints();

            base.Move(pos_rel);
        }

        public override void Update()
        {
            SyncFromControlPoints();

            if (IsDragging)
            {
                Paint.Color = ColorDragging;
            }
            else if (IsMouseOver)
            {
                Paint.Color = ColorHover;

				if (IsSelectableWithLeftClick)
				{
					if (IsMouseLeftClicked)
					{
						Select();
					}
					else if (IsSelected && IsLeftMouseStartDragging)
					{
						DragStart();
					}
				}
				else
				{
					// implicit selection
					if (IsLeftMouseStartDragging)
					{
						DragStart();
					}
				}
			}
            else
            {
                Paint.Color = Color;
            }

            foreach (var cp in GetChildren<Segment.SegmentControlPoint>())
                cp.IsEnabled = IsSelected;

            // ToggleControlPoints(IsSelected);

            // create the path
            // TODO: only when something changes

            path.Reset();

            // Origin on first vertex
            path.MoveTo(GetVertexPosition(0));

            foreach (var s in Segments)
                s.AddToPath(path);

            path.Close();

            _bounds = path.Bounds;

            UpdateContours();

            // backup last vertices
            LastVertices = Vertices.ToArray();
        }

		public override void OnEnable()
		{
			foreach (var cp in GetChildren<Segment.SegmentControlPoint>())
				cp.IsEnabled = IsSelected;

			base.OnEnable();
		}

		public override void OnDisable()
		{
            foreach (var cp in GetChildren<Segment.SegmentControlPoint>())
                cp.IsEnabled = false;

			base.OnEnable();
		}

		public override void UpdateContours()
        {
            foreach (var s in Segments)
                s.Contour.Update();
        }

        public override void Draw()
        {
            if (IsInError)
                Paint.Color = ColorError;

            Paint.Style = SKPaintStyle.Fill;

            Canvas.DrawPath(path, Paint);

            Paint.Color = SKColors.White;
            Paint.Style = SKPaintStyle.Stroke;
            Paint.StrokeWidth = 1;

			Canvas.DrawPath(path, Paint);

			if (IsSelected)
            {
                // Contours always drawn/hitchecked, but they are transparent when not hover
                foreach (var s in Segments)
                    s.Contour.Draw();
            }

            // some segments may have other drawing to do (controlpoints lines)
            if (IsSelected)
            {
                Paint.Color = ColorAnchorLines;
                foreach (var s in Segments)
                    s.LastDraw(Canvas, Paint);
            }
        }

		#region Helpers

		// Return True if the polygon is convex.
		private bool IsPolygonConvex()
		{
			float CrossProductLength(float Ax, float Ay,
			float Bx, float By, float Cx, float Cy)
			{
				// Get the vectors' coordinates.
				float BAx = Ax - Bx;
				float BAy = Ay - By;
				float BCx = Cx - Bx;
				float BCy = Cy - By;

				// Calculate the Z coordinate of the cross product.
				return (BAx * BCy - BAy * BCx);
			}

			// For each set of three adjacent points A, B, C,
			// find the dot product AB · BC. If the sign of
			// all the dot products is the same, the angles
			// are all positive or negative (depending on the
			// order in which we visit them) so the polygon
			// is convex.
			bool got_negative = false;
			bool got_positive = false;
			int nuPoints = Vertices.Count;
			int B, C;
			for (int A = 0; A < nuPoints; A++)
			{
				B = (A + 1) % nuPoints;
				C = (B + 1) % nuPoints;

				float cross_product =
					CrossProductLength(
						Vertices[A].X, Vertices[A].Y,
						Vertices[B].X, Vertices[B].Y,
						Vertices[C].X, Vertices[C].Y);
				if (cross_product < 0)
				{
					got_negative = true;
				}
				else if (cross_product > 0)
				{
					got_positive = true;
				}
				if (got_negative && got_positive) return false;
			}

			// If we got this far, the polygon is convex.
			return true;
		}

		private float GetVerticesOrder()
		{
			/*
			point[0] = (5, 0)   edge[0]: (6 - 5)(4 + 0) = 4
            point[1] = (6, 4)   edge[1]: (4 - 6)(5 + 4) = -18
            point[2] = (4, 5)   edge[2]: (1 - 4)(5 + 5) = -30
            point[3] = (1, 5)   edge[3]: (1 - 1)(0 + 5) = 0
            point[4] = (1, 0)   edge[4]: (5 - 1)(0 + 0) = 0
            
             (x2 − x1)(y2 + y1)
             */

			float sum = 0;
			for (int i = 0; i < Vertices.Count; i++)
			{
				var v1 = Vertices[i];
				var v2 = Vertices[(i + 1) % Vertices.Count];

				var edge = (v2.X - v1.X) * (v2.Y + v1.Y);
				sum += edge;
			}
			return sum;
		}

		public static bool IsPolygonAutoIntersecting(List<SKPoint> verts, bool isClosed=true)
		{
			bool DoLinesIntersect(SKPoint p1, SKPoint p2, SKPoint p3, SKPoint p4)
			{
				double denominator = ((p4.Y - p3.Y) * (p2.X - p1.X)) - ((p4.X - p3.X) * (p2.Y - p1.Y));
				if (denominator == 0) // lines are parallel
				{
					return false;
				}

				double ua = (((p4.X - p3.X) * (p1.Y - p3.Y)) - ((p4.Y - p3.Y) * (p1.X - p3.X))) / denominator;
				double ub = (((p2.X - p1.X) * (p1.Y - p3.Y)) - ((p2.Y - p1.Y) * (p1.X - p3.X))) / denominator;

				if (ua < 0 || ua > 1 || ub < 0 || ub > 1)
				{
					return false;
				}

				return true;
			}

			int vertexCount = verts.Count;
			if (vertexCount < (isClosed ? 4 : 3))
			{
				return false; // a polygon with less than 4 points cannot intersect itself
			}

			for (int i = 0; i < vertexCount; i++)
			{
				for (int j = i + 2; j < vertexCount; j++)
				{
					int nexti = (i + 1) % vertexCount;
					int nextj = (j + 1) % vertexCount;

					if (i == j || nexti == j || i == nextj)
					{
						continue; // skip adjacent edges
					}

					if (!isClosed && i == 0 && j == vertexCount - 1)
					{
						continue; // skip the last point connecting to the first for polylines
					}

					if (DoLinesIntersect(verts[i], verts[nexti], verts[j], verts[nextj]))
					{
						return true;
					}
				}
			}
			return false;
		}
		
		#endregion
	}
}
