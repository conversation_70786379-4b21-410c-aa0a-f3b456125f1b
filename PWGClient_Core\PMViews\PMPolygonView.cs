﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;


namespace Tabula.PMCore
{
    public class PMPolygonView : PMView<Tabula.PMCore.Polygon, Tabula.PMCore.PolygonView>
    {
        private SKPolyLine polyline;

        public PMPolygonView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }        

        public override void CreateVisual()
        {
            polyline = new SKPolyLine(Scene)
            {
                ShowClosedPath = true,
                ClosedPathColor = new SKColor(255,255,255,150),
                ClosedPathPointDistance = SARGAME.iMainWindowUI.CloseShapePointDistance
			};

            Visual = polyline;
            Visual.IsHitTestVisible = false;

            base.CreateVisual();
        }

        private void _update_vertices()
		{
            lock (polyline.Scene)
            {
                polyline.Vertices.Clear();

                foreach (var v in Model.vertices)
                    polyline.Vertices.Add(new SKPoint(v.x, v.y));
            }
		}


        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            polyline.IsDrawnInScene = Model.enabled;

            polyline.DrawLastLineToMouse = Model.enabled;

            _update_vertices();

#if DEBUG
            /*
            foreach (var v in Model.vertices)
                System.Diagnostics.Debug.WriteLine($"v[{Model.vertices.IndexOf(v)}]=(x:{v.x} , y:{v.y})");
            */
#endif

            return true;
        }       
    }
}
