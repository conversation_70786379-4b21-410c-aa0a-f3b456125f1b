using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PMCore;
using System.Threading.Tasks;
using Tabula.SKRenderGraph;
using SkiaSharp;
using System.Collections.Generic;
using Avalonia.Controls.Shapes;
using Avalonia.Media;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class LevelDesignPage : UserControl, ITabbedPage
	{
		public static LevelDesignPage Instance;

		public LevelDesignPage()
		{
			Instance = this;

			InitializeComponent();

			PointerPressed += (s, args) =>
			{
				var point = args.GetCurrentPoint(null);
				if (point.Properties.IsRightButtonPressed)
				{
					MainWindowUI.Instance.SetCursorHand();
				}
			};

			PointerReleased += (s, args) =>
			{
				var point = args.GetCurrentPoint(null);
				if (!point.Properties.IsRightButtonPressed)
				{
					MainWindowUI.Instance.SetCursorDefault();
				}
			};
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			SetStructuresLockStatus();

			InfoBox_Default();

			base.OnLoaded(e);
		}

		#region Infobox

		public void InfoBox_Default()
		{
			Tabula.SharedObjectMap.Dispatcher.DispatchAsync(() =>
			{
				MainWindowUI.Instance.InfoBox.IsVisible = true;
				MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
				MainWindowUI.Instance.InfoBox.Message = "Use Mouse Wheel to zoom and Right Mouse to drag.";
				MainWindowUI.Instance.InfoBox.ErrorMessage = null;
				MainWindowUI.Instance.InfoBox.IsActionButtonVisible = false;
				MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = false;
			});
		}

		public void InfoBox_CreateStructure()
		{
			Tabula.SharedObjectMap.Dispatcher.DispatchAsync(() =>
			{
				MainWindowUI.Instance.InfoBox.IsVisible = true;
				MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
				MainWindowUI.Instance.InfoBox.Message = "Draw the shape points with Left Mouse clicks and close it clicking on last point, or with a Right Mouse click.\nRemove points with a Right Click on them.";
				MainWindowUI.Instance.InfoBox.ErrorMessage = null;
				MainWindowUI.Instance.InfoBox.IsActionButtonVisible = false;
				MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = false;
			});
		}

		#endregion


		#region UI Events

		async void bt_add_structure_Click(object sender, RoutedEventArgs args)
		{
			await AddStructureFree();
		}

		async void bt_delete_click(object sender, RoutedEventArgs args)
		{
			await MainWindowUI.Instance.DeleteSelectedObject();
		}

		void bt_lock_structures_Click(object sender, RoutedEventArgs args)
		{
			MainWindowUI.Instance.AreStructuresLocked = !MainWindowUI.Instance.AreStructuresLocked;

			SetStructuresLockStatus();
		}

		void SetStructuresLockStatus()
		{
			var vstructs = PMViewExt.GetPMViewsOfType<PMStructureView>();

			if (vstructs != null)
				foreach (var vs in vstructs)
					vs.Visual.IsDraggable = !MainWindowUI.Instance.AreStructuresLocked;
		}

		async void bt_create_entity_Click(object sender, RoutedEventArgs args)
		{

			var created_entity = await EntityCreateDialog.Show();

			if (created_entity == null)
			{
				// TODO: error
				return;
			}

			// check license feature
			if (!string.IsNullOrEmpty(created_entity.license_feature))
			{
				if (!LocalAppConfig.Instance.CheckLicenseFeature(created_entity.license_feature))
				{
					await ProgressDialog.ShowMessageAsync("Your license does not support this feature");
					return;
				}
			}

			if (!created_entity.has_visual)
			{
				// add directly 
				SharedObjectMap.Dispatcher.DispatchAsync(async () =>
				{
					var obj = await PMEntityView.CreateEntityOnServer(created_entity);

					if (obj == null)
					{
						// TODO: error
						await ProgressDialog.ShowMessageAsync("Error creating Object");
					}
				});

				return;
			}

			MainWindowUI.Instance.SetCursorIcon(created_entity.icon);

			// OK need to pick the point where the entity is created
			LevelEditor.Instance?.Scene.BeginClickCapture(async (button, pos) =>
			{
				if (button == SKScene.MouseRightButton)
				{
					// abort action
					return;
				}

				LevelEditor.Instance?.Scene.EndClickCapture();

				SharedObjectMap.Dispatcher.DispatchAsync(async () =>
				{
					try
					{
						var obj = await PMEntityView.CreateEntityOnServer(created_entity, pos.X, pos.Y);
						if (obj == null)
							throw new System.Exception("Error creating Object");
					}
					catch
					{
						await ProgressDialog.ShowMessageAsync("Error creating Object");
					}
					finally
					{
						SharedObjectMap.Dispatcher.DispatchAsync(() => MainWindowUI.Instance.SetCursorIcon(null));
					}


				});

				
			});
		}

		void bt_settings_Click(object sender, RoutedEventArgs args)
		{
			settings_popup.IsOpen = !settings_popup.IsOpen;
		}

		#endregion

		#region Structure Drawing

		private bool _polygon_active = false;

		async Task AddStructureFree()
		{
			// FIXME: on some machines the cross is barely visible...
			// MainView.Instance.SetCursorCross();

			async Task _last_point(SKPoint pos, bool add_last_point)
			{
				// add last point
				if (add_last_point)
					MainWindowUI.Instance.View.Polygon.vertices_Add(new Vector2i(pos.X, pos.Y));

				// finish action, create a structure
				Tabula.SharedObjectMap.Dispatcher.DispatchAsync(() =>
				{
					MainWindowUI.Instance.SetCursorDefault();
					MainWindowUI.Instance.IsDrawingStructure = false;
				});
				LevelEditor.Instance?.Scene.EndClickCapture();

				var structure_view = await _create_structure();

				MainWindowUI.Instance.View.SetEditingPolygon(false);
				_polygon_active = false;

				var structure_views = PMViewExt.GetPMViewsOfType<PMStructureView>();
				foreach (var s in structure_views)
				{
					s.Visual.IsSelectable = true;
					s.Visual.IsDraggable = !MainWindowUI.Instance.AreStructuresLocked;
				}

				structure_view?.Visual.Select();

				InfoBox_Default();
			}

			if (!_polygon_active)
			{
				var structure_views = PMViewExt.GetPMViewsOfType<PMStructureView>();
				foreach (var s in structure_views)
				{
					s.Visual.IsSelectable = false;
					s.Visual.IsDraggable = !MainWindowUI.Instance.AreStructuresLocked;
				}

				LevelEditor.Instance?.Scene.UnSelectAllObjects();

				MainWindowUI.Instance.View.SetEditingPolygon(true);

				InfoBox_CreateStructure();

				LevelEditor.Instance?.Scene.BeginClickCapture(async (button, pos) =>
				{
					if (button == SKScene.MouseRightButton)
					{
						await _last_point(pos, true);

						return;
					}
					else if (button == SKScene.MouseLeftButton)
					{
						// Check if we are closing the shape with the last point
						var scene = LevelEditor.Instance.Scene;
						var vertices = MainWindowUI.Instance.View.Polygon.vertices;
						if (scene.IsMouseInScene && vertices.Count > 2)
						{
							var dist = SKPoint.Distance(pos, new SKPoint(vertices[0].x, vertices[0].y));
							if (dist < MainWindowUI.Instance.CloseShapePointDistance)
							{
								await _last_point(pos, false);
								return;
							}
						}

						// Adds a point to the polygon
						MainWindowUI.Instance.View.Polygon.vertices_Add(new Vector2i(pos.X, pos.Y));
					}

				});

				_polygon_active = true;
				MainWindowUI.Instance.IsDrawingStructure = true;
			}
			else
			{
				// finish action, create a structure
				Tabula.SharedObjectMap.Dispatcher.DispatchAsync(() =>
				{
					MainWindowUI.Instance.SetCursorDefault();
					MainWindowUI.Instance.IsDrawingStructure = false;
				});
				LevelEditor.Instance?.Scene.EndClickCapture();

				var structure_view = await _create_structure();

				MainWindowUI.Instance.View.SetEditingPolygon(false);
				_polygon_active = false;
				MainWindowUI.Instance.IsDrawingStructure = false;

				var structure_views = PMViewExt.GetPMViewsOfType<PMStructureView>();
				foreach (var s in structure_views)
				{
					s.Visual.IsSelectable = true;
					s.Visual.IsDraggable = !MainWindowUI.Instance.AreStructuresLocked;
				}

				structure_view?.Visual.Select();

				InfoBox_Default();
			}

		}

		private async Task<PMStructureView> _create_structure()
		{
			var Polygon = MainWindowUI.Instance.View.Polygon;

#if DEBUG
			System.Diagnostics.Debug.WriteLine("_create_structure");
			foreach (var v in Polygon.vertices)
				System.Diagnostics.Debug.WriteLine($"v[{Polygon.vertices.IndexOf(v)}]=(x:{v.x} , y:{v.y})");
#endif

			

			if (Polygon.vertices.Count <= 2)
				return null;

			// compute center
			var center = new Vector2f(0, 0);
			var vertices = new List<Vector2f>();

			//FIXME: always remove last vertex?????
			//for (int i = 0; i < Polygon.vertices.Count - 1; i++)
			for (int i = 0; i < Polygon.vertices.Count; i++)
			{
				center.x += Polygon.vertices[i].x;
				center.y += Polygon.vertices[i].y;

				vertices.Add(new Vector2f(Polygon.vertices[i].x, Polygon.vertices[i].y));
			}

			center.x /= (float)Polygon.vertices.Count;
			center.y /= (float)Polygon.vertices.Count;

			foreach (var v in vertices)
			{
				v.x -= center.x;
				v.y -= center.y;
			}

			// TODO: reorder vertices clockwise
			//vertices = JarvisMarch_ReorderVertices(vertices);
			float order = check_clockwise(vertices);

			// Debug.WriteLine($"Structure Vertices order = {order}");

			// <0 is clockwise, >0 is anticlockwise and must reversded
			if (order > 0)
				vertices.Reverse();

			// check this is a valid shape using the SKFreePolygon function (needs SKPoints)
			var skpoints = new List<SKPoint>();
			foreach (var v in vertices)
				skpoints.Add(new SKPoint(v.x, v.y));

			if (SKFreePolygon.IsPolygonAutoIntersecting(skpoints, true))
			{
				// shape is not valid
				return null;
			}

			/*
			var new_guid = MainWindowUI.Instance.View.Structures_Add(new Structure()
			{
				position = center,
				vertices = vertices
			});

			// wait for the view to be created
			var new_view = await PMViewExt.WaitForPMViewWithGuid<PMStructureView>(new_guid);
			*/

			var new_structure = await MainWindowUI.Instance.CreateStructureOnServer(new Structure()
			{
				position = center,
				vertices = vertices
			});

			var new_view =  PMViewExt.GetPMViewOfType<PMStructureView>(new_structure.__guid);

			// TEST: reinitialize all views like on model receive
			// Without this deleting a vertex resulted in misalignement
			// TODO: probably ALL new created entities should have views reinitialized
			Tabula.SharedObjectMap.SharedObjectMap.InitializeAllViews();

			return new_view;
		}

		// https://stackoverflow.com/questions/1165647/how-to-determine-if-a-list-of-polygon-points-are-in-clockwise-order
		private float check_clockwise(List<Vector2f> vertices)
		{
			float sum = 0;
			for (int i = 0; i < vertices.Count; i++)
			{
				var v1 = vertices[i];
				var v2 = vertices[(i + 1) % vertices.Count];

				var edge = (v2.x - v1.x) * (v2.y + v1.y);
				sum += edge;
			}

			return sum;
		}

		#endregion

		#region ITabbedPage

		public void OnTabSelected()
		{
			MainWindowUI.Instance.Fit();

			// In calibrator, make sure to show structures (other modules will ignore the call)
			// public void ConfigureCalibrator(bool toggle_grid, bool toggle_spatialgrid, bool toggle_pattern_background, bool toggle_structures,  bool toggle_calibration_points)
			Task.Run(async () => await MainWindowUI.Instance.View.CallMethod("ConfigureCalibrator", false, true, false, true, false));
		}

		public void OnTabUnselected()
		{
			// TODO
		}

		#endregion
	}
}
