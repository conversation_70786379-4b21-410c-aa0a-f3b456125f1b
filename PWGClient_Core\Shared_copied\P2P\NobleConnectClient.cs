﻿using System;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using NobleConnect;
using NobleConnect.Ice;

namespace NobleConnectLib
{
    public class NobleConnectClient
    {
        public Peer peer;

        private Action<IPEndPoint>? _onReadyToConnectToBridge;
        private IPEndPoint? _localBridgeEndPoint;
        private bool isConnecting = true;
        public bool needAbort = false;
        public NobleConnectClient(IceConfig iceConfig, Logger.Level logLevel)
        {
            // TODO: check online/offline status
            // Peer ctor assumes an online status
            Console.WriteLine("[CLIENT] Initializing Peer...");
            peer = new Peer(iceConfig);

            Logger.logger = delegate(string s) { Console.WriteLine($"[CLIENT]: {s} "); };
            Logger.warnLogger = delegate(string s) { Console.WriteLine($"[CLIENT]: {s} "); };
            Logger.errorLogger = delegate(string s) { Console.WriteLine($"[CLIENT]: {s} "); };
            Logger.logLevel = logLevel;
            
            peer.PrepareToConnect();
        }
    
        public void Initialize(IPEndPoint serverEndPoint, Action<IPEndPoint>? onReadyToConnectToBridge)
        {
            Console.WriteLine($"[CLIENT] Connecting Peer to server at {serverEndPoint.Address.ToString()}:{serverEndPoint.Port}");
            _onReadyToConnectToBridge = onReadyToConnectToBridge;
            peer.InitializeClient(serverEndPoint, OnClientPrepared);
            
            while (isConnecting && !needAbort)
            {
                peer.Update();
                Thread.Sleep(50);
            }
        }
    
        void OnClientPrepared(IPEndPoint bridgeEndPoint, IPEndPoint bridgeEndPointIPv6)
        {
            isConnecting = false;
            _localBridgeEndPoint = bridgeEndPoint;
            _onReadyToConnectToBridge?.Invoke(bridgeEndPoint);
        }

        public IPEndPoint GetLocalBridgeEndpoint()
        {
            return _localBridgeEndPoint;
        }

        #region UDP_CLIENT

        public async Task UdpSendTestData(string message, Action<string, int> onReceiveData)
        {
            
            if (_localBridgeEndPoint == null)
                return;
            
            var data = Encoding.UTF8.GetBytes(message);
            using (var udpClient = new UdpClient(AddressFamily.InterNetwork))
            {
                await udpClient.SendAsync(data, data.Length,
                    _localBridgeEndPoint.Address.ToString(),
                    _localBridgeEndPoint.Port);
                var received = await udpClient.ReceiveAsync();
                var response = Encoding.UTF8.GetString(received.Buffer, 0, received.Buffer.Length);
                onReceiveData.Invoke(response, received.Buffer.Length);
                udpClient.Close();
            }
        }
        #endregion
        
        #region TCP_CLIENT

        public async Task TcpSendTestData(string message, Action<string, int, TimeSpan> onReceiveData)
        {
            Stopwatch stopwatch = new Stopwatch();
            if (_localBridgeEndPoint == null)
                return;
            
            var data = Encoding.UTF8.GetBytes(message);
            using (var tcpClient = new TcpClient(AddressFamily.InterNetwork))
            {
                Console.WriteLine($"[CLIENT TCP SOCKET] Sending {data.Length} Bytes to {_localBridgeEndPoint.Address}:{_localBridgeEndPoint.Port}");

                // TODO: Connection Refused
                await tcpClient.ConnectAsync(_localBridgeEndPoint.Address, _localBridgeEndPoint.Port);
                //await tcpClient.GetStream().WriteAsync(data, 0, data.Length);

            }
        }
        #endregion
        
    }
}