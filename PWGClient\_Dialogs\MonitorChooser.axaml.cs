using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Interactivity;
using PropertyChanged;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.Unity;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class MonitorChooser : UserControl
	{
		public string dialog_identifier = "monitorchooser_dialog";

		public MonitorChooser()
		{
			InitializeComponent();
		}		
		
		public static async Task<Tabula.Unity.Display> Show()
		{
			var ret = await DialogHostAvalonia.DialogHost.Show( new MonitorChooser());

			return ret as Tabula.Unity.Display;
		}

		protected async override void OnLoaded(RoutedEventArgs e)
		{
			MainWindowUI.Instance.CustomResolutionEnabled = false;
			MainWindowUI.Instance.CustomResolutionSpout = false;

			await Refresh();

			base.OnLoaded(e);			
		}

		bool is_refresing = false;
		public async Task Refresh()
		{
			if (is_refresing)
				return;

			is_refresing = true;

			try
			{

				UnityToolData unitytool_data = null;

				await ProgressDialog.ShowAsync(new ProgressDialogView()
				{
					Message = "Checking projector",
					IsProgressIndeterminate = true,
					Action = async (cts) =>
					{
						// Start the UnityTool, refreshes the list automatically
						unitytool_data = await SARGAME.Instance.UnityTool();
					},
					CloseOnTaskFinish = true
				}, identifier: dialog_identifier);

				MainWindowUI.Instance.UnityToolData = unitytool_data;



				

				//displays_control.DataContext = null;

				//displays_control.DataContext = unitytool_data;

				//var template = displays_control.ItemTemplate.Build(MainWindowUI.Instance.UnityToolData);

				//var templated_obj = template.Build(MainWindowUI.Instance.UnityToolData);

				MainWindowUI.Instance.RaiseAllPropertyChanged();

			}
			catch(System.Exception ex)
			{
				App.Local.logException("MonitorChooser.Refresh()", ex);
			}

			is_refresing = false;
		}	

		async void bt_refresh_Click(object sender, RoutedEventArgs args)
		{
			await Refresh();
		}

		void bt_choose_Click(object sender, RoutedEventArgs args)
		{
			MainWindowUI.Instance.CustomResolutionEnabled = false;

			var d = (sender as Control).DataContext as Tabula.Unity.Display;

			DialogHostAvalonia.DialogHost.Close("dialog", d);
		}

		void bt_cancel_Click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", null);
		}

		#region Custom Resolution

		async void bt_custom_resolution_Click(object sender, RoutedEventArgs args)
		{
			if (!LocalAppConfig.Instance.CheckLicenseFeature("business") && !LocalAppConfig.Instance.CheckLicenseFeature("spout"))
			{
				await ProgressDialog.ShowMessageAsync("You need to upgrade to use this feature", identifier: dialog_identifier);
				return;
			}

			custom_resolution_popup.IsOpen = true;
		}

		void bt_custom_resolution_cancel_Click(object sender, RoutedEventArgs args)
		{
			custom_resolution_popup.IsOpen = false;
		}

		void bt_custom_resolution_start_Click(object sender, RoutedEventArgs args)
		{
			custom_resolution_popup.IsOpen = false;

			MainWindowUI.Instance.CustomResolutionEnabled = true;

			// return a created virtual Display
			var virtual_display = new Tabula.Unity.Display()
			{
				system_width = MainWindowUI.Instance.CustomResolutionWidth,
				system_height = MainWindowUI.Instance.CustomResolutionHeight,
				active = false
			};

			DialogHostAvalonia.DialogHost.Close("dialog", virtual_display);
		}

		#endregion
	}
}
