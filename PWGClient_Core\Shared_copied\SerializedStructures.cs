﻿//TABULA_GUID:{F0DBDB0D-BB2F-439D-91C8-62D832FD6FFB}
// Copyright (C) 2007-2010 <PERSON> BRLSFN75D18A944S
// This file is part of the "TabulaSDK".
// For conditions of distribution and use, see copyright notice in the root folder TABULASDK_LICENSE.TXT
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

// Structures for XML serialization
/*
 * 
 * REFERENCE_WINDOWS to use Rect
 */


namespace Tabula
{
    public partial class TabulaSerializedStructureAttribute : Attribute
    { }

#if __MOBILE__
    public partial class SerializableAttribute : Attribute { }
#endif

    // This is the base class form XML configured custom contents (for filesystem-based loading)
    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedContentConfig
    {
        public string Type;
    }

    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedZone
    {
        public int X = 0, Y = 0;
        public int Width = 0, Height = 0;
    }

    // NOTE: this class basic type has become float, and supersedes SerializedPointF
    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedPoint
    {
        public double X = 0, Y = 0;

        public SerializedPoint()
        {
            X = 0;
            Y = 0;
        }

        public SerializedPoint(int x, int y)
        {
            X = (float) x;
            Y = (float)y;
        }

        public SerializedPoint(float x, float y)
        {
            X = (float) x;
            Y = (float) y;
        }

        public SerializedPoint(double x, double y)
        {
            X = x;
            Y = y;
        }

#if REFERENCE_WINDOWS
        public SerializedPoint(System.Windows.Point pt)
        {
            X = pt.X;
            Y = pt.Y;
        }

        public System.Windows.Point ToPoint()
        {
            return new System.Windows.Point(X,Y);
        }
#endif

        public override bool Equals(object obj)
        {
            if (obj is SerializedPoint)
            {
                SerializedPoint op = obj as SerializedPoint;
                return (X == op.X && Y == op.Y);
            }
            else
                return base.Equals(obj);
        }

        public bool EqualsInteger(object obj)
        {
            if (obj is SerializedPoint)
            {
                SerializedPoint op = obj as SerializedPoint;
                return ((int)X == (int)op.X && (int)Y == (int)op.Y);
            }
            else
                return false;
        }

        public static SerializedPoint operator *(SerializedPoint first, float other)
        {
            return new SerializedPoint(first.X * other, first.Y * other);
        }

        // copy array by value
        public static void CopyByValueTo(SerializedPoint[] src, SerializedPoint[] dest)
        {
            for (int i = 0; i < src.Length; i++)
                if (src[i]!=null)
                {
                    if (dest[i] == null)
                        dest[i] = new SerializedPoint();

                    dest[i].X = src[i].X;
                    dest[i].Y = src[i].Y;
                }

        }
    }

    // Deprecated: stays only for name compatibility
    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedPointF : SerializedPoint
    {        
        public SerializedPointF()
        {
            X = 0;
            Y = 0;
        }

        public SerializedPointF(int x, int y)
        {
            X = (float) x;
            Y = (float)y;
        }

        public SerializedPointF(float x, float y)
        {
            X = (float) x;
            Y = (float) y;
        }

        public SerializedPointF(double x, double y)
        {
            X = x;
            Y = y;
        }

        public static SerializedPointF operator *(SerializedPointF first, float other)
        {
            return new SerializedPointF(first.X * other, first.Y * other);
        }

        // copy array by value
        public static void CopyByValueTo(SerializedPointF[] src, SerializedPointF[] dest)
        {
            for (int i = 0; i < src.Length; i++)
                if (src[i] != null)
                {
                    if (dest[i] == null)
                        dest[i] = new SerializedPointF();

                    dest[i].X = src[i].X;
                    dest[i].Y = src[i].Y;
                }

        }
    }

    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedSize
    {
        public double Width = 0, Height = 0;

        public SerializedSize()
        {}

        public SerializedSize(double w, double h)
        {
            Width = w;
            Height = h;
        }
        
#if REFERENCE_WINDOWS
        public static implicit operator System.Windows.Size(SerializedSize sz)  // implicit digit to byte conversion operator
        {
            if (sz.Width==-1 || sz.Height==-1)
                return System.Windows.Size.Empty;
            else
                return new System.Windows.Size(sz.Width, sz.Height);
        }
#endif
    }

    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedRectangle
    {
        public int Left = 0, Top = 0, Right = 0, Bottom = 0;
        
        [XmlIgnore]
        public int Width
        {
            get
            {
                return Right - Left;
            }
        }

        [XmlIgnore]
        public int Height
        {
            get
            {
                return Bottom - Top;
            }
        }
        
        [XmlIgnore]
        public bool IsValid
        {
            get
            {
                return (Left <= Right) && (Top <= Bottom);
            }
        }

        [XmlIgnore]
        public bool IsEmpty
        {
            get
            {
                return (Width <= 0 || Height <= 0);
            }

        }

        public SerializedRectangle()
        {}

#if REFERENCE_WINDOWS
        public SerializedRectangle(System.Windows.Rect rect)
        {
            Left = (int) rect.Left;
            Top = (int)rect.Top;
            Right = (int)rect.Right;
            Bottom = (int)rect.Bottom;
        }        
#endif

        public SerializedRectangle(int left, int top, int right, int bottom)
        {
            Left = left;
            Top = top;
            Right = right;
            Bottom = bottom;
        }

        public SerializedRectangle(int left, int top, Tuple<int,int> size)
        {
            Left = left;
            Top = top;
            Right = left + size.Item1;
            Bottom = top + size.Item2;
        }

        public override bool Equals(object obj)
        {
            SerializedRectangle r2 = obj as SerializedRectangle;
            if (r2 == null)
                return false;
            else
                return (Left == r2.Left && Right == r2.Right && Top == r2.Top && Bottom == r2.Bottom);
        }
    }

    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedRectangleF
    {
        public float Left = 0, Top = 0, Right = 0, Bottom = 0;

        [XmlIgnore]
        public float Width
        {
            get
            {
                return Right - Left;
            }
        }

        [XmlIgnore]
        public float Height
        {
            get
            {
                return Bottom - Top;
            }
        }

        public SerializedRectangleF()
        {}

#if REFERENCE_WINDOWS
        public SerializedRectangleF(System.Windows.Rect rect)
        {
            Left = (float) rect.Left;
            Top = (float)rect.Top;
            Right = (float)rect.Right;
            Bottom = (float)rect.Bottom;
        }
#endif

        public SerializedRectangleF(float left, float top, float right, float bottom)
        {
            Left = left;
            Top = top;
            Right = right;
            Bottom = bottom;
        }
    }

    [Serializable]
    [TabulaSerializedStructureAttribute]
    public class SerializedColor
    {
        public int A = 255, R = 0, G = 0, B = 0;

        public SerializedColor()
        { }
        
        public SerializedColor(int a, int r, int g, int b)
        {
            A = a; R = r; G = g; B = b;
        }
    }


    [Serializable]
    [TabulaSerializedStructureAttribute]
    [XmlRoot("dictionary")]
    public class SerializedDictionary<TKey, TValue> : Dictionary<TKey, TValue>, IXmlSerializable
#if !__MOBILE__
        , ICloneable
#endif
    {

        public SerializedDictionary()
            : base()
        { }

#if !__MOBILE__
        public SerializedDictionary(SerializationInfo info, StreamingContext context) : base(info, context)
        {

        }
#endif

#region IXmlSerializable Members

        public System.Xml.Schema.XmlSchema GetSchema()
        {
            return null;
        }

        public void ReadXml(System.Xml.XmlReader reader)
        {
            System.Xml.Serialization.XmlSerializer keySerializer = new System.Xml.Serialization.XmlSerializer(typeof(TKey));
            System.Xml.Serialization.XmlSerializer valueSerializer = new System.Xml.Serialization.XmlSerializer(typeof(TValue));

            bool wasEmpty = reader.IsEmptyElement;
            reader.Read();

            if (wasEmpty)
                return;

            while (reader.NodeType != System.Xml.XmlNodeType.EndElement)
            {
                reader.ReadStartElement("item");
                reader.ReadStartElement("key");
                TKey key = (TKey)keySerializer.Deserialize(reader);
                reader.ReadEndElement();
                reader.ReadStartElement("value");
                TValue value = (TValue)valueSerializer.Deserialize(reader);
                reader.ReadEndElement();
                this.Add(key, value);
                reader.ReadEndElement();
                reader.MoveToContent();
            }

            reader.ReadEndElement();
        }

 
        public void WriteXml(System.Xml.XmlWriter writer)
        {
            System.Xml.Serialization.XmlSerializer keySerializer = new System.Xml.Serialization.XmlSerializer(typeof(TKey));
            System.Xml.Serialization.XmlSerializer valueSerializer = new System.Xml.Serialization.XmlSerializer(typeof(TValue));

            foreach (TKey key in this.Keys)
            {
                writer.WriteStartElement("item");
                writer.WriteStartElement("key");
                keySerializer.Serialize(writer, key);
                writer.WriteEndElement();
                writer.WriteStartElement("value");
                TValue value = this[key];
                valueSerializer.Serialize(writer, value);
                writer.WriteEndElement();
                writer.WriteEndElement();
            }

        }

#endregion


        public object Clone()
        {
            SerializedDictionary<TKey, TValue> copy = new SerializedDictionary<TKey, TValue>();

            foreach (var kvp in this)
                copy.Add(kvp.Key, kvp.Value);

            return copy;
        }
    }


}
