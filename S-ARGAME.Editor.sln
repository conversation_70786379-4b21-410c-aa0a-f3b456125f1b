﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32811.315
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{3DA99C4E-89E3-4049-9C22-0A7EC60D83D8}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		Directory.Build.props = Directory.Build.props
		global.json = global.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PWGClient", "PWGClient\PWGClient.csproj", "{5431832A-4B47-4407-9235-E42990D8187D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PWGClient.Desktop", "PWGClient.Desktop\PWGClient.Desktop.csproj", "{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deps", "deps", "{F859D1D6-32B0-4B4A-974F-7E1FDE53AA38}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "netsparkle", "netsparkle", "{DBF31AEE-0DC2-4F65-A3C4-E42968A20A24}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NetSparkle", "..\..\_PUBLIC\NetSparkle\src\NetSparkle\NetSparkle.csproj", "{6E8ACE07-775C-4240-9EC6-99D74FD89F78}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NetSparkle.UI.Avalonia", "..\..\_PUBLIC\NetSparkle\src\NetSparkle.UI.Avalonia\NetSparkle.UI.Avalonia.csproj", "{3AC7605C-C9B9-4D45-A07B-0D2284029628}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avalonia.HtmlRenderer", "..\..\_AVALONIA\Avalonia.HtmlRenderer\Source\Avalonia.HtmlRenderer\Avalonia.HtmlRenderer.csproj", "{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PWGClient_Core", "PWGClient_Core\PWGClient_Core.csproj", "{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Beta Release|Any CPU = Beta Release|Any CPU
		Beta Release|ARM64 = Beta Release|ARM64
		Beta Release|x64 = Beta Release|x64
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|Any CPU.ActiveCfg = Beta Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|Any CPU.Build.0 = Beta Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|ARM64.ActiveCfg = Beta Release|ARM64
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|ARM64.Build.0 = Beta Release|ARM64
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|x64.ActiveCfg = Beta Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Beta Release|x64.Build.0 = Beta Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|ARM64.Build.0 = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Debug|x64.Build.0 = Debug|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|Any CPU.Build.0 = Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|ARM64.ActiveCfg = Release|ARM64
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|ARM64.Build.0 = Release|ARM64
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|x64.ActiveCfg = Release|Any CPU
		{5431832A-4B47-4407-9235-E42990D8187D}.Release|x64.Build.0 = Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|Any CPU.ActiveCfg = Beta Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|Any CPU.Build.0 = Beta Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|ARM64.ActiveCfg = Beta Release|ARM64
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|ARM64.Build.0 = Beta Release|ARM64
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|x64.ActiveCfg = Beta Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Beta Release|x64.Build.0 = Beta Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|ARM64.Build.0 = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Debug|x64.Build.0 = Debug|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|ARM64.ActiveCfg = Release|ARM64
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|ARM64.Build.0 = Release|ARM64
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|x64.ActiveCfg = Release|Any CPU
		{0C08DD08-0E64-4947-8DA2-B91CB8C9406E}.Release|x64.Build.0 = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|Any CPU.Build.0 = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|ARM64.ActiveCfg = Beta Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|ARM64.Build.0 = Beta Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|x64.ActiveCfg = Beta Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Beta Release|x64.Build.0 = Beta Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|ARM64.Build.0 = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Debug|x64.Build.0 = Debug|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|ARM64.ActiveCfg = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|ARM64.Build.0 = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|x64.ActiveCfg = Release|Any CPU
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78}.Release|x64.Build.0 = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|Any CPU.Build.0 = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|ARM64.ActiveCfg = Beta Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|ARM64.Build.0 = Beta Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|x64.ActiveCfg = Beta Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Beta Release|x64.Build.0 = Beta Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|ARM64.Build.0 = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Debug|x64.Build.0 = Debug|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|ARM64.ActiveCfg = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|ARM64.Build.0 = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|x64.ActiveCfg = Release|Any CPU
		{3AC7605C-C9B9-4D45-A07B-0D2284029628}.Release|x64.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|Any CPU.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|Any CPU.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|ARM64.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|ARM64.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|x64.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Beta Release|x64.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|ARM64.Build.0 = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|x64.ActiveCfg = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Debug|x64.Build.0 = Debug|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|Any CPU.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|ARM64.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|ARM64.Build.0 = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|x64.ActiveCfg = Release|Any CPU
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222}.Release|x64.Build.0 = Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|Any CPU.ActiveCfg = Beta Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|Any CPU.Build.0 = Beta Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|ARM64.ActiveCfg = Beta Release|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|ARM64.Build.0 = Beta Release|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|x64.ActiveCfg = Beta Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Beta Release|x64.Build.0 = Beta Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|ARM64.Build.0 = Debug|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Debug|x64.Build.0 = Debug|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|ARM64.ActiveCfg = Release|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|ARM64.Build.0 = Release|ARM64
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|x64.ActiveCfg = Release|Any CPU
		{EBC4A0F5-E6CC-4923-8EBE-0B44ECFA538F}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DBF31AEE-0DC2-4F65-A3C4-E42968A20A24} = {F859D1D6-32B0-4B4A-974F-7E1FDE53AA38}
		{6E8ACE07-775C-4240-9EC6-99D74FD89F78} = {DBF31AEE-0DC2-4F65-A3C4-E42968A20A24}
		{3AC7605C-C9B9-4D45-A07B-0D2284029628} = {DBF31AEE-0DC2-4F65-A3C4-E42968A20A24}
		{045D9734-3BC0-4F8E-B5B3-AD54B4EE3222} = {DBF31AEE-0DC2-4F65-A3C4-E42968A20A24}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {83CB65B8-011F-4ED7-BCD3-A6CFA935EF7E}
	EndGlobalSection
EndGlobal
