using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Interactivity;
using Avalonia.Threading;
using PropertyChanged;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.Unity;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class InfoBox : UserControl
	{
		// View
		// NOTE: this is bound in MainWindowUI !
		[AddINotifyPropertyChangedInterface]
		public class View
		{
			public MainWindowUI Main => MainWindowUI.Instance;

			public bool IsVisible { get; set; }

			public Material.Icons.MaterialIconKind Icon { get; set; }
			public string Message { get; set; }
			public string ErrorMessage { get; set; }

			public bool IsErrorMessageVisible => !string.IsNullOrEmpty(ErrorMessage);

			public bool IsCancelButtonVisible { get; set; }
			public string CancelButtonText { get; set; } = "Cancel";

			public bool IsActionButtonVisible { get; set; }
			public string ActionButtonText { get; set; }

			public Action OnCancelButton { get; set; }
			public Action OnActionButton { get; set; }
		}

		public InfoBox()
		{
			InitializeComponent();

			DataContext = MainWindowUI.Instance.InfoBox;
		}

		void bt_cancel_Click(object sender, RoutedEventArgs args)
		{
			var v = DataContext as View;
			v.OnCancelButton?.Invoke();
		}

		void bt_action_Click(object sender, RoutedEventArgs args)
		{
			var v = DataContext as View;
			v.OnActionButton?.Invoke();
		}

		#region Check Timer

		// Static method that works on MainWindowUI.Instance.InfoBox for ALL infoboxes

		private static DispatcherTimer check_timer;

		public static void StartCheckAction(Action action, double interval_seconds=0.5)
		{
			check_timer?.Stop();

			check_timer = new DispatcherTimer(TimeSpan.FromSeconds(interval_seconds), DispatcherPriority.Default,
				(e, a) => action?.Invoke());	
		}

		public static void StopCheckAction()
		{
			check_timer?.Stop();
		}

		#endregion

	}
}
