﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net7.0</TargetFramework>
    <AssemblyName>PWGClient</AssemblyName>
    <RootNamespace>Tabula.PMCore</RootNamespace>
    <ApplicationIcon>Images\icon_128.ico</ApplicationIcon>
    <Platforms>AnyCPU;ARM64</Platforms>
    <Configurations>Debug;Release;Beta Release</Configurations>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>BETA_RELEASE;SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <AvaloniaNameGeneratorBehavior>InitializeComponent</AvaloniaNameGeneratorBehavior>
    <AvaloniaNameGeneratorDefaultFieldModifier>internal</AvaloniaNameGeneratorDefaultFieldModifier>
    <AvaloniaNameGeneratorFilterByPath>*</AvaloniaNameGeneratorFilterByPath>
    <AvaloniaNameGeneratorFilterByNamespace>*</AvaloniaNameGeneratorFilterByNamespace>
    <AvaloniaNameGeneratorViewFileNamingStrategy>NamespaceAndClassName</AvaloniaNameGeneratorViewFileNamingStrategy>
  </PropertyGroup>
  <ItemGroup>
    <AvaloniaXaml Remove="Shared_S-ARGAME.TemplateGame\**" />
    <Compile Remove="Shared_S-ARGAME.TemplateGame\**" />
    <EmbeddedResource Remove="Shared_S-ARGAME.TemplateGame\**" />
    <None Remove="Shared_S-ARGAME.TemplateGame\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="App.config" />
    <None Remove="data\fcd_beziersurface.xml" />
    <None Remove="data\fcd_meshsurface.xml" />
    <None Remove="Images\buttons\cursor.png" />
    <None Remove="Images\buttons\delete.png" />
    <None Remove="Images\buttons\edit.png" />
    <None Remove="Images\buttons\fit.png" />
    <None Remove="Images\buttons\pan.png" />
    <None Remove="Images\buttons\reset.png" />
    <None Remove="Images\buttons\zoom_in.png" />
    <None Remove="Images\buttons\zoom_out.png" />
    <None Remove="Images\embedded\calibration_point.png" />
    <None Remove="Images\embedded\calibration_point_1.png" />
    <None Remove="Images\embedded\calibration_point_10.png" />
    <None Remove="Images\embedded\calibration_point_2.png" />
    <None Remove="Images\embedded\calibration_point_3.png" />
    <None Remove="Images\embedded\calibration_point_4.png" />
    <None Remove="Images\embedded\calibration_point_5.png" />
    <None Remove="Images\embedded\calibration_point_6.png" />
    <None Remove="Images\embedded\calibration_point_7.png" />
    <None Remove="Images\embedded\calibration_point_8.png" />
    <None Remove="Images\embedded\calibration_point_9.png" />
    <None Remove="Images\embedded\image_marker.png" />
    <None Remove="Images\games\calibrator.png" />
    <None Remove="Images\games\ponkanoid.png" />
    <None Remove="Images\games\pumpshoot.png" />
    <None Remove="Images\games\rr_jungle.png" />
    <None Remove="Images\games\rr_neon.png" />
    <None Remove="Images\games\tablerace.png" />
    <None Remove="Images\games\walleroids.png" />
    <None Remove="Images\icon_128.ico" />
    <None Remove="Images\icon_128.png" />
    <None Remove="Images\logo.png" />
    <None Remove="Images\logo_square.png" />
    <None Remove="Images\overlay_new.png" />
    <None Remove="Images\overlay_try.png" />
    <None Remove="Images\screen.png" />
    <None Remove="Images\store_apple.png" />
    <None Remove="Images\store_google.png" />
    <None Remove="Program.cs.txt" />
    <!-- <None Remove="Styles\SideBar.xaml" /> -->
    <None Remove="Styles\Styles.xaml" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <AvaloniaResource Include="Images\buttons\cursor.png" />
    <AvaloniaResource Include="Images\buttons\delete.png" />
    <AvaloniaResource Include="Images\buttons\edit.png" />
    <AvaloniaResource Include="Images\buttons\fit.png" />
    <AvaloniaResource Include="Images\buttons\pan.png" />
    <AvaloniaResource Include="Images\buttons\reset.png" />
    <AvaloniaResource Include="Images\buttons\zoom_in.png" />
    <AvaloniaResource Include="Images\buttons\zoom_out.png" />
    <AvaloniaResource Include="Images\games\calibrator.png" />
    <AvaloniaResource Include="Images\games\ponkanoid.png" />
    <AvaloniaResource Include="Images\games\pumpshoot.png" />
    <AvaloniaResource Include="Images\games\rr_jungle.png" />
    <AvaloniaResource Include="Images\games\rr_neon.png" />
    <AvaloniaResource Include="Images\games\tablerace.png" />
    <AvaloniaResource Include="Images\games\walleroids.png" />
    <AvaloniaResource Include="Images\icon_128.ico" />
    <AvaloniaResource Include="Images\icon_128.png" />
    <AvaloniaResource Include="Images\logo.png" />
    <AvaloniaResource Include="Images\logo_square.png" />
    <AvaloniaResource Include="Images\overlay_new.png" />
    <AvaloniaResource Include="Images\overlay_try.png" />
    <AvaloniaResource Include="Images\screen.png" />
    <AvaloniaResource Include="Images\store_apple.png" />
    <AvaloniaResource Include="Images\store_google.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_1.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_10.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_2.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_3.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_4.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_5.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_6.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_7.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_8.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_9.png" />
    <EmbeddedResource Include="Images\embedded\image_marker.png" />
    <!-- <AvaloniaResource Include="Styles\SideBar.xaml" /> -->
    <AvaloniaResource Include="Styles\Styles.xaml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\.editorconfig" Link=".editorconfig" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.0.10" />
    <PackageReference Include="Avalonia.Controls.ColorPicker" Version="11.0.10" />
    <PackageReference Include="Avalonia.Desktop" Version="11.0.10" />
    <PackageReference Include="Avalonia.Diagnostics" Version="11.0.10" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.0.10" />
    <PackageReference Include="Avalonia.Themes.Simple" Version="11.0.10" />
    <PackageReference Include="Avalonia.Xaml.Behaviors" Version="11.0.10" />
    <PackageReference Include="Avalonia.Xaml.Interactivity" Version="11.0.10" />
    <PackageReference Include="CliWrap" Version="3.6.6" />
    <PackageReference Include="Eziriz.Reactor.TrimHelper" Version="6.9.0" />
    <PackageReference Include="DialogHost.Avalonia" Version="0.7.7" />
    <PackageReference Include="Fody" Version="6.8.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="HomographySharp" Version="3.3.0" />
    <PackageReference Include="MessageBox.Avalonia" Version="*******" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PropertyChanged.Fody" Version="4.1.0" />
    <PackageReference Include="Sentry" Version="4.6.2" />
    <PackageReference Include="SukiUI" Version="5.3.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="_Controls\BoolCardsField.axaml.cs">
      <DependentUpon>BoolCardsField.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Controls\InfoBox.axaml.cs">
      <DependentUpon>InfoBox.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Controls\ChoiceSelectorField.axaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>ChoiceSelectorField.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\CalibrationProcessing.axaml.cs">
      <DependentUpon>CalibrationProcessing.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\CalibrationChooser.axaml.cs">
      <DependentUpon>CalibrationChooser.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\StructureEffectAddDialog.axaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>StructureEffectAddDialog.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\ProgressDialog.axaml.cs">
      <DependentUpon>ProgressDialog.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\MonitorChooser.axaml.cs">
      <DependentUpon>MonitorChooser.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Dialogs\ScenarioChooser.axaml.cs">
      <DependentUpon>ScenarioChooser.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\ScreenCalibrationEditor.axaml.cs">
      <DependentUpon>ScreenCalibrationEditor.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\GameSettings.axaml.cs">
      <SubType>Code</SubType>
      <DependentUpon>GameSettings.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\SupportPage.axaml.cs">
      <DependentUpon>SupportPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\TutorialsPage.axaml.cs">
      <DependentUpon>TutorialsPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\ToolsPage.axaml.cs">
      <DependentUpon>ToolsPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\LicensePage.axaml.cs">
      <DependentUpon>LicensePage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\StorePage.axaml.cs">
      <DependentUpon>StorePage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\LevelDesignPage.axaml.cs">
      <DependentUpon>LevelDesignPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\ProjectionPage.axaml.cs">
      <DependentUpon>ProjectionPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\ImageCalibrationEditor.axaml.cs">
      <DependentUpon>ImageCalibrationEditor.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\DashboardPage.axaml.cs">
      <DependentUpon>DashboardPage.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\ScreenEditorPanel.axaml.cs">
      <DependentUpon>ScreenEditorPanel.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\ScreenRegionEditor.axaml.cs">
      <DependentUpon>ScreenRegionEditor.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Pages\SukiUI_Custom\DesktopPage2.axaml.cs">
      <DependentUpon>DesktopPage2.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Controls\UserBitmap.axaml.cs">
      <DependentUpon>UserBitmap.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\LevelEditor.axaml.cs">
      <DependentUpon>LevelEditor.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\ScreenEditor.axaml.cs">
      <DependentUpon>ScreenEditor.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Editors\SKSceneUserControl.axaml.cs">
      <DependentUpon>SKSceneUserControl.axaml</DependentUpon>
    </Compile>
    <Compile Update="MainView.axaml.cs">
      <DependentUpon>MainView.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Windows\MessageBox.axaml.cs">
      <DependentUpon>MessageBox.axaml</DependentUpon>
    </Compile>
    <Compile Update="_Windows\ModuleInfoWindow.axaml.cs">
      <DependentUpon>ModuleInfoWindow.axaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Update="WireframeEditor.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="data\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PWGClient_Core\PWGClient_Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="LicenseActivatorLib_Net_Standard">
      <HintPath>..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\LicenseActivatorLib_Net_Standard.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <AvaloniaXaml Update="_Controls\ChoiceSelectorField.axaml">
      <SubType>Designer</SubType>
    </AvaloniaXaml>
    <AvaloniaXaml Update="_Dialogs\StructureEffectAddDialog.axaml">
      <SubType>Designer</SubType>
    </AvaloniaXaml>
    <AvaloniaXaml Update="_Pages\GameSettings.axaml">
      <SubType>Designer</SubType>
    </AvaloniaXaml>
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio><UserProperties Reactor_Commands="" Reactor_Configuration="Release" Reactor_Deploy="0" Reactor_Enabled="0" Reactor_Output="&lt;AssemblyLocation&gt;\&lt;AssemblyFileName&gt;" Reactor_Project="C:\G\Codice\TABULA.PMCORE\S-ARGAME.Editor\Reactor_PWGClient.nrproj" /></VisualStudio>
  </ProjectExtensions>
</Project>