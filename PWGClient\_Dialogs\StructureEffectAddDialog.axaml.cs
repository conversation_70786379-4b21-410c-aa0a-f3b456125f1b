using Avalonia.Controls;
using Avalonia.Interactivity;
using Org.BouncyCastle.Asn1.Cmp;
using PropertyChanged;
using Sentry;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class StructureEffectAddDialog : UserControl
	{
		public string dialog_identifier = "structure_effect_add_dialog";

		public StructureEffectAddDialog()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			// Now entities are retrieved once on connect, in _connect_to_server()

			base.OnLoaded(e);
		}



		public static async Task<EntityCreate> Show()
		{
			var ret = await DialogHostAvalonia.DialogHost.Show( new StructureEffectAddDialog());

			return ret as EntityCreate;
		}
		

		async void bt_closedialog_click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", null);
		}

		async void bt_choose_Click(object sender, RoutedEventArgs args)
		{
			var ec = (sender as Control).DataContext as EntityCreate;

			DialogHostAvalonia.DialogHost.Close("dialog", ec);
		}
	}
}
