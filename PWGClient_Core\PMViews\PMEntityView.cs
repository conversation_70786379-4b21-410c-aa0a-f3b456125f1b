﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Xaml.Interactions.Custom;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

namespace Tabula.PMCore
{
    public class PMEntityView : PMView<Tabula.PMCore.Entity, Tabula.PMCore.EntityView>
    {
        bool HasRequestedSync = false;
        bool HasSynced = true;  // test without sync

        public PMEntityView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }

        private SKSprite sprite;

        // Some important fields are sent with fast-update, they are not received in OnUpdate
        public bool FieldsAreDirty
        {
            get => Model.position.__is_dirty || Model.size.__is_dirty || Model.offset.__is_dirty;
            set
            {
                if (!value)
                { 
                    Model.position.ResetDirty();
                    Model.size.ResetDirty();
                    Model.offset.ResetDirty();
                }
            }
        }

        public override void CreateVisual()
        {
            // Support re-creating visual
            if (Visual != null)
            {
                Scene.Remove(Visual);
                Visual = null;
            }


            // get icon from model            
            byte[] icon = !string.IsNullOrEmpty(Model.icon) ? SARGAME.iMainWindowUI.GetIcon(Model.icon) : null;

            if (Model.flags.HasFlag(Entity.Flags.HasVisual))
            {
                if (!Model.flags.HasFlag(Entity.Flags.ShowAsZone) && Model.icon != null && icon != null)
                {
                    // Shows the bounding box and a scaled icon

                    sprite = SKSprite.CreateFromBytes(Scene, new SKRect(0, 0, 30, 30), icon);

                    sprite.DrawFlag = SKSprite.DrawFlags.DrawBoundsIfSelected;

                    // NOTE: fit bounds and align should not be needed anymore now that rotation is used
                    if (Model.flags.HasFlag(Entity.Flags.IconFit))
                    {
                        sprite.FitInBounds = true;
                        sprite.FitAlign = SKHelpers.Align.Bottom;
                    }
                    else
						sprite.FitInBounds = false;

					sprite.Color = new SKColor(0, 255, 0, 255);
                    sprite.ColorHover = new SKColor(0, 0, 255, 255);
                    sprite.ColorDragging = new SKColor(0, 255, 0, 150);

                    Visual = sprite;
                }
                else
                {
                    // Shows the bounding box filled of the zone, the name and the unscaled icon

                    // fallback to color and label
                    sprite = SKSprite.Create(Scene, new SKRect(0, 0, 30, 30));

                    sprite.DrawFlag = SKSprite.DrawFlags.DrawBounds;
                    //sprite.FitInBounds = true;
                    //sprite.FitAlign = SKHelpers.Align.Bottom;

                    sprite.Color = new SKColor(0, 255, 0, 100);
                    sprite.ColorHover = new SKColor(0, 0, 255, 255);
                    sprite.ColorDragging = new SKColor(0, 255, 0, 150);

                    if (!string.IsNullOrEmpty(Model.name_desc))
                        sprite.LabelText = Model.name_desc;
                    else
                        sprite.LabelText = Model.name.Replace("#", "");

                    sprite.LabelColor = SKColors.White;
                    sprite.LabelSize = 18;
                    sprite.LabelAlign = SKTextAlign.Center;

                    Visual = sprite;

                    /*
                    Visual = new SKCircle(Scene, new SKPoint(0, 0), 10)
                    {
                        Color = new SKColor(0, 255, 0, 255),
                        ColorHover = new SKColor(0, 0, 255, 255),
                        ColorDragging = new SKColor(0, 255, 0, 150)
                    };
                    */
                }
            }

            if (Visual != null)
            {

                Visual.onMove += (o, pos) =>
                {
                    if (View.position == null)
                        return;

                    var center = Visual.GetPosition();

                    View.CommitBegin();
                    View.position.x = center.X;
                    View.position.y = center.Y;
                    View.CommitEnd();
                };

                Visual.onBeforeUpdate += (o) =>
                {
                    if (Model.flags.HasFlag(Entity.Flags.ShowWhenSelected))
                        Visual.IsDrawnInScene = View.IsSelected;
                    else
                        Visual.IsDrawnInScene = HasSynced;

                    // Support for rotation  (0..1 mapped to -180 : 180
                    if (Visual is SKSprite)
                    {
                        try
                        {
                            if (View.rotation != null && View.rotation != 0)
                            {
                                var sprite = Visual as SKSprite;
                                sprite.Rotation = -180 + (View.rotation * 360f);
                            }
                        }
                        catch(Exception ex)
                        { }
                    }
				};

                // While dragging edit mode is set
                // TODO: retain global edit mode state

                Visual.onDragStart += (o) =>
                {
                    View.Root.ChangeModeToEdit();
                };

                Visual.onDragEnd += (o) =>
                {
                    View.Root.ChangeModeToPlay();
                };

                // open object menu
                Visual.OnMouseClicked += (o,button) =>
                {
                    // Will open depending on the underlyig wrapper/entity
                    if (button == SKScene.MouseMiddleButton)
					{
                        SARGAME.iMainWindowUI.OpenObjectMenu(this);
					}
				};
            }

            base.CreateVisual();
        }
        
        //NOTE: now size, offset, position are arriving as fast updates, they don't trigger OnUpdate()
        // only when Sync() is requested
        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            Debug.WriteLine($"PMEntityView OnUpdate(): from_server={from_server} fieldname={fieldname}");

            // On first update, notify the server we need important data (positione ect.) to be refreshed
            /*
            if (!HasRequestedSync)
            {
                View.Sync();    // request sync from server
				HasRequestedSync = true;
				//Debug.WriteLine($"PMEntityView BeforeVisualUpdate(): pos=({View.position.x},{View.position.y}) size=({View.size.width},{View.size.height}) offset=({View.offset.x},{View.offset.y}");
			}            
            */

            // Updates come for each field, let's just invalidate
            if (from_server)
            {
                HasSynced = true;
                VisualIsDirty = true;

				SARGAME.iMainWindowUI.EntityMessagesCount++;
            }

            if (from_server)
                switch (fieldname)
                {
                    case nameof(Model.icon):
                        // changed the icon, re-create the visual
                        CreateVisual();
                        break;
                }

            return true;
        }

		public override void BeforeVisualUpdate()
		{
            //NOTE: we are directly receiving posizion,size,offset as fast updates, so we check for dirty
            if (VisualIsDirty || FieldsAreDirty)
            {
				Debug.WriteLine($"PMEntityView BeforeVisualUpdate(): pos=({View.position.x},{View.position.y}) size=({View.size.width},{View.size.height}) offset=({View.offset.x},{View.offset.y}");

				// NOTE: using View properties will ensure they are created, and can sync with data, before i used Model..
				var pos = View.position;
                var size = View.size;
                var offset = View.offset;

                if (pos != null)
                    Visual.SetPosition(new SKPoint(pos.x, pos.y));
                if (size != null && (size.width > 0 && size.height > 0))
                    Visual.SetSize(new SKSize(size.width, size.height));
                if (offset != null && (offset.x != 0 || offset.y != 0))
                    sprite?.SetOffset(new SKPoint(offset.x, offset.y));

                VisualIsDirty = false;
                FieldsAreDirty = false;
            }

			base.BeforeVisualUpdate();
		}


		// Moved here after refactoring
		#region Entity Create

		public static async Task<Entity> CreateEntityOnServer(EntityCreate entity_create, float pos_x=0, float pos_y=0, long parent_guid=-1, bool add_to_recent = true)
		{
			try
			{

                SARGAME.iMainWindowUI.SetCursorWait();

				Entity e = null;

                if (entity_create.category == "structure_effects")
                {
                    e = new StructureEffect()
                    {
                        name = entity_create.type,  // TODO: send a name ?
                        type = entity_create.type
                    };
                }
                else
                {
                    e = new Entity()
                    {
                        name = entity_create.type,  // TODO: send a name ?
                        type = entity_create.type,
                        position = new Vector2f(pos_x, pos_y)
                    };
                }

				e.Normalize();

				// Old version, works but notifications arrive not at the same time, created special CreateEntity()
				/*
				var received_e = await Root.AddEntityAsync(e);
				await Task.Delay(100);
				received_e?.GetVisuals()?.First()?.Select();
				*/

				// SARGAME.iMainWindowUI.IsRefreshHierarchyEnabled = false;

				// The received entity is guaranteed to be also valid on client
				var received_e = (Entity)await SARGAME.iMainWindowUI.CreateEntityOnServer(e, parent_guid, add_to_recent);

                if (received_e != null)
                {
                    if (received_e is StructureEffect)
                    {
                        // Nothing to do
                    }
                    else if (received_e is Entity)
                    { 
                        // Wait the PMView only if the entity has a visual
                        if (received_e.flags.HasFlag(Entity.Flags.HasVisual))
                        {
                            var new_pmview = await PMViewExt.WaitForPMViewWithGuid<PMEntityView>(received_e.__guid);

                            if (new_pmview == null)
                            {
                                received_e = null;  // it was received, but we return null because of missing PMView
								goto exit_create_entityonserver;
                            }

                            new_pmview?.GetVisual().Select();
                        }

                        // TEST: reinitialize all views like on model receive
                        // Without this deleting a vertex resulted in misalignement
                        // TODO: probably ALL new created entities should have views reinitialized
                        //Tabula.SharedObjectMap.SharedObjectMap.InitializeAllViews();

                        //SARGAME.iMainWindowUI.IsRefreshHierarchyEnabled = true;
                        SARGAME.iMainWindowUI.RefreshHierarchy(received_e.getView());
                    }
                }
                else
                {
                   // will return null
                }

                exit_create_entityonserver:

                SARGAME.iMainWindowUI.IsRefreshHierarchyEnabled = true;

				SARGAME.iMainWindowUI.SetCursorDefault();

				return received_e;
			}
			catch (Exception ex)
			{
				LocalAppConfig.Instance?.logException("PMEntityView.CreateEntityOnServer()", ex);
				SARGAME.iMainWindowUI.SetCursorDefault();
				return null;
			}
		}

		#endregion
	}
}
