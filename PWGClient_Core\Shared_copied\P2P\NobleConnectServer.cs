using NobleConnect;
using System;
using System.ComponentModel;
using System.Threading;
using Timer = System.Timers.Timer;
using NobleConnect.Ice;

namespace NobleConnectLib
{
    public class NobleConnectServer //: ISynchronizeInvoke
    {
        public Peer peer;
        
        public int localServerPort;
        
        private Action<string, ushort>? _onReadyToHost;
        public bool needAbort = false;

        private int updateRateMs;
        private Timer? updateTimer;

        public NobleConnectServer(int _localServerPort, IceConfig iceConfig, Logger.Level logLevel, int _updateRateMs)
        {
            Console.WriteLine("[SERVER] Initializing Peer...");
            localServerPort = _localServerPort;
            updateRateMs = _updateRateMs;
            Console.WriteLine($"[SERVER] New peer on thread: {Thread.CurrentThread.ManagedThreadId}");
            peer = new Peer(iceConfig);
            peer.PrepareToConnect();
            
            Logger.logger = delegate(string s) { Console.WriteLine($"[SERVER]: {s} "); };
            Logger.warnLogger = delegate(string s) { Console.WriteLine($"[SERVER]: {s} "); };
            Logger.errorLogger = delegate(string s) { Console.WriteLine($"[SERVER]: {s} "); };
            Logger.logLevel = logLevel;
        }
        
        public void Initialize(CancellationToken token, Action<string, ushort>? onReadyToHost)
        {
            Console.WriteLine($"[SERVER] Initializing hosting on local port: {localServerPort}");
            _onReadyToHost = onReadyToHost;

            peer.InitializeHosting(localServerPort, OnServerPrepared);
            
            /*
            updateTimer = new Timer(200);
            updateTimer.AutoReset = true;
            //updateTimer.SynchronizingObject = this;
            updateTimer.Elapsed += (sender, args) =>
            {
                if (!token.IsCancellationRequested)
                {
                    Console.WriteLine($"[SERVER] Updating peer on thread: {Thread.CurrentThread.ManagedThreadId}");
                    peer.Update();
                }
                else
                {
                    updateTimer.Stop();
                }
            }; 
            updateTimer.Start();
            */
            
            while (!token.IsCancellationRequested)
            {
                //Console.WriteLine($"[SERVER] Updating peer on thread: {Thread.CurrentThread.ManagedThreadId}");
                peer.Update();
                Thread.Sleep(updateRateMs);
            }
        }
        
        void OnServerPrepared(string address, ushort port)
        {
            _onReadyToHost?.Invoke(address,port);
        }

        /*
        public IAsyncResult? BeginInvoke(Delegate method, object[] args)
        {
            return null;
        }

        public object? EndInvoke(IAsyncResult result)
        { 
            return null;
        }

        public object? Invoke(Delegate method, object[] args)
        {
            return null;
        }

        public bool InvokeRequired => false;
        */
    }
}