using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using PropertyChanged;
using ReactiveUI;
using System.ComponentModel;
using System.Threading.Tasks;

namespace Tabula.PWGClient
{
	public partial class MessageBox : Window
	{
		public MessageBox()
		{
			InitializeComponent();
		}

		private void InitializeComponent()
		{
			AvaloniaXamlLoader.Load(this);
		}

		#region INotifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
		{
			((IReactiveObject)this).RaisePropertyChanged(args);
		}

		protected void RaisePropertyChanged(string name)
		{
			((IReactiveObject)this).RaisePropertyChanged(name);
		}

		#endregion
	}
}
