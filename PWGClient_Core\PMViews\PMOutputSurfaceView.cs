﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;


namespace Tabula.PMCore
{
    // Extension of the auto-generated view class with methods to call from xaml/GUI
    public partial class OutputSurfaceView : GuidObjectSyncView<OutputSurface>
    {
     
    }

    public class PMOutputSurfaceView : PMEditablePolygonView<Tabula.PMCore.OutputSurface, Tabula.PMCore.OutputSurfaceView>
    {

		public override Tabula.PMCore.OutputSurfaceView View => (Tabula.PMCore.OutputSurfaceView)Model.__view;

		public PMOutputSurfaceView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }

        // This model is recreated when the whole warp is reset
        public override void CreateVisual()
        {
            bool was_selected = Visual!=null ? Visual.IsSelected : false;

            if (Visual != null)
                Visual.Scene.Remove(Visual);

            VisualPoly = new SKFreePolygon(Scene)
            {
                IsDraggable = false,
                MustBeConvex = true,

                Color = new SKColor(0, 0, 100, 255),
                ColorHover = new SKColor(0, 0, 255, 255),
                ColorDragging = new SKColor(255, 255, 255, 150)
            };

            VisualPoly.Center = new SKPoint(0, 0);

            foreach (var v in Model.vertices)
                VisualPoly.AddSegment(new SKPoint(v.x, v.y), SKFreePolygon.SegmentType.Line);

            VisualPoly.ToggleSegmentsHitVisibility(false);

            // TODO: onMove dei singoli segmenti
            VisualPoly.OnVertexMove += OnVertexMove;
            //VisualPoly.OnVertexAdd += OnVertexAdd;
            //VisualPoly.OnVertexRemove += OnVertexRemove;

            Visual = VisualPoly;

            Visual.onMove += (o, pos) => OnMove(pos);

            VisualPoly.onKey += OnKey;
            Visual.onSelected += OnSelected;
            Visual.onUnSelected += OnUnSelected;

            if (was_selected)
                Visual.Scene.SelectObject(Visual);

            // TEST: try to initialize views
            //OnMove(SKPoint.Empty);


			base.CreateVisual();
        }

        public override void BeforeVisualUpdate()
        {
            // if it is dirty reconfigure the visual
            if (VisualIsDirty)
            {
				foreach (var v in Model.vertices)
					VisualPoly.SetVertexPosition(Model.vertices.IndexOf(v), new SKPoint(v.x, v.y));

                VisualPoly.SyncToControlPoints();

				VisualIsDirty = false;
            }

            base.BeforeVisualUpdate();
        }
        
        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            lock (this)
            {
                if (from_server)
                {
                    // every vertex x,y will be notified, so just ask for an update in BeforeVisualUpdate()
                    //if (view.GetParent().GetModel() is OutputSurface) // good example, but not needed
                    {
                        VisualIsDirty = true;
                    }

				}
            }

            return true;
        }
        

        public void OnMove(SKPoint pos)
        {
            var center = Visual.GetPosition();            

            View.CommitBegin();
                for (int i=0; i<View.vertices.Count; i++)
                {                                        
                    VisualPoly.SyncFromControlPoint(i);
                    View.vertices[i].x = (int)VisualPoly.Vertices[i].X + (int) center.X;
                    View.vertices[i].y = (int)VisualPoly.Vertices[i].Y + (int) center.Y;
                }
            View.CommitEnd();
        }

        public void OnVertexMove(SKControlPoint cp, int index, SKPoint pos)
        {
            var center = Visual.GetPosition();

            // optimize syncing only right segment
            VisualPoly.SyncFromControlPoint(index);

            View.CommitBegin();
                View.vertices[index].x = (int) VisualPoly.Vertices[index].X + (int)center.X;
                View.vertices[index].y = (int) VisualPoly.Vertices[index].Y + (int)center.Y;
            View.CommitEnd();            
        }

        public void OnSelected()
        {
            SelectControlPoint(null);
        }

        public void OnUnSelected()
        {
            SelectControlPoint(null);
        }
    }
}
