<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"      
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.ToolsPage">
	<Border Classes="Card" >
		<Grid RowDefinitions="Auto,*">

			<Grid Grid.Row="0" RowDefinitions="Auto,1,Auto">
				<TextBlock Grid.Row="0" Text="Mobile Controller" Classes="h4" FontWeight="Bold" Margin="5,0,0,10" TextAlignment="Left"/>
				<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
				<TextBlock Grid.Row="2" Margin="5,10,0,10">
					In order to play with the games please connect your <Bold>XBox compatible</Bold> gamepads.<LineBreak/>
					You can also play with your <Bold>mobile device</Bold> installing the free <Bold>Mobile Controller App</Bold> to play over the network.<LineBreak/>
					Once started insert the <Bold>Join Code</Bold> and start playing.
				</TextBlock>
				
			</Grid>

			<StackPanel Grid.Row="1" Orientation="Horizontal" VerticalAlignment="Top" Margin="0,0,0,0">
				<StackPanel.Styles>
					<Style Selector="Button">
						<Setter Property="Command" Value="{Binding OpenURLCommand}" />
					</Style>
					<Style Selector="Button">
						<Setter Property="Height" Value="80" />
						<Setter Property="Margin" Value="10" />
						<Setter Property="Padding" Value="10" />
					</Style>
				</StackPanel.Styles>
					<Button Classes="Basic" Grid.Row="0" BorderThickness="0" BorderBrush="Transparent" Click="bt_store_apple">
						<Grid>
							<Image  Source="/Images/store_apple.png" RenderOptions.BitmapInterpolationMode="HighQuality" />
						</Grid>
					</Button>		
				
					<Button  Classes="Basic" Grid.Row="0" BorderThickness="0" BorderBrush="Transparent" Click="bt_store_google">
						<Grid>
							<Image  Source="/Images/store_google.png" RenderOptions.BitmapInterpolationMode="HighQuality" />
						</Grid>
					</Button>
			</StackPanel>
			
		</Grid>
	</Border>
	
</UserControl>
