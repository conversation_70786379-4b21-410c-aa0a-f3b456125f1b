using Avalonia.Controls;
using Avalonia.Interactivity;
using Org.BouncyCastle.Asn1.Cmp;
using PropertyChanged;
using Sentry;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class EntityCreateDialog : UserControl
	{
		public string dialog_identifier = "entitycreate_dialog";

		public EntityCreateDialog()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			// Now entities are retrieved once on connect, in _connect_to_server()

			if (MainWindowUI.Instance.HasRecentObjects)
				tab_objects_recents.IsSelected = true;
			else
			{
				// select the first not empty tab

				if (MainWindowUI.Instance.EntitiesToCreate_Objects.Count > 0)
					tab_objects.IsSelected = true;
				else if (MainWindowUI.Instance.EntitiesToCreate_Characters.Count > 0)
					tab_characters.IsSelected = true;
				else if(MainWindowUI.Instance.EntitiesToCreate_Effects.Count > 0)
					tab_effects.IsSelected = true;
				else if (MainWindowUI.Instance.EntitiesToCreate_Media.Count > 0)
					tab_media.IsSelected = true;
				else if (MainWindowUI.Instance.EntitiesToCreate_Backgrounds.Count > 0)
					tab_backgrounds.IsSelected = true;
			}


			base.OnLoaded(e);
		}



		public static async Task<EntityCreate> Show()
		{
			var ret = await DialogHostAvalonia.DialogHost.Show( new EntityCreateDialog());

			return ret as EntityCreate;
		}
		

		async void bt_closedialog_click(object sender, RoutedEventArgs args)
		{
			DialogHostAvalonia.DialogHost.Close("dialog", null);
		}

		async void bt_choose_Click(object sender, RoutedEventArgs args)
		{
			var ec = (sender as Control).DataContext as EntityCreate;

			DialogHostAvalonia.DialogHost.Close("dialog", ec);
		}
	}
}
