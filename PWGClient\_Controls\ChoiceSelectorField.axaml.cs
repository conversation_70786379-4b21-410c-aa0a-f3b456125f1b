using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Skia;
using ReactiveUI;
using SkiaSharp;
using System.ComponentModel;
using PropertyChanged;
using System.IO;
using Avalonia.LogicalTree;
using Avalonia.Controls.Primitives;
using System;
using Avalonia.Interactivity;
using System.Collections.Generic;
using Tabula.PMCore;

namespace Tabula.PWGClient
{
    [DoNotNotify]
    [AddINotifyPropertyChangedInterface]
    public partial class ChoiceSelectorField : UserControl
    {
        public ChoiceSelectorField()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        async void bt_left(object sender, Avalonia.Input.PointerPressedEventArgs args)
        {
			var view = (ChoiceSelectorFieldView) DataContext;
			view?.Previous();
		}

		async void bt_right(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			var view = (ChoiceSelectorFieldView)DataContext;
            view?.Next();
		}


		/*
        public static readonly StyledProperty<string?> PathProperty =
                    AvaloniaProperty.Register<UserBitmap, string?>(nameof(Path));

        public string? Path
        {
            get => GetValue(PathProperty);
            set => SetValue(PathProperty, value);
        }

        public static readonly StyledProperty<double?> DecodeWidthProperty =
                   AvaloniaProperty.Register<UserBitmap, double?>(nameof(DecodeWidth));

        public double? DecodeWidth
        {
            get => GetValue(DecodeWidthProperty);
            set => SetValue(DecodeWidthProperty, value);
        }

		public static readonly StyledProperty<double?> DecodeHeightProperty =
				   AvaloniaProperty.Register<UserBitmap, double?>(nameof(DecodeHeight));

		public double? DecodeHeight
		{
			get => GetValue(DecodeHeightProperty);
			set => SetValue(DecodeHeightProperty, value);
		}
        */

		protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
		{

			base.OnPropertyChanged(change);
		}

        protected override void OnLoaded(RoutedEventArgs e)
        {
            base.OnLoaded(e);
        }

        
    }
}
