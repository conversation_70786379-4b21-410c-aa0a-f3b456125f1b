using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;
using Tabula.PMCore;
using System.Threading;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using Tabula.Licensing.LicenseActivator;
using Sentry;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class ToolsPage : UserControl
	{
		public ToolsPage()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{

			base.OnLoaded(e);
		}

		async void bt_store_apple(object sender, RoutedEventArgs args)
		{
			App.OpenURL("https://apps.apple.com/it/app/s-argame-controller/id6450183642");
		}

		async void bt_store_google(object sender, RoutedEventArgs args)
		{
			App.OpenURL("https://play.google.com/store/apps/details?id=eu.tabulatouch.sargamecontroller");
		}
	}

	

}
