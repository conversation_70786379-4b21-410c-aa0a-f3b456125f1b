<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
             mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="300" Background="Transparent"
             x:Class="Tabula.PWGClient.ProgressDialog" >
	<Border Classes="Card" Background="{DynamicResource ExpanderBackground}" BorderBrush="{DynamicResource SukiPrimaryColor}">
		<Grid RowDefinitions="Auto, Auto,*,Auto">
			
			<!-- (Optional) Image -->
			<Image Grid.Row="0" Source="/Images/logo.png" Height="100" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="10" IsVisible="{Binding ShowLogo}"/>

			<!-- Main Message -->
			<StackPanel Grid.Row="1" HorizontalAlignment="Stretch">
				<TextBlock Text="{Binding Message}" TextWrapping="Wrap" Margin="10"/>
				<ProgressBar IsIndeterminate="{Binding IsProgressIndeterminate}" Minimum="0" Maximum="100" Value="{Binding ProgressValue}" IsVisible="{Binding ShowProgress}" Margin="10"/>
			</StackPanel>

			<ContentPresenter Grid.Row="2"/>

			<!-- Cancel -->
			<Button Grid.Row="3" Content="{Binding CancelText}" Click="bt_cancel_Click" IsVisible="{Binding ShowCancelButton}">
				<TextBlock Text="{Binding CancelText}"/>
			</Button>

			<!-- Choice -->
			<StackPanel Name="stackpanel_choice" Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Bottom" IsVisible="{Binding ShowChoicePanel}">
				<Button Click="bt_choice_Click" Tag="1" IsVisible="{Binding ShowChoice1Button}">
					<TextBlock Text="{Binding Choice1Text}"/>
				</Button>
				<Button Click="bt_choice_Click" Tag="2" IsVisible="{Binding ShowChoice2Button}">
					<TextBlock Text="{Binding Choice2Text}"/>
				</Button>
				<Button Click="bt_choice_Click" Tag="3" IsVisible="{Binding ShowChoice3Button}">
					<TextBlock Text="{Binding Choice3Text}"/>
				</Button>
				<Button Click="bt_choice_Click" Tag="4" IsVisible="{Binding ShowChoice4Button}">
					<TextBlock Text="{Binding Choice3Text}"/>
				</Button>
			</StackPanel>
		</Grid>
	</Border>
</UserControl>
