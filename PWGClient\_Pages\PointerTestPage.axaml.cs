using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using Avalonia.Input;
using System.Collections.ObjectModel;
using Avalonia.Interactivity;
using System.ComponentModel;
using ReactiveUI;
using Avalonia.VisualTree;

namespace Tabula.PWGClient
{
	[DoNotNotify]
	public partial class PointerTestPage : UserControl
	{
		public class EventList : BasePropertyChanged
		{
			public ObservableCollection<string> events { get; set; } = new ObservableCollection<string>();

			public bool PointerEntered { get; set; } = true;
			public bool PointerMoved { get; set; } = true;
			public bool PointerExited{ get; set; } = true;
			public bool PointerPressed { get; set; } = true;
			public bool PointerReleased { get; set; } = true;
			public bool PointerWheelChanged { get; set; } = true;
			public bool PointerCaptureLost { get; set; } = true;
			public bool Tapped { get; set; } = true;
			public bool DoubleTapped { get; set; } = true;

			public void AddPointerEvent(string event_desc, Visual visual, PointerEventArgs args)
			{
				var pos = args.GetPosition(visual);
			  	var str = $"{event_desc} Id:{args.Pointer.Id} {args.Pointer.Type} {args.GetCurrentPoint(visual).Properties.PointerUpdateKind} Pos:({pos.X:F0},{pos.Y:F0})";

				if (args is PointerWheelEventArgs)
					str += $" Delta:{(args as PointerWheelEventArgs).Delta}";

				 events.Insert(0, str);
				_limit_events();
			}

			public void AddPointerEvent(string event_desc, PointerCaptureLostEventArgs args)
			{
				events.Insert(0, $"{event_desc} Id:{args.Pointer.Id} {args.Pointer.Type} CAPTURE LOST");
				_limit_events();
			}

			public void AddPointerEvent(string desc)
			{
				events.Insert(0, desc);
				_limit_events();
			}

			private void _limit_events()
			{
				if (events.Count > 100)
					events.RemoveAt(events.Count - 1);
			}
		}

		public EventList event_list { get; set; } = new();

		public PointerTestPage()
		{
			InitializeComponent();
		}

		private void InitializeComponent()
		{
			AvaloniaXamlLoader.Load(this);

			PointerEntered += (s, e) => { if (event_list.PointerEntered) event_list.AddPointerEvent(nameof(PointerEntered), this, e); };
			PointerMoved += (s, e) => { if (event_list.PointerMoved) event_list.AddPointerEvent(nameof(PointerMoved), this, e); };
			PointerExited += (s, e) => {	if (event_list.PointerExited)  event_list.AddPointerEvent(nameof(PointerExited), this, e); };

			PointerPressed += (s, e) => { if (event_list.PointerPressed) event_list.AddPointerEvent(nameof(PointerPressed), this, e); };
			PointerReleased += (s, e) => { if (event_list.PointerReleased) event_list.AddPointerEvent(nameof(PointerReleased), this, e); };

			PointerWheelChanged += (s, e) => { if (event_list.PointerWheelChanged) event_list.AddPointerEvent(nameof(PointerWheelChanged), this, e); };

			PointerCaptureLost += (s, e) => { if (event_list.PointerCaptureLost) event_list.AddPointerEvent(nameof(PointerCaptureLost), e); };

			Tapped += (s, e) => { if (event_list.Tapped) event_list.AddPointerEvent(nameof(Tapped)); };
			DoubleTapped += (s, e) => { if (event_list.DoubleTapped) event_list.AddPointerEvent(nameof(DoubleTapped)); };

			DataContext = event_list;
		}

	}

	

}
