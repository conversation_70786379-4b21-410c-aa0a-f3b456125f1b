using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using PropertyChanged;
using ReactiveUI;
using System.ComponentModel;
using System.Threading.Tasks;
using Tabula.PMCore;
using TheArtOfDev.HtmlRenderer.Avalonia;

namespace Tabula.PWGClient
{
	public partial class ModuleInfoWindow : Window
	{
		public ModuleInfoWindow()
		{
			InitializeComponent();
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			// Load html info
			

			var module = MainWindowUI.Instance.SelectedModule;

			if (module == null)
			{
				base.OnLoaded(e);
				return;
			}			
			try
			{
				//var html = await SARGAME.DownloadText("https://sargame-cdn.tabulatouch.com/win_x64/packages/kinetikpack/guides/kinetikplayground.html");

				TheArtOfDev.HtmlRenderer.Core.Handlers.TabulaExtensions.BaseUri = module.GuideFolderPath;
			
				var html = File.ReadAllText(module.GuideHtmlPath);

				html_panel.Text = "";
				html_panel.Text = html;
			}
			catch
			{
				html_panel.Text = "<b style=\"color: white;\">Sorry, module info is not yet available :(</b>";
			}

			if (string.IsNullOrWhiteSpace(html_panel.GetHtml()))
			{
				html_panel.InvalidateMeasure();
				html_panel.InvalidateVisual();
			}

			base.OnLoaded(e);
		}

		#region INotifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
		{
			((IReactiveObject)this).RaisePropertyChanged(args);
		}

		protected void RaisePropertyChanged(string name)
		{
			((IReactiveObject)this).RaisePropertyChanged(name);
		}

		#endregion
	}
}
