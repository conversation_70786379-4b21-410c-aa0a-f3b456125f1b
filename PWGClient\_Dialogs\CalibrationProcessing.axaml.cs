using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Interactivity;
using Avalonia.Threading;
using DynamicData;
using PropertyChanged;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Tabula.Licensing.LicenseActivator;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.Unity;
using Tabula.WebServices.Arguments;

namespace Tabula.PWGClient
{

	[DoNotNotify]
	public partial class CalibrationProcessing : UserControl
	{
		public string dialog_identifier = "calibrationwait_dialog";

		// View
		[AddINotifyPropertyChangedInterface]
		public class View
		{
			public MainWindowUI Main => MainWindowUI.Instance;

			public string Message { get; set; }

			public string AuthCode { get; set; }

			public bool IsAuthCodeVisible { get; set; } = false;
			public bool IsProgressVisible { get; set; } = false;

			public Material.Icons.MaterialIconKind Icon { get; set; }

			public bool StartCalibration = false;	// OK proceed with real calibration ops (authcode is stable)
			
		}

		public enum CalibrationOutcome
		{
			None,

			Success,	// always both image markers and shapes

			Cancel,
			Timeout,

			ErrorInRequest,				// malformed request from client
			ErrorFromServer,			// the server returned generic error
			ErrorInClientProcessing,	// some unmet condition in client
			ErrorNoShapes,				// no shapes have been found
			ErrorCannotDownloadImage	// cannot obtain image
		}

		public class Result
		{
			public CalibrationOutcome	outcome = CalibrationOutcome.None;
			public string				temp_image_path;
			public ImageData			image_data;		// contains also the analysis
		}

		public static Result result;

		public CalibrationProcessing()
		{
			InitializeComponent();
		}

		public static async Task<Result> CalibrationStep1(View view)
		{
			var d = new CalibrationProcessing();
			d.DataContext = view;

			result = new Result();

			Calibration_Response calibration_response;

			_ = Task.Run(async () =>
			{
				// Wait for OK to really start calibration (authcode is stable)
				while (!view.StartCalibration)
					await Task.Delay(100);  // TODO: timeout ?

				// DEBUG: if we requested a re-try, simulate a new upload with the same image			
				if (MainWindowUI.Instance.RetryCalibration)
				{
					var scenario = App.Instance.GetCurrentScenario();
					if (scenario != null && File.Exists(scenario.CalibrationImagePath))
						try
						{
							int upload_result = await Task.Run(() => LicenseActivatorLib.CalibrationImageUpload(view.AuthCode, scenario.CalibrationImagePath));
						}
						catch { }
				}

				DateTime time_check_start = DateTime.Now;

				// STEP 1: receive markers

				int image_ttl = 120;
#if DEBUG
				if (MainWindowUI.Instance.RetryCalibration)
					image_ttl = 1000;
#endif

				while ((DateTime.Now - time_check_start).TotalSeconds < 120)
				{
					calibration_response = LicenseActivatorLib.Calibration(view.AuthCode, 1, null, image_ttl);

					if (calibration_response == null)
					{
						// generic error or connection, just retry
					}
					else if (calibration_response.result <= 0)
					{
						switch (calibration_response.result)
						{
							case Calibration_Response.Result_ERROR_REQUEST:
								// malformed request, useless to retry
								result.outcome = CalibrationOutcome.ErrorInRequest;
								SARGAME.App.logError($"Calibration ERROR, malformed request");
								Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
								return;

							default:
								// just retry
								break;
						}
					}
					else
					{
						if (calibration_response.image_data == null)
						{
							// should never happen
							result.outcome = CalibrationOutcome.ErrorFromServer;
							SARGAME.App.logError($"Calibration ERROR, ErrorFromServer image_data is null");
							Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
							return;
						}

						result.outcome = CalibrationOutcome.Success;
						result.image_data = calibration_response.image_data;

						SARGAME.App.log($"Calibration STEP1, result={calibration_response.result} markes_count={(calibration_response.image_data.ImageMarkers != null ? calibration_response.image_data.ImageMarkers.Count : -1)} ");

						Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
						return;
					}

					await Task.Delay(1000);
				}

				// time expired

				result.outcome = CalibrationOutcome.Timeout;
				SARGAME.App.logError($"Calibration ERROR, timeout");
				Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
			});


			_ = await DialogHostAvalonia.DialogHost.Show(d);

			return result;
		}

		public static async Task<Result> CalibrationStep2(View view, ImageData image_data)
		{
			var d = new CalibrationProcessing();
			d.DataContext = view;

			result = new Result();

			Calibration_Response calibration_response;

			_ = Task.Run(async () =>
			{
				// STEP 2: confirm markers, receive shapes

				// updates the image_data with screen markers and image_markers confirmed by user

				var region = App.Client.Model.GetVisualScreenSize(0);

				image_data.screen_full = new ImageData.ScreenFull()
				{
					width = App.Client.Model.Screen.size.width,
					height = App.Client.Model.Screen.size.height
				};

				image_data.screen_crop = new ImageData.ScreenCrop()
				{
					x = region.x,
					y = region.y,
					width = region.width,
					height = region.height
				};

				// NOTE: these are sorted on the server side!
				image_data.screen_markers = new System.Collections.Generic.List<ImageData.ScreenMarker>();

				foreach (var cp in App.Client.Model.Screen.screen_markers)
				{
					image_data.screen_markers.Add(new ImageData.ScreenMarker()
					{
						x = (int) cp.position.x,
						y = (int) cp.position.y
					});
				}

				// Now, let's update ONLY THE POSITION of the image_data.image_markers based on the model ones.
				// If we enabled more than the one received, the "new" ones will be filled with average color/radius from the others
				
				var enabled_image_markers = (from i in App.Client.View.Screen.image_markers where i.enabled select i);

				if (enabled_image_markers.Count() != 4)
				{
					// strange error, should never happen
					result.outcome = CalibrationOutcome.ErrorInClientProcessing;
					SARGAME.App.logError($"Calibration ERROR, ErrorInClientProcessing enabled markers are not 4");
					Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
					return;
				}

				// update/add  the markers in native format
				List<List<object>> new_image_markers = new List<List<object>>();

				foreach (var im_view in enabled_image_markers)
				{
					// find the index in the native
					int idx = App.Client.View.Screen.image_markers.IndexOf(im_view);

					if (idx < image_data.image_markers.Count)
					{
						// existing image data marker
						var original_im = image_data.image_markers[idx];

						// very low level, update position x,y
						original_im[0] = Convert.ToDouble(im_view.position.x);
						original_im[1] = Convert.ToDouble(im_view.position.y);

						new_image_markers.Add(original_im);
					}
					else
					{
						// new one
						var new_im = new ImageData.ImageMarker()
						{
							position = new ImageData.Point() { x = im_view.position.x, y = im_view.position.y },
							color = new ImageData.Color()
							{
								r = image_data.ImageMarkers.Count > 0 ? (from m in image_data.ImageMarkers select m.color.r).Average() : 1,
								g = image_data.ImageMarkers.Count > 0 ? (from m in image_data.ImageMarkers select m.color.g).Average() : 1,
								b = image_data.ImageMarkers.Count > 0 ? (from m in image_data.ImageMarkers select m.color.b).Average() : 1,
								a = 1
							},
							radius = image_data.ImageMarkers.Count > 0 ? (from m in image_data.ImageMarkers select m.radius).Average() : 0
						};

						var new_im_native = new_im.ToObjectList();

						new_image_markers.Add(new_im_native);
					}
				}

				// always replace with new
				image_data.image_markers = new_image_markers;

				int image_ttl = 120;
#if DEBUG
				if (MainWindowUI.Instance.RetryCalibration)
					image_ttl = 1000;
#endif

				// Call to calibration step 2, this time the call is direct no waiting
				calibration_response = LicenseActivatorLib.Calibration(view.AuthCode, 2, image_data, image_ttl);

				if (calibration_response == null || (calibration_response!=null && calibration_response.result <= 0))
				{
					// ERROR

					// Archive the result, and remove it from the current database so we can calibrate another
					// We need also error cases!
					Task.Run(() => LicenseActivatorLib.CalibrationArchive(view.AuthCode));

					result.outcome = CalibrationOutcome.ErrorFromServer;
					SARGAME.App.logError($"Calibration ERROR, server reported error={calibration_response?.result}");
					Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
					return;
				}
				else
				{
					if (calibration_response.image_data == null)
					{
						// should never happen
						result.outcome = CalibrationOutcome.ErrorFromServer;
						SARGAME.App.logError($"Calibration ERROR(2), ErrorFromServer image_data is null");
						Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
						return;
					}

					result.image_data = calibration_response.image_data;

					// Download the image
					result.temp_image_path = await LicenseActivatorLib.CalibrationImageDownload(view.AuthCode);

					if (result.temp_image_path == null)
					{
						result.outcome = CalibrationOutcome.ErrorCannotDownloadImage;
						SARGAME.App.logError($"Calibration ERROR, cannot download image");

						Task.Run(() => LicenseActivatorLib.CalibrationArchive(view.AuthCode));

						Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
						return;
					}

					// Archive any result, and remove it from the current database so we can calibrate another
					Task.Run(() => LicenseActivatorLib.CalibrationArchive(view.AuthCode));

					// No shapes is threated as an error
					if (result.image_data.Shapes.Count == 0)
						result.outcome = CalibrationOutcome.ErrorNoShapes;
					else
						result.outcome = CalibrationOutcome.Success;    // SUCCESS

					Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
					return;
				}

			});


			_ = await DialogHostAvalonia.DialogHost.Show(d);

			return result;
		}


			/*
			public static async Task<Result> ShowAndWait(View view)
			{
				var d = new CalibrationWait();
				d.DataContext = view;

				result = new Result();

				Calibration_Response calibration_response;

				_ = Task.Run(async () =>
				{
					// Wait for OK to really start calibration (authcode is stable)
					while (!view.StartCalibration)
						await Task.Delay(100);  // TODO: timeout ?

					// DEBUG: if we requested a re-try, simulate a new upload with the same image			
					if (MainWindowUI.Instance.RetryCalibration)
					{
						var scenario = App.Instance.GetCurrentScenario();
						if (scenario != null && File.Exists(scenario.CalibrationImagePath))
							try
							{
								int upload_result = await Task.Run(() => LicenseActivatorLib.CalibrationImageUpload(view.AuthCode, scenario.CalibrationImagePath));								
							}
							catch { }
					}

					DateTime time_check_start = DateTime.Now;

					switch (view.CalibrationMode)
					{


						case CalibrationChooser.CalibrationMode.Automatic:

							// STEP 1: receive markers

							while ((DateTime.Now - time_check_start).TotalSeconds < 120)
							{
								calibration_response = LicenseActivatorLib.Calibration(view.AuthCode, 1);

								if (calibration_response == null)
								{
									// generic error or connection, just retry
								}
								else if (calibration_response.result <= 0)
								{
									switch (calibration_response.result)
									{
										case Calibration_Response.Result_ERROR_REQUEST:
											// malformed request, useless to retry
											result.outcome = CalibrationOutcome.ErrorInRequest;
											SARGAME.App.logError($"Calibration ERROR, malformed request");
											Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
											return;

										default:
											// just retry
											break;
									}
								}
								else
								{
									if (calibration_response.image_data == null)
									{
										// should never happen
										result.outcome = CalibrationOutcome.ErrorFromServer;
										SARGAME.App.logError($"Calibration ERROR, ErrorFromServer image_data is null");
										Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
										return;
									}

									// ImageMarkers have been received
									//calibration_response.image_data.ImageMarkers.c
								}

								await Task.Delay(1000);
							}





							// prepare the image data
							WebServices.Arguments.ImageData image_data = new WebServices.Arguments.ImageData();

							var region = App.Client.Model.GetVisualScreenSize(0);

							image_data.screen_full = new WebServices.Arguments.ImageData.ScreenFull()
							{
								width = App.Client.Model.Screen.size.width,
								height = App.Client.Model.Screen.size.height
							};

							image_data.screen_crop = new WebServices.Arguments.ImageData.ScreenCrop()
							{
								x = region.x,
								y = region.y,
								width = region.width,
								height = region.height
							};

							// markers
							// TODO: now we follow the order, we should calculate based on nearest corner
							image_data.markers = new System.Collections.Generic.List<WebServices.Arguments.ImageData.Marker>();

							foreach (var cp in App.Client.Model.Screen.calibration_points)
							{
								image_data.markers.Add(new WebServices.Arguments.ImageData.Marker()
								{
									x = (int) cp.screen_position.x,
									y = (int) cp.screen_position.y
								});
							}

							while ((DateTime.Now - time_check_start).TotalSeconds < 120)
							{
								// perform the calibration
								calibration_response = LicenseActivatorLib.Calibration(view.AuthCode, image_data);

								if (calibration_response != null)
								{
									if (calibration_response.image_analysis != null)
										SARGAME.App.log($"Calibration result={calibration_response.result} markes_count={(calibration_response.image_analysis.Markers!=null ? calibration_response.image_analysis.Markers.Count : -1)} shapes_count={(calibration_response.image_analysis.Shapes!=null ? calibration_response.image_analysis.Shapes.Count : -1)}");

									if (calibration_response.result == Calibration_Response.Result_COMPLETE)
									{
										result.image_data = image_data;
										result.image_analysis = calibration_response.image_analysis;								

										// NOTE: markers count and shapes are checked after

										// Download the image
										result.temp_image_path = await LicenseActivatorLib.CalibrationImageDownload(view.AuthCode);

										if (result.temp_image_path == null)
										{
											result.outcome = CalibrationOutcome.ErrorCannotDownloadImage;
											SARGAME.App.logError($"Calibration ERROR, cannot download image");
											Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
											return;
										}

										// Archive any result, and remove it from the current database so we can calibrate another
										Task.Run(() => LicenseActivatorLib.CalibrationArchive(view.AuthCode));

										// SUCCESS
										result.outcome = CalibrationOutcome.Success;

										Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
										return;
									}
									else if (calibration_response.result == Calibration_Response.Result_ERROR)
									{
										// Archive the result, and remove it from the current database so we can calibrate another
										// We need also error cases!
										Task.Run(() => LicenseActivatorLib.CalibrationArchive(view.AuthCode));

										result.outcome = CalibrationOutcome.ErrorFromServer;
										SARGAME.App.logError($"Calibration ERROR, server reported");
										Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
										return;
									}								

								}

								await Task.Delay(1000);
							}

							if (result.temp_image_path == null)
							{
								if (result.outcome == CalibrationOutcome.None)
									result.outcome = CalibrationOutcome.Timeout;

								SARGAME.App.logError($"Calibration ERROR, timeout");

								Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close("dialog"));
								return;
							}

							break;
					}
				});


				_ =  await DialogHostAvalonia.DialogHost.Show(d);

				return result;
			}
			*/

		void bt_cancel_Click(object sender, RoutedEventArgs args)
		{
			result.outcome = CalibrationOutcome.Cancel;

			DialogHostAvalonia.DialogHost.Close("dialog");
		}


	}
}
