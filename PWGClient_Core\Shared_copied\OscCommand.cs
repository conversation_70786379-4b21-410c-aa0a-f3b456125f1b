//TABULA_GUID:{344E70DA-5C45-48A9-A8D6-F57E162EFBBB}
using System;
using System.Collections.Generic;
using OSC.NET;
using System.Threading;
using System.Text.RegularExpressions;

// OSCCommand: quick way to send/receive commands using OSC and a retry-packet policy that simulates TCP reliability
/*
 * /tabula/cmd
 *      - bundle can contain any number of messages, but first must be in this format:
 *      (string)    the client unique GUID or identifier
 *      (long)     the command ID, incremented per sendCommand(), and re-sent for each retry
 * 
 */
/** Defines:
 */

/* Version:
 *  1.3 (23/10/16)  the packet is changed to a special first message in the bundle
 *  1.2.1  ThreadAbortException, internal keywords
 *  1.2
 */

namespace Tabula.OscCommand
{
    internal class Client
    {
        private long cmd_id = 0; // the command ID
        private OSCTransmitter oscClient;

        public int RetryCount = 3;  // number of times the command is sent

        private string guid = "";

        // will generate an exception if it cannot connect
        public Client(string address)
        {
            guid = System.Guid.NewGuid().ToString("N"); // local client guid
            oscClient = new OSCTransmitter(address, Server.StandardPort);
        }

        // will generate an exception if it cannot connect
        public Client(string address, int port)
        {
            guid = System.Guid.NewGuid().ToString("N"); // local client guid
            oscClient = new OSCTransmitter(address, port);
        }

        public string GUID
        {
            get { return guid; }
        }

        public void sendCommand(string cmd, List<object> arglist)
        {
            object[] arr = arglist.ToArray();

            sendCommand(cmd, arr);
        }

        /* (old) version          
        public void sendCommand(string cmd, params object[] args)
        {
            // if there are no args, check if passed cmd has inline string arguments
            if (args.Length==0)
            {
                args = getInlineArgs(cmd, ref cmd);
            }

            lock (oscClient)
            {
                 cmd_id++;
                for (int i = 0; i < RetryCount; i++)
                {
                    OSCBundle bnd = new OSCBundle();                    

                    OSCMessage msg = new OSCMessage("/tabula/cmd");
                    msg.Append(guid);   // client GUID
                    msg.Append(cmd);    // command
                    msg.Append(cmd_id); // command seq

                    foreach (object o in args)
                    {
                        msg.Append(o);
                    }

                    bnd.Append(msg);

                    oscClient.Send(bnd);
                }
            }
        }
        */

        public void sendCommand(string cmd, params object[] args)
        {
            // if there are no args, check if passed cmd has inline string arguments
            if (args.Length == 0)
            {
                args = getInlineArgs(cmd, ref cmd); // TODO: to deprecate?
            }

            lock (oscClient)
            {
                cmd_id++;
                for (int i = 0; i < RetryCount; i++)
                {
                    OSCBundle bnd = new OSCBundle();

                    OSCMessage msg = new OSCMessage("/tabula/cmd");
                    msg.Append(guid);   // client GUID                    
                    msg.Append(cmd_id); // command seq

                    bnd.Append(msg);

                    msg = new OSCMessage("/tabula/cmd");
                    msg.Append(cmd);    // command
                    foreach (object o in args)                    
                        msg.Append(o);                    

                    bnd.Append(msg);

                    oscClient.Send(bnd);
                }
            }
        }

        public void sendCommandBatch(List<Tuple<string,object[]>> batch)
        {
            lock (oscClient)
            {
                cmd_id++;
                for (int i = 0; i < RetryCount; i++)
                {
                    OSCBundle bnd = new OSCBundle();

                    OSCMessage msg = new OSCMessage("/tabula/cmd");
                    msg.Append(guid);   // client GUID                    
                    msg.Append(cmd_id); // command seq

                    bnd.Append(msg);

                    foreach (var t in batch)
                    {
                        msg = new OSCMessage("/tabula/cmd");
                        msg.Append(t.Item1);    // command
                        foreach (object o in t.Item2)
                            msg.Append(o);

                        bnd.Append(msg);
                    }

                    oscClient.Send(bnd);
                }
            }
        }

        // static version    
        public static bool sendCommandStatic(string address, string cmd, params object[] args)
        {
            return sendCommandStatic(address, Server.StandardPort, cmd, args);
        }

        public static bool sendCommandStatic(string address, int port, string cmd, params object[] args)
        {
            try
            {
                Client c = new Client(address, port);
                c.sendCommand(cmd, args);
                return true;
            }
            catch { return false; }
        }

        // returns inline args if any [supports 6 args]
        public static object[] getInlineArgs(string cmdstr, ref string isolatedcmd)
        {
            Regex reg = new Regex("^(?<cmd>[^\\s]+)( )?(?<arg1>[^\\s]+)?( )?(?<arg2>[^\\s]+)?( )?(?<arg3>[^\\s]+)?( )?(?<arg4>[^\\s]+)?( )?(?<arg5>[^\\s]+)?( )?(?<arg6>[^\\s]+)?");
            Match m = reg.Match(cmdstr);

            if (m.Success)
            {
                int argnum = 0;
                for (int i = 1; i < 7; i++)
                    if (m.Groups["arg" + i].Value.Length > 0)
                        argnum++;
                object[] arguments = new object[argnum];
                for (int i = 1; i < (argnum + 1); i++)
                    arguments[i - 1] = m.Groups["arg" + i].Value;

                if (isolatedcmd != null)
                    isolatedcmd = m.Groups["cmd"].Value;

                return arguments;
            }
            else
                return null;
        }
    }

    internal class Command
    {
        public string           ClientGUID = "";
        public string           Cmd = "";
        public List<object>     Args = new List<object>();

        public int CommandSequence = 0;

    }

    internal class Server
    {
        public const int StandardPort = 33128;

        private bool connected = false;
        public  int port = 33128;
        private OSCReceiver receiver;
        private Thread thread;

        private Dictionary<string, long> clientCommands = new Dictionary<string, long>();

        //public Action<Command>        onCommand = null;           // (old) a recognizer Command, either reliable or not
        public Action<OSCPacket,int>    onReliableCommandBundle;    // (new) reliable /tabula/cmd bundle processing, process command messages starting from <int>
        public Action<OSCPacket>        onUnreliableCommandBundle;  // (new) unreliable /tabula/osc bundle processing
        public Action<OSCPacket>        onGenericBundle;            // any other received packed with not recognized address (bundle)
        public Action<OSCMessage>       onGenericMessage;           // any other received packed with not recognized address (single message)
        public Action<OSCPacket>        onPacket;                   // very low level, invoked on each bundle
        public Action<string,Exception> onException;                // log exceptions

        public string ReliableCommandAddress = "/tabula/cmd";       // used for reliable commands
        public string CommandAddress = "/tabula/osc";               // used for normal OSC messages (with typed arguments appended)


        public Server() { }

        public Server(int port)
        {
            this.port = port;
        }

        public bool Start()
        {
            try
            {
                receiver = new OSCReceiver(port);
                thread = new Thread(new ThreadStart(listenThread));
                thread.IsBackground = true;
                connected = true;
                thread.Start();                
            }
            catch (Exception)
            {
                // usually the address is taken
                return false;
            }

            return true;
        }

        public void Stop()
        {
            try
            {
                if (receiver != null) receiver.Close();
                receiver = null;
            }
            catch (Exception ex)
            {
                onException?.Invoke("OscCommand.Server.Stop", ex);
            }

            connected = false;
            if (thread != null)
                thread.Abort();
        }

        // sends a command to itself
        /*
        public void selfCommand(string cmd)
        {
            selfCommand(cmd, new List<object>());
        }

        public void selfCommand(string cmd, List<object> args)
        {
            if (onCommand == null)
                return;

            Command c = new Command();
            c.ClientGUID = "self";
            c.Cmd = cmd;
            c.Args = args;

            onCommand(c);
        }
        */

        public bool isConnected() { return connected; }

        private void listenThread()
        {            
            while (connected)
            {
                try
                {
                    OSCPacket packet = receiver.Receive();
                    if (packet != null)
                    {
                        if (onPacket != null)
                            onPacket(packet);

                        if (packet.IsBundle())
                        {
                            // NOTE: Reliable messaging needs a Bundle (first message is the reliable header)
                            // For non-standard bundles onGenericBundle will be called
                            processBundle(packet);
                        }
                        else
                        {
                            // Any other non-bundle message will be routed to onGenericMessage
                            processMessage(packet);
                        }

                    }

                }
                catch (ThreadAbortException e)
                {
                    // nothing
                }
                catch (Exception e)
                {
                    onException?.Invoke("OscCommand.Server.listenThread", e);
                }
            }
        }

        // Original version: each message is its own bundle and is tagget with client guid and command sequence
        /*
        private void processBundle(OSCPacket packet)
        {
            ArrayList messages = packet.Values;

            lock (clientCommands)
            {

                for (int i = 0; i < messages.Count; i++)
                {
                    OSCMessage message = (OSCMessage)messages[i];
                    string address = message.Address;
                    ArrayList args = message.Values;

                    // Reliable OSC Command implementation
                    // Adds extra ClientGUID and CommandSequence

                    if (address == ReliableCommandAddress)  // usually /tabula/cmd
                    {
                        Command cmd = new Command();

                        cmd.ClientGUID = (string)args[0];
                        cmd.Cmd = (string)args[1];
                        cmd.CommandSequence = (int)args[2];

                        // check if command has already been received/processed and discards previous sequence commands
                        if (clientCommands.ContainsKey(cmd.ClientGUID))
                        {
                            int last_cmd_seq = clientCommands[cmd.ClientGUID];
                            clientCommands[cmd.ClientGUID] = cmd.CommandSequence;
                            if (last_cmd_seq >= cmd.CommandSequence)
                                continue;
                        }
                        else
                            clientCommands.Add(cmd.ClientGUID, cmd.CommandSequence);

                        for (int a = 3; a < args.Count; a++)
                            cmd.Args.Add(args[a]);

#if DEBUG
                        System.Diagnostics.Debug.WriteLine(string.Format("OscCommand.ReliableCommand {0} args_count={1} client={2} seq={3}", cmd.Cmd, cmd.Args.Count, cmd.ClientGUID, cmd.CommandSequence));
#endif

                        if (onCommand != null)
                            onCommand(cmd);
                    }
                    else if (address == CommandAddress)  // usually /tabula/osc
                    {
                        Command cmd = new Command();

                        cmd.Cmd = (string) args[0];

                        for (int a = 1; a < args.Count; a++)
                            cmd.Args.Add(args[a]);

#if DEBUG
                        System.Diagnostics.Debug.WriteLine(string.Format("OscCommand {0} args_count={1}", cmd.Cmd, cmd.Args.Count));
#endif

                        if (onCommand != null)
                            onCommand(cmd);
                    }
                    else
                    {
                        // Generic command address, let's route it to a custom handler
                        onGenericMessage?.Invoke(message);
                    }

                }               
            }
        }*/

        // New version: each bundle can contain more messages, the first message is always a special one with guid/sequence
        // This way batch messages can be created
        private void processBundle(OSCPacket packet)
        {
            lock (clientCommands)
            {
                //for (int i = 0; i < packet.Values.Count; i++)          
                
                // Convention: all messages in a bundle are determined by the first one                      
                OSCMessage message = (OSCMessage)packet.Values[0];

                if (message.Address == ReliableCommandAddress)  // usually /tabula/cmd
                {
                    // Reliable OSC Command implementation with first message containin ClientGUID and CommandSequence
                    // Note: there must at least 2 messages
                    if (packet.Values.Count <= 1)
                        return;

                    string  client_guid =   (string) message.Values[0];
                    long   client_seq =    (long) message.Values[1];

                    if (clientCommands.ContainsKey(client_guid))
                    {
                        long last_cmd_seq = clientCommands[client_guid];
                        clientCommands[client_guid] = client_seq;
                        if (last_cmd_seq >= client_seq)
                            return;
                    }
                    else
                        clientCommands.Add(client_guid, client_seq);

                    // Process all bundle as reliable with messages starting from 1
                    onReliableCommandBundle?.Invoke(packet, 1);
                }
                else if (message.Address == CommandAddress)  // usually /tabula/osc
                {
                    // Process all bundle as unreliable with all messages
                    onUnreliableCommandBundle?.Invoke(packet);
                }
                else
                {
                    // Generic command address, let's route it to a custom handler
                    onGenericBundle?.Invoke(packet);
                }

                
            }
        }

        // for messages that arrive and are not bundles (this is NOT for reliable commands)
        private void processMessage(OSCPacket packet)
        {
            lock (clientCommands)
            {
                // Convention: all messages in a bundle are determined by the first one                      
                OSCMessage message = (OSCMessage) packet;

                /*
                if (message.Address == ReliableCommandAddress)  // usually /tabula/cmd
                {
                    // Reliable OSC Command implementation with first message containin ClientGUID and CommandSequence
                    // Note: there must at least 2 messages
                    if (packet.Values.Count <= 1)
                        return;

                    string client_guid = (string)message.Values[0];
                    long client_seq = (long)message.Values[1];

                    if (clientCommands.ContainsKey(client_guid))
                    {
                        long last_cmd_seq = clientCommands[client_guid];
                        clientCommands[client_guid] = client_seq;
                        if (last_cmd_seq >= client_seq)
                            return;
                    }
                    else
                        clientCommands.Add(client_guid, client_seq);

                    // Process all bundle as reliable with messages starting from 1
                    onReliableCommandBundle?.Invoke(packet, 1);
                }
                else if (message.Address == CommandAddress)  // usually /tabula/osc
                {
                    // Process all bundle as unreliable with all messages
                    onUnreliableCommandBundle?.Invoke(packet);
                }
                else
                */
                {
                    // Generic command address, let's route it to a custom handler
                    onGenericMessage?.Invoke(message);
                }


            }
        }


    }
}