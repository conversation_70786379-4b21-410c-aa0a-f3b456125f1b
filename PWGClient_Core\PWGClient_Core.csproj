﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net7.0</TargetFramework>
    <AssemblyName>PWGClient_Core</AssemblyName>
    <RootNamespace>Tabula.PMCore</RootNamespace>
    <Platforms>AnyCPU;ARM64</Platforms>
    <Configurations>Debug;Release;Beta Release</Configurations>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>BETA_RELEASE;SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>SARGAME_EDITOR;SHAREDOBJECTMAP_CLIENT;SHAREDOBJECTMAP_AVALONIA;NETREACTOR;HID;TABULAWEBSERVICES_CLIENT</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|ARM64'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <AvaloniaNameGeneratorBehavior>InitializeComponent</AvaloniaNameGeneratorBehavior>
    <AvaloniaNameGeneratorDefaultFieldModifier>internal</AvaloniaNameGeneratorDefaultFieldModifier>
    <AvaloniaNameGeneratorFilterByPath>*</AvaloniaNameGeneratorFilterByPath>
    <AvaloniaNameGeneratorFilterByNamespace>*</AvaloniaNameGeneratorFilterByNamespace>
    <AvaloniaNameGeneratorViewFileNamingStrategy>NamespaceAndClassName</AvaloniaNameGeneratorViewFileNamingStrategy>
  </PropertyGroup>
  <PropertyGroup>
    <ReactorLocation>"C:\Program Files (x86)\Eziriz\.NET Reactor\dotNET_Reactor.Console.exe"</ReactorLocation>
    <ReactorProject>"C:\G\Codice\TABULA.PMCORE\S-ARGAME.Editor\Reactor_PWGClient_Core.nrproj"</ReactorProject>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="SKRenderGraph\SKSceneWPF.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\sukiui\5.3.0\contentFiles\any\netstandard2.0\suki_photo.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="App.config" />
    <None Remove="data\fcd_beziersurface.xml" />
    <None Remove="data\fcd_meshsurface.xml" />
    <None Remove="Images\embedded\calibration_point.png" />
    <None Remove="Images\embedded\calibration_point_1.png" />
    <None Remove="Images\embedded\calibration_point_10.png" />
    <None Remove="Images\embedded\calibration_point_2.png" />
    <None Remove="Images\embedded\calibration_point_3.png" />
    <None Remove="Images\embedded\calibration_point_4.png" />
    <None Remove="Images\embedded\calibration_point_5.png" />
    <None Remove="Images\embedded\calibration_point_6.png" />
    <None Remove="Images\embedded\calibration_point_7.png" />
    <None Remove="Images\embedded\calibration_point_8.png" />
    <None Remove="Images\embedded\calibration_point_9.png" />
    <None Remove="Images\embedded\image_marker.png" />
    <None Remove="Program.cs.txt" />
    <!-- <None Remove="Styles\SideBar.xaml" /> -->
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\_PM_Core\generated\api_generated_rpc.cs" Link="Shared_S-ARGAME.TemplateGame\api_generated_rpc.cs" />
    <Compile Include="..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\_PM_Core\generated\PMCore_Model.cs" Link="Shared_S-ARGAME.TemplateGame\PMCore_Model.cs" />
    <Compile Include="..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\_PM_Core\generated\view_generated.cs" Link="Shared_S-ARGAME.TemplateGame\view_generated.cs" />
    <Compile Include="..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\_PM_Core\generated\wrapper_generated.cs" Link="Shared_S-ARGAME.TemplateGame\wrapper_generated.cs" />
    <Compile Include="..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\_PM_Core\PlayerControllerMessage.cs" Link="Shared_S-ARGAME.TemplateGame\PlayerControllerMessage.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Images\embedded\calibration_point.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_1.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_10.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_2.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_3.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_4.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_5.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_6.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_7.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_8.png" />
    <EmbeddedResource Include="Images\embedded\calibration_point_9.png" />
    <EmbeddedResource Include="Images\embedded\image_marker.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\.editorconfig" Link=".editorconfig" />
    <None Include="SKRenderGraph\SKSceneWPF.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.0.10" />
    <PackageReference Include="Avalonia.Controls.ColorPicker" Version="11.0.10" />
    <PackageReference Include="Avalonia.Desktop" Version="11.0.10" />
    <PackageReference Include="Avalonia.Diagnostics" Version="11.0.10" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.0.10" />
    <PackageReference Include="Avalonia.Themes.Simple" Version="11.0.10" />
    <PackageReference Include="Avalonia.Xaml.Behaviors" Version="11.0.10" />
    <PackageReference Include="Avalonia.Xaml.Interactivity" Version="11.0.10" />
    <PackageReference Include="CliWrap" Version="3.6.6" />
    <PackageReference Include="Eziriz.Reactor.TrimHelper" Version="6.9.0" />
    <PackageReference Include="DialogHost.Avalonia" Version="0.7.7" />
    <PackageReference Include="Fody" Version="6.8.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="HomographySharp" Version="3.3.0" />
    <PackageReference Include="MessageBox.Avalonia" Version="*******" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PropertyChanged.Fody" Version="4.1.0" />
    <PackageReference Include="Sentry" Version="4.6.2" />
    <PackageReference Include="SukiUI" Version="5.3.0" />
  </ItemGroup>
  <ItemGroup>
    <None Update="WireframeEditor.xaml">
      <Generator>MSBuild:Compile</Generator>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="data\" />
    <Folder Include="Shared_S-ARGAME.TemplateGame\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\_PUBLIC\NetSparkle\src\NetSparkle.UI.Avalonia\NetSparkle.UI.Avalonia.csproj" />
    <ProjectReference Include="..\..\..\_PUBLIC\NetSparkle\src\NetSparkle\NetSparkle.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="LicenseActivatorLib_Net_Standard">
      <HintPath>..\..\S-ARGAME.TemplateGame\Assets\_SARGAME\_Core\LicenseActivatorLib_Net_Standard.dll</HintPath>
    </Reference>
    <Reference Include="NobleConnect">
      <HintPath>Shared_copied\P2P\NobleConnect.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties Reactor_Commands="" Reactor_Configuration="Release" Reactor_Deploy="1" Reactor_Enabled="1" Reactor_Output="&lt;AssemblyLocation&gt;\&lt;AssemblyFileName&gt;" Reactor_Project="C:\G\Codice\TABULA.PMCORE\S-ARGAME.Editor\Reactor_PWGClient_Core.nrproj" />
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="ReactorCall" AfterTargets="Compile">
    <Exec Command="$(ReactorLocation) -file &quot;$(ProjectDir)$(IntermediateOutputPath)$(TargetFileName)&quot; -project $(ReactorProject) -targetfile &quot;$(ProjectDir)$(IntermediateOutputPath)$(TargetFileName)&quot;" Condition="'$(ConfigurationName)' == 'Release' " />
  </Target>
</Project>