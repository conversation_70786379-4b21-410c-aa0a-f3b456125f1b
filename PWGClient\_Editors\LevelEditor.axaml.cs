using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Rendering.SceneGraph;
using Avalonia.Skia;
using Avalonia.Threading;
using ReactiveUI;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Tabula;
using Tabula.PMCore;
using Tabula.SKRenderGraph;
using Tabula.AvaloniaUtils;
using System.Reflection;
using System.IO;
using HomographySharp;
using Avalonia.VisualTree;
using Tabula.PWG.SARGAME;

namespace Tabula.PWGClient
{
    // NOTE: Scene will be populated in App.axaml.cs, when model is available

    public partial class LevelEditor : SKSceneUserControl
    {
        public static LevelEditor Instance;

		public override bool IsSceneVisible
        {
            get
            {
				var tab = this.FindAncestor<TabItem>();

				if (tab != null)
                    return tab.IsSelected;
                else
                    return false;
            }
        }

        private SKImage     CalibrationImage;   // the last refreshed one
        private SKWarpQuad  CalibrationImageQuad;

        public Point MousePosition;

        public LevelEditor()
        {
            Instance = this;
        }

		protected override void OnInitialized()
		{
			Dispatcher.UIThread.InvokeAsync(async () => await ConfigureScene());

			// also get always the mouse position relative to this control
			PointerEntered += (s, e) 
                => MousePosition = e.GetPosition(this);
			PointerMoved += (s, e) 
                => MousePosition = e.GetPosition(this);
			PointerExited += (s, e) 
                => MousePosition = new Point(-1, -1);

			base.OnInitialized();
		}     

        public async override Task ConfigureScene()
        {
            Scene.BeforeDraw += BeforeDraw;
            Scene.AfterDraw += AfterDraw;

            Scene.OnObjectSelected += (sel) =>
            {
                if (sel == null)
                    return;

                var sel_pmview = PMViewExt.GetPMViewFromVisual(sel);

                if (sel_pmview != null)
                {
                    Dispatcher.UIThread.Invoke(() => MainWindowUI.Instance.MainSelectedItem = sel_pmview.GetView());
                }

            };
        }

		public override void OnCanvasAvailable()
		{
			// Fit
			Dispatcher.UIThread.InvokeAsync(() => MainWindowUI.Instance.Fit());

			base.OnCanvasAvailable();
		}

		public override void HandleKey(bool is_down, KeyEventArgs key_args)
		{
			base.HandleKey(is_down, key_args);

			if (key_args.Key == Key.Delete && is_down)
			{
                // Delete selected object
                MainWindowUI.Instance.DeleteSelectedObject();
			}			
		}

		// Will hint at refreshing, prepares image
		private bool _should_apply_calibration_image = false;
        public async Task RefreshCalibrationImage()
		{
            // Prepare calibration overlay (wall)
            try
            {
                // CalibrationImage = await App.Instance.GetCalibrationImage();
                _should_apply_calibration_image = (CalibrationImage != null);
                Scene.Refresh();
            }
            catch(Exception ex)
			{
                CalibrationImage = null;
                _should_apply_calibration_image = false;
			}
        }

        public override void OnBecameVisible()
		{
            // Always remove the calibration image quad
            if (CalibrationImageQuad != null)
            {
                Scene.Remove(CalibrationImageQuad);
                CalibrationImageQuad = null;
            }

            // Check if a calibration image is available
            var scenario = App.Instance.GetCurrentScenario();
            if (scenario != null)
            {
                if (File.Exists(scenario.CalibrationImagePath))
                {
                    CalibrationImage?.Dispose();
					CalibrationImage = SKImage.FromEncodedData(File.ReadAllBytes(scenario.CalibrationImagePath));
                    _applyCalibrationImage(CalibrationImage);
				}
            }
            
            base.OnBecameVisible();
		}


        private void _applyCalibrationImage(SKImage calibration_image)
		{
			// translated points
			var warped_vertices = new List<SKPoint>();

            if (App.Client.View.Screen.CreateHomography() == null)
            {
                // error?
                return;
            }

            App.Client.View.Screen.GetImageWarpedVertices(calibration_image.Width, calibration_image.Height, warped_vertices);

            /*
			// Calculate homography out of current calibration points
			List<System.Numerics.Vector2> src = new List<System.Numerics.Vector2>(), dst = new List<System.Numerics.Vector2>();
            for (int i = 0; i < 4; i++)
            {
                var image_pos = App.Client.Model.Screen.calibration_points[i].image_position;
                var screen_pos = App.Client.Model.Screen.calibration_points[i].screen_position;

                src.Add(new System.Numerics.Vector2(image_pos.x, image_pos.y));
                dst.Add(new System.Numerics.Vector2(screen_pos.x, screen_pos.y));
            }

            var homo_matrix = Homography.Find(src, dst);

            // translated points
            var warped_vertices = new List<SKPoint>();

            var p = homo_matrix.Translate(0, 0);
            warped_vertices.Add(new SKPoint(p.X, p.Y));
            p = homo_matrix.Translate(calibration_image.Width, 0);
            warped_vertices.Add(new SKPoint(p.X, p.Y));
            p = homo_matrix.Translate(calibration_image.Width, calibration_image.Height);
            warped_vertices.Add(new SKPoint(p.X, p.Y));
            p = homo_matrix.Translate(0, calibration_image.Height);
            warped_vertices.Add(new SKPoint(p.X, p.Y));

            */

            if (CalibrationImageQuad == null)
            {
                CalibrationImageQuad = new SKWarpQuad(Scene, new SKRect(0, 0, calibration_image.Width, calibration_image.Height))
                {
                    Layer = SKScene.LayerBackground,
                    Opacity = 1,
                    Image = calibration_image,
                    IsHitTestVisible = false
                };

                Scene.Add(CalibrationImageQuad);
            }

            CalibrationImageQuad.SetVertices(warped_vertices);
		}

        SKPaint _paint = null;
        public void BeforeDraw()
        {
            if (_paint == null)
               _paint = new SKPaint()             
                    { 
                        Color = new SKColor(255, 255, 255), 
                        IsStroke= true,
                        StrokeWidth = 2,
                        IsAntialias = true             
                    };           
        }

        public void AfterDraw()
        {
            // Only draw if model is present
            if (!App.IsConnected)
                return;


            // Draw the real screen size, depending on the cropping
            // TODO: this is now only for surface #0
            var visible_screen = App.Client.Model.GetVisualScreenSize(0);

			Scene.Canvas.DrawRect(new SKRect(visible_screen.x, visible_screen.y, visible_screen.x + visible_screen.width, visible_screen.y + visible_screen.height), _paint);
		}
      
    }
}
