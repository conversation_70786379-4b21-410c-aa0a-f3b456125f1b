﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Animation;
using Avalonia.Controls;
using Avalonia.Controls.Chrome;
using Avalonia.Controls.Templates;
using Avalonia.Interactivity;
using Avalonia.Media;
using HomographySharp;
using Newtonsoft.Json.Linq;
using PropertyChanged;
using ReactiveUI;
using SkiaSharp;
using Tabula.Licensing;
using Tabula.Log;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;
using Tabula.Unity;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static Tabula.PMCore.Entity;

namespace Tabula.PWGClient
{
	// Generic and not model-related hierarchy item or container
	[AddINotifyPropertyChangedInterface]
	public class HierarchyItem : BasePropertyChanged
	{
		public virtual string Title { get; set; }
		public bool IsExpanded { get; set; }
		public bool IsSelected { get; set; }

		public virtual FontWeight FontWeight { get; set; } = FontWeight.Regular;
		public virtual double FontSize { get; set; } = 14;

		public virtual ObservableCollection<object> Items { get; set; } = new ObservableCollection<object>();

		public virtual ObservableCollection<MenuItem> MenuItems { get; set; } = new ObservableCollection<MenuItem>();

		[DependsOn(nameof(MenuItems))]
		public bool HasMenuItems => MenuItems.Count > 0;

		public ModelView Root => ModelView.Instance;
	}

	// NOTE: this is not EntityView! still used???
	/*
	[AddINotifyPropertyChangedInterface]
	public class HierarchyEntityView : HierarchyItem
	{
		public EntityView View { get; set; }

		// TODO: change semantics
		public bool IsGroup => false; // View.group != null;

		public override ObservableCollection<object> Items
			=> View.ItemsForHierarchy;

		// UI specific
		public FontWeight FontWeight => IsGroup ? FontWeight.Bold : FontWeight.Normal;
		public bool HasIcon => !string.IsNullOrEmpty(View.icon);

		public HierarchyEntityView(EntityView view)
		{
			View = view;
		}

		public bool HasMenuItems => !IsGroup; // MenuItems.Count > 0;

		public ObservableCollection<MenuItem> MenuItems
		{
			get
			{
				var menuitems = new List<MenuItem>();

				// build the menu
				if (!IsGroup)
				{
					// TODO: can be deleted ?
					menuitems.Add(new MenuItem() { Header = "Delete", Tag = "delete" });
					menuitems.Add(new MenuItem() { Header = "Clone", Tag = "clone" });
				}


				foreach (var mi2 in menuitems)
					mi2.Click += menuitem_entity;

				if (menuitems.Count > 0)
					return new ObservableCollection<MenuItem>(menuitems);
				else
					return new ObservableCollection<MenuItem>();
			}
		}

		async void menuitem_entity(object sender, RoutedEventArgs args)
		{
			var mi = (sender as MenuItem);
			var ev = mi.DataContext as EntityView;
			switch (mi.Tag)
			{
				case "delete": await Root.RemoveEntityAsync(ev); break;

				case "clone":
				
					var new_entity = ev.Model.Clone<Entity>();
					new_entity.name += " (clone)";
					new_entity.position = new Vector2f(ev.position.x + 30, ev.position.y + 30);

					await Root.AddEntityAsync(new_entity, Root.Entities[0]);
					
					break;
			}
		}

	}
	*/
}

namespace Tabula.PMCore
{
	// Extensions with helpers
	public static class Extensions
	{
		public static bool HasProperty(this List<KeyValueType> properties, string pname, string pvalue)
		{
			if (properties == null)
				return false;

			return (from p in properties where p.key == pname && p.value == pvalue select p).Any();
		}
	}


	public partial class OutputSurfaceView : GuidObjectSyncView<OutputSurface>
	{
		public string Name => $"Surface #{SARGAME.View.Model.OutputSurfaces.IndexOf(Model) + 1}";

		// hierarchy visualization
		public bool IsExpanded { get; set; } = false;
		public bool IsSelected { get; set; } = false;

		public double FontSize => 14;
	}


	public partial class StructureView : GuidObjectSyncView<Structure>
	{
		public string Name => $"Structure #{SARGAME.View.Model.Structures.IndexOf(Model) + 1}";

		public bool HasFields => fields != null && fields.Count() > 0;
		public bool HasVisual => flags.HasFlag(Entity.Flags.HasVisual);       // the visual position is used, therefore shown in inspector

		// overrides behaviour for structures, that have the single StructureGroup parent (UI only)
		public new object GetParent() => SARGAME.iMainWindowUI.structure_group;

		public bool IsRootChild => Model.__guid_parent == 1;

		// hierarchy visualization
		public bool IsExpanded { get; set; } = false;
		public bool IsSelected { get; set; } = false;
		public Thickness Padding => new Thickness(2, 2, 2, 2);

		public double FontSize => 14;
		public bool AreEffectsExpanded { get; set; } = false;

		public ObservableCollection<KeyValueString> Effects => SARGAME.iMainWindowUI.Effects;

		public object Main => SARGAME.iMainWindowUI;
	}

	/*
	public partial class EffectView : GuidObjectSyncView<Effect>
	{
		public bool HasPresets => Model.presets!=null && Model.presets.Count() > 0;

		public bool HasSelectedPreset => !string.IsNullOrEmpty(Model.preset) && HasPresets && (from p in Model.presets where p.name == Model.preset select p).Any();
	}
	*/

	// Extension of the ScreenView to perform hompgraphy and other calibration tasks

	public partial class ScreenView : GuidObjectSyncView<Screen>
	{
		private HomographyMatrix<float> CurrentHomography = null;

		public HomographyMatrix<float> CreateHomography()
		{
			// Calculate homography out of current calibration points
			List<System.Numerics.Vector2> src = new List<System.Numerics.Vector2>(), dst = new List<System.Numerics.Vector2>();
			for (int i = 0; i < 4; i++)
			{
				var image_pos = image_markers[i].position;
				var screen_pos = screen_markers[i].position;

				src.Add(new System.Numerics.Vector2(image_pos.x, image_pos.y));
				dst.Add(new System.Numerics.Vector2(screen_pos.x, screen_pos.y));
			}

			try
			{
				CurrentHomography = Homography.Find(src, dst);
			}
			catch
			{
				CurrentHomography = null;
				return null;
			}

			return CurrentHomography;
		}

		// calculates homography and tries to define a "goodness" factor
		public double GetHomographyPerspectiveError()
		{
			var M = CreateHomography();

			double perspective_error = 100.0;

			if (M[1, 1] != 0.0)
				perspective_error = Math.Abs(M[0, 0] / M[1, 1] - 1) + 100.0 * (Math.Abs(M[2, 0]) + Math.Abs(M[2, 1]) + Math.Abs(M[2, 2] - 1));

			return perspective_error;
		}


		public void GetImageWarpedVertices(int image_width, int image_height, List<SKPoint> warped_vertices)
		{
			var p = CurrentHomography.Translate(0, 0);
			warped_vertices.Add(new SKPoint(p.X, p.Y));
			p = CurrentHomography.Translate(image_width, 0);
			warped_vertices.Add(new SKPoint(p.X, p.Y));
			p = CurrentHomography.Translate(image_width, image_height);
			warped_vertices.Add(new SKPoint(p.X, p.Y));
			p = CurrentHomography.Translate(0, image_height);
			warped_vertices.Add(new SKPoint(p.X, p.Y));
		}

		public void HomographyTransform(float x, float y, ref float warp_x, ref float warp_y)
		{
			var p = CurrentHomography.Translate(x, y);
			warp_x = p.X;
			warp_y = p.Y;
		}


	}


	// groups of fields
	[AddINotifyPropertyChangedInterface]
	public class FieldGroup
	{
		public string Name => GroupFields.Count > 0 ? (GroupFields[0] as dynamic).Model.group : "";

		public bool HasName => !string.IsNullOrEmpty(Name);

		public bool IsExpanded { get; set; } = false;

		public bool ShowHeader => (HasName ? true : false);

		[DependsOn(nameof(IsExpanded))]
		public bool AreFieldsExpanded => ShowHeader ? IsExpanded : true;		

		public ObservableCollection<object> GroupFields { get; set; } = new ObservableCollection<object>();

		public static ObservableCollection<FieldGroup> CreateFromEntityView(EntityView ev)
		{
			var groups = new ObservableCollection<FieldGroup>();

			var dict_group = new SortedDictionary<int, SortedDictionary<int, List<object>>>(); // group_index, (field_index, field)  

			// first look for fields that hide other fields or groups
			List<string> fields_hidden = new List<string>();
			List<string> groups_hidden = new List<string>();
			foreach (dynamic f in ev.fields)
			{
				if (f.Model.hides_fields != null || f.Model.hides_groups != null)
				{					
					try
					{
						var hide_value = f.Model.hide_value;
						if (hide_value == null)
							continue;
					
						if (f.Model.value.Equals(hide_value))
						{
							if (f.Model.hides_fields != null)
							{
								foreach (var h in f.Model.hides_fields)
								{
									if (!fields_hidden.Contains(h))
										fields_hidden.Add(h);
								}
							}

							if (f.Model.hides_groups != null)
							{
								foreach (var h in f.Model.hides_groups)
								{
									if (!groups_hidden.Contains(h))
										groups_hidden.Add(h);
								}
							}
						}
					}
					catch { }
				}				
			}

			foreach (dynamic f in ev.fields)
			{
				if (f.show_editor == false)
					continue;

				// check if fields has dependencies on some other bool field
				if (fields_hidden.Contains(f.name))
					continue;

				int gi = f.group_index;
				if (!dict_group.ContainsKey(gi))
					dict_group.Add(gi, new SortedDictionary<int, List<object>>());

				if (!dict_group[gi].ContainsKey(f.field_index))
					dict_group[gi][f.field_index] = new List<object>();

				dict_group[gi][f.field_index].Add(f);
			}

			// Create FieldGroup only if there are contained fields
			foreach (var g in dict_group)
			{
				var fg = new FieldGroup();

				foreach (var g2 in g.Value)
				{
					foreach (var g3 in g2.Value)
						fg.GroupFields.Add(g3);
				}

				if (fg.GroupFields.Count > 0)
				{
					if (!groups_hidden.Contains(fg.Name))
						groups.Add(fg);
				}
			}

			return groups;
		}
	}

	public partial class EntityView : GuidObjectSyncView<Entity>
	{
		public string Name => !string.IsNullOrEmpty(name_desc) ? name_desc : name.Replace("#", "");

		public bool HasFields => fields != null && fields.Count() > 0;
		public bool HasVisual => flags.HasFlag(Entity.Flags.HasVisual);       // the visual position is used, therefore shown in inspector
		

		// hierarchy visualization
		public bool IsExpanded { get; set; } = false;
		public bool IsSelected { get; set; } = false;

		// editing
		private bool _is_locked = false;
		public bool IsLocked
		{
			get => _is_locked;

			set
			{
				_is_locked = value;
				var visuals = this.GetVisuals();
				if (visuals != null)
					foreach (var v in visuals)
					{
						//v.IsSelectable = !_is_locked;
						v.IsDraggable = !_is_locked;
						//v.IsHitTestVisible = !_is_locked;
					}

				/*
				if (_is_locked)
					SARGAME.iMainWindowUI.ForceMainSelectedItem(null);
				*/
			}
		}

		// from entity properties
		public bool CanBeDeleted	=> Model.GetProp<bool>("delete");
		public bool CanBeCloned		=> Model.GetProp<bool>("clone");

		public bool HasItems => Model.items!=null && Model.items.Count() > 0;

		private ObservableCollection<FieldGroup> _groups;
		public ObservableCollection<FieldGroup> Groups
		{
			get
			{								
				if (_groups == null)
					_groups = FieldGroup.CreateFromEntityView(this);				

				return _groups;
			}
		}

		public void ForceGroupsRefresh()
		{
			Dictionary<string, bool> _get_group_expand()
			{
				var dict = new Dictionary<string, bool>();

				if (_groups == null)
					return dict;

				foreach (var g in _groups)
					if (g.HasName)
						dict.Add(g.Name, g.IsExpanded);

				return dict;
			}

			void _set_group_expand(Dictionary<string, bool> dict)
			{
				if (_groups == null)
					return;

				foreach (var g in _groups)
				{
					if (g.HasName)
						if (dict.ContainsKey(g.Name))
							g.IsExpanded = dict[g.Name];
				}
			}

			// retains group expansion between refreshes
			var _group_dict = _get_group_expand();

			_groups = null;

			RaisePropertyChanged(nameof(Groups));

			_set_group_expand(_group_dict);
		}

		// Just show show_editor items, needed for nested views
		public ObservableCollection<object> items_shown_in_editor
		{
			get
			{
				if (items == null)
					return new ObservableCollection<object>();
				
				return new ObservableCollection<object>(from i in items where i.show_editor select i);
			}
		}

		// FIXME: still used?
		/*
		public ObservableCollection<object> ItemsForHierarchy
		{
			get
			{
				if (items == null)
					return new ObservableCollection<object>();

				var c = new ObservableCollection<object>();
				foreach (var i in items)
					c.Add(new HierarchyEntityView(i));

				return c;
			}
		}
		*/

		// checks if any fields has an "advanced" tag for grouping
		public ObservableCollection<object> AdvancedFieldsShown
		{
			get
			{
				if (fields == null)
					return new ObservableCollection<object>();

				var shown = new ObservableCollection<object>();

				string fname = "";
				dynamic dfield;

				foreach (var f in fields)
				{
					try
					{
						dfield = f;
						fname = dfield.Model.name;

						if (dfield.Model.tag == null)
							continue;

						if (dfield.Model.tag != "advanced")
							continue;
					}
					catch
					{
						continue;
					}

					if (Model.fields_shown == null)
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
					else if (Model.fields_shown.Contains(fname))
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
				}

				return shown;
			}
		}

		public bool HasAdvancedFieldsShown => AdvancedFieldsShown != null && AdvancedFieldsShown.Count > 0;


		// fields that do not have "advanced" tag 
		public ObservableCollection<object> NormalFieldsShown
		{
			get
			{
				if (fields == null)
					return new ObservableCollection<object>();

				var shown = new ObservableCollection<object>();

				string fname = "";
				dynamic dfield;

				foreach (var f in fields)
				{
					try
					{
						dfield = f;
						fname = dfield.Model.name;

						if (dfield.Model.tag != null && dfield.Model.tag == "advanced")
							continue;
					}
					catch
					{
						continue;
					}

					if (Model.fields_shown == null)
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
					else if (Model.fields_shown.Contains(fname))
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
				}

				return shown;
			}
		}

		// Generic (all shown fields)
		public ObservableCollection<object> FieldsShown
		{
			get
			{
				if (fields == null)
					return new ObservableCollection<object>();

				var shown = new ObservableCollection<object>();

				string fname = "";
				dynamic dfield;

				foreach (var f in fields)
				{
					// dynamic
					try
					{
						dfield = f;
						fname = dfield.Model.name;
					}
					catch
					{
						continue;
					}

					if (Model.fields_shown == null)
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
					else if (Model.fields_shown.Contains(fname))
					{
						if (dfield.Model.show_editor == true)
							shown.Add(f);
					}
				}

				return shown;
			}
		}

		// Helper to set value to dynamic field (for preset applying)
		public void SetFieldValue(string name, object value)
		{
			dynamic dfield;

			foreach (var f in fields)
			{
				try
				{
					dfield = f;

					if (dfield.Model.name == name)
					{
						dfield.SetValue(value);
						return;
					}
				}
				catch
				{
					continue;
				}
			}
		}

		// tries to get the field value casted, or default if not found
		public object GetFieldValue<T>(string name)
		{
			dynamic dfield;

			foreach (var f in fields)
			{
				try
				{
					dfield = f;

					if (dfield.Model.name == name)
					{
						return (T) dfield.value;
					}
				}
				catch
				{
					continue;
				}
			}

			return default;
		}

		public void RefreshAllFields()
		{
			dynamic dfield;

			foreach (var f in fields)
			{
				try
				{
					dfield = f;
					dfield.RaiseAllPropertyChanged();
				}
				catch
				{
					continue;
				}
			}
		}


		// Presets
		#region Presets

		public PresetView CurrentPreset
		{
			get
			{
				if (preset == null || presets == null)
					return null;

				return (from p in presets where p.name == preset select p).FirstOrDefault();
			}
		}

		public PresetView GetPresetView(string preset_name)
		{
			if (presets == null)
				return null;

			return (from p in presets where p.name == preset_name select p).FirstOrDefault();
		}

		public ObservableCollection<string> Presets => new ObservableCollection<string>(from p in Model.presets select p.name);

		public ObservableCollection<string> CustomPresets => new ObservableCollection<string>(from p in Model.presets where !p.is_readonly select p.name);

		public string CustomPresetToOverwrite { get; set; }

		// For preset UI
		private string _preset_selected = null;
		public string SelectedPreset
		{
			get
			{
				if (_preset_selected == null)
					_preset_selected = preset;

				return _preset_selected;
			}

			set => _preset_selected = value;
		}

		[DependsOn(nameof(preset))]
		public string PresetLabel => $"Preset ({(preset != null ? preset : "")})";

		public bool HasPresets => Model.presets != null && Model.presets.Count > 0;

		public bool IsSelectedPresetEditable
		{
			get
			{
				// Ignore if used in other GameSettings tabs... FIXME: not really clean, but we are using the same EntityView data template
				if (this != SARGAME.iMainWindowUI.RulesSettingsEntity)
					return true;

				var v = GetPresetView(SelectedPreset);
				if (v == null)
					return false;
				else
					return !v.is_readonly;
			}
		}

		#endregion


		public bool		  IsRootChild => Model.__guid_parent == 1;

		// UI specific
		public double	  FontSize => 14;
		public FontWeight FontWeight => IsRootChild ? FontWeight.Bold : FontWeight.Normal;
		public Thickness  Padding => IsRootChild ? new Thickness(0, 5, 5, 5) :  new Thickness(2, 2, 2, 2);
		public bool		  HasIcon => !string.IsNullOrEmpty(icon);

		SKImage _skia_icon;
		public SKImage SkiaIcon
		{
			get
			{
				if (_skia_icon == null)
					_skia_icon = SKImage.FromEncodedData(SARGAME.iMainWindowUI.GetIcon(icon));

				return _skia_icon;
			}
		}

		public bool HasMenuItems => CanBeCloned || CanBeDeleted;

		public ObservableCollection<MenuItem> MenuItems
		{
			get
			{
				var menuitems = new List<MenuItem>();

				// build the menu
				if (CanBeDeleted)
					menuitems.Add(new MenuItem() { Header = "   Delete", Tag = "delete" });

				if (CanBeCloned)
					menuitems.Add(new MenuItem() { Header = "   Clone", Tag = "clone", });

				foreach (var mi2 in menuitems)
					mi2.Click += menuitem_entity;

				if (menuitems.Count > 0)
					return new ObservableCollection<MenuItem>(menuitems);
				else
					return new ObservableCollection<MenuItem>();
			}
		}

		async void menuitem_entity(object sender, RoutedEventArgs args)
		{
			var mi = (sender as MenuItem);
			var ev = mi.DataContext as EntityView;

			switch (mi.Tag)
			{
				case "delete": 
					Root.RemoveEntity(ev); 
					break;

				case "clone":

					await PMEntityView.CreateEntityOnServer(new EntityCreate()
					{
						type = ev.Model.type
					}, 
					ev.position.x + 30, ev.position.y + 30, add_to_recent: false);

					// TODO: use CreateEntity ?
					/*
					var new_entity = ev.Model.Clone<Entity>();
					new_entity.name += " (clone)";
					new_entity.position = new Vector2f(ev.position.x + 30, ev.position.y + 30);

					Root.AddEntity(Root.Entities[0], new_entity);
					*/
					break;
			}
		}
	}

	// groups of fields
	public partial class StructureEffectView : GuidObjectSyncView<StructureEffect>
	{
		public string Name => !string.IsNullOrEmpty(name_desc) ? name_desc : name.Replace("#", "");

		public bool HasFields => fields != null && fields.Count() > 0;

		public bool IsExpanded { get; set; } = false;

		public bool ShowHeader => true;

		[DependsOn(nameof(IsExpanded))]
		public bool AreFieldsExpanded => ShowHeader ? IsExpanded : true;
	}

	// Extension to wrap properties for dialogs
	[AddINotifyPropertyChangedInterface]
	public partial class EntityCreate
	{
		public string Icon => icon;
		public string NameDesc => name_desc;
	}

	#region Field Views extensions

	public partial class BoolFieldView : GuidObjectSyncView<BoolField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (bool)value;
	}

	public partial class BoolCardsFieldView : GuidObjectSyncView<BoolCardsField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		[DependsOn(nameof(value))]
		public bool IsTrue => value == true;

		[DependsOn(nameof(value))]
		public double ZTrue => value == true ? 1 : 0;

		[DependsOn(nameof(value))]
		public double ZFalse => value == true ? 0 : 1;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (bool)value;
	}

	public partial class StringFieldView : GuidObjectSyncView<StringField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class FileFieldView : GuidObjectSyncView<FileField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class IntegerFieldView : GuidObjectSyncView<IntegerField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		public override object GetValidatedValue(string name, object value)
		{
			if (name == nameof(value))
			{
				if (min != max)
					return Math.Clamp((int)value, min, max);
				else
					return value;
			}
			else
				return value;
		}

		// need this for presets
		public void SetValue(object value) => this.value = (int)Convert.ChangeType(value, typeof(int));
	}

	public partial class ButtonFieldView : GuidObjectSyncView<ButtonField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;
	}

	public partial class FloatFieldView : GuidObjectSyncView<FloatField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		public override object GetValidatedValue(string name, object value)
		{
			if (name == nameof(value))
			{
				if (min != max)
					return Math.Clamp((float)value, min, max);
				else
					return value;
			}
			else
				return value;
		}

		// need this for presets
		public void SetValue(object value) => this.value = (float)Convert.ChangeType(value, typeof(float));
	}

	public partial class ChoiceFieldView : GuidObjectSyncView<ChoiceField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		private ObservableCollection<KeyValuePair<string, string>> _choices = null;
		public ObservableCollection<KeyValuePair<string, string>> Choices
		{
			get
			{
				if (Model.choice_keys == null)
					return null;

				if (_choices != null)
					return _choices;

				_choices = new ObservableCollection<KeyValuePair<string, string>>();
				int i = 0;
				foreach (var k in Model.choice_keys)
				{
					_choices.Add(new KeyValuePair<string, string>(k, Model.choice_values[i]));
					i++;
				}

				return _choices;
			}
		}

		// supports hide_fields and hide_groups
		object _last_value = null;
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			// need to check last value because combobox would continuolsy refresh
			if (_last_value == null)
				_last_value = value;

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					if (value!=null && !_last_value.Equals(value))
					{
						_last_value = value;
						(GetParent() as EntityView)?.ForceGroupsRefresh();
					}
			
		}

		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class ChoiceSelectorFieldView : GuidObjectSyncView<ChoiceSelectorField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		public string Value
		{
			get
			{
				int key_index = Model.choice_keys.ToList().IndexOf(value);
				return Model.choice_values[key_index];
			}
		}

		public void Previous()
		{
			if (Model.choice_keys == null)
				return;

			int key_index = Model.choice_keys.ToList().IndexOf(value);
			if (key_index > 0)
				value = Model.choice_keys[key_index - 1];
		}

		public void Next()
		{
			if (Model.choice_keys == null)
				return;

			int key_index = Model.choice_keys.ToList().IndexOf(value);
			if (key_index < (Model.choice_keys.Length-1))
				value = Model.choice_keys[key_index + 1];
		}

		private ObservableCollection<KeyValuePair<string, string>> _choices = null;
		public ObservableCollection<KeyValuePair<string, string>> Choices
		{
			get
			{
				if (Model.choice_keys == null)
					return null;

				if (_choices != null)
					return _choices;

				_choices = new ObservableCollection<KeyValuePair<string, string>>();
				int i = 0;
				foreach (var k in Model.choice_keys)
				{
					_choices.Add(new KeyValuePair<string, string>(k, Model.choice_values[i]));
					i++;
				}

				return _choices;
			}
		}

		// supports hide_fields and hide_groups
		object _last_value = null;
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			// need to check last value because combobox would continuolsy refresh
			if (_last_value == null)
				_last_value = value;

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					if (value != null && !_last_value.Equals(value))
					{
						_last_value = value;
						(GetParent() as EntityView)?.ForceGroupsRefresh();
					}
		}

		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	public partial class SliderFloatFieldView : GuidObjectSyncView<SliderFloatField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		public override object GetValidatedValue(string name, object value)
		{
			if (name == nameof(value))
			{
				if (min != max)
					return Math.Clamp((float)value, min, max);
				else
					return value;
			}
			else
				return value;
		}

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (float)Convert.ChangeType(value, typeof(float));
	}

	public partial class SliderFloatPercentageFieldView : GuidObjectSyncView<SliderFloatPercentageField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		public override object GetValidatedValue(string name, object value)
		{
			if (name == nameof(value))
			{
				if (min != max)
					return Math.Clamp((float)value, min, max);
				else
					return value;
			}
			else
				return value;
		}

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (float)Convert.ChangeType(value, typeof(float));
	}

	public partial class ColorFieldView : GuidObjectSyncView<ColorField>
	{
		public bool HasTooltip => !string.IsNullOrEmpty(tooltip);
		public string Label => !string.IsNullOrEmpty(name_desc) ? name_desc : name;

		[DependsOn(nameof(value))]
		public bool IsRuleAndSelectedPresetEditable
			=> (GetParent() as EntityView)?.IsSelectedPresetEditable ?? false;

		// supports hide_fields and hide_groups
		public override void RaisePropertyChanged(string propertyName)
		{
			base.RaisePropertyChanged(propertyName);

			if (Model.hide_value != null)
				if (propertyName == nameof(value))
					(GetParent() as EntityView)?.ForceGroupsRefresh();
		}

		// need this for presets
		public void SetValue(object value) => this.value = (string)Convert.ChangeType(value, typeof(string));
	}

	#endregion
}
