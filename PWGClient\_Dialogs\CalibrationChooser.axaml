<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
			 xmlns:tabula_unity="clr-namespace:Tabula.Unity"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 Width="{Binding DialogMediumSize.Width}" Height="{Binding DialogMediumSize.Height}"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.CalibrationChooser" >

	<dialogHost:DialogHost Identifier="calibrationchooser_dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True">
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

		<Border BorderBrush="White" BorderThickness="1" Background="{DynamicResource ExpanderBackground}">
				<Grid Grid.Row="0" ColumnDefinitions="*,*"  Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
					<Button Grid.Column="0" Width="256" Height="144" Margin="10" Padding="10" Click="bt_manual_Click">
						<Grid RowDefinitions="*,Auto">
							<materialIcons:MaterialIcon Grid.Row="0" Width="30" Height="30" Margin="15" Kind="MonitorEdit" />
							<TextBlock Grid.Row="1" Text="Manual Calibration" FontSize="16" FontWeight="Bold"/>
						</Grid>
					</Button>
					<Button Grid.Column="1" Width="256" Height="144" Margin="10" Padding="10" Click="bt_automatic_Click">
						<Grid RowDefinitions="*,Auto">
							<materialIcons:MaterialIcon Grid.Row="0" Width="30" Height="30" Margin="15" Kind="CameraEnhanceOutline" />
							<TextBlock Grid.Row="1" Text="Automatic Calibration" FontSize="16" FontWeight="Bold"/>
						</Grid>
					</Button>
				</Grid>
		</Border>
	</dialogHost:DialogHost>
</UserControl>
