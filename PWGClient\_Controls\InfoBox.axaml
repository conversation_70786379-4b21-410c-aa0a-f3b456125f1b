<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
			 xmlns:tabula_unity="clr-namespace:Tabula.Unity"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.InfoBox" >
	<Grid ColumnDefinitions="Auto,*,Auto" VerticalAlignment="Bottom" HorizontalAlignment="Stretch">
		<Grid.Background>
			<SolidColorBrush Color="Black" Opacity="0.50"/>
		</Grid.Background>
		<materialIcons:MaterialIcon Grid.Column="0" Width="20" Height="20" Margin="15" Kind="{Binding Icon}" HorizontalAlignment="Center" VerticalAlignment="Center" IsHitTestVisible="False"/>
		<Grid Grid.Column="1" Grid.RowDefinitions="Auto,Auto"  HorizontalAlignment="Left" VerticalAlignment="Center">
			<TextBlock Grid.Row="0" Text="{Binding Message}" TextWrapping="Wrap" Margin="10" HorizontalAlignment="Left" VerticalAlignment="Center" IsHitTestVisible="False"/>
			<TextBlock Grid.Row="1" Text="{Binding ErrorMessage}" IsVisible="{Binding IsErrorMessageVisible}" Foreground="Red" TextWrapping="Wrap" Margin="10" HorizontalAlignment="Left" VerticalAlignment="Center" IsHitTestVisible="False"/>
		</Grid>
		

		<!-- (optional) action buttons -->
		<Grid Grid.Column="2" ColumnDefinitions="Auto,Auto">
			
			<!-- Cancel -->
			<Button Grid.Column="0" Classes="Outlined" IsVisible="{Binding IsCancelButtonVisible}" Margin="5"
				 HorizontalAlignment="Right" VerticalAlignment="Center"
				 Click="bt_cancel_Click">
				<TextBlock Text="{Binding CancelButtonText}"/>
			</Button>
			
			<!-- Action -->
			<Button x:Name="bt_action" Grid.Column="1" Classes="Primary Rounded" IsVisible="{Binding IsActionButtonVisible}" Margin="5"
				 HorizontalAlignment="Right" VerticalAlignment="Center"
				 Click="bt_action_Click">
				<TextBlock Text="{Binding ActionButtonText}"/>
			</Button>
		</Grid>

	</Grid>
</UserControl>
