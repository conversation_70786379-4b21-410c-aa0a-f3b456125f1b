﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Input;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

namespace Tabula.PMCore
{
    // NOTE: polylines are entities not special objects
    public class PMPolyLineView : PMEditablePolyLineView<Tabula.PMCore.Entity, Tabula.PMCore.EntityView>
    {
        public PMPolyLineView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }

        // editing color references
        private SKColor Color = SKColors.Cyan;
        private SKColor ColorHover = new SKColor(0, 0, 255, 150);
        private SKColor ColorDragging = new SKColor(255, 255, 255, 150);


		public override void CreateVisual()
        {
            bool was_selected = Visual!=null ? Visual.IsSelected : false;

            if (Visual != null)
                Visual.Scene.Remove(Visual);

            VisualPoly = new SKFreePolyLine(Scene)
            {
                IsDraggable = false,

                MustBeValid = true,

                MustBeInScreen = Model.GetProp<bool>("must_be_in_screen"),  // will query for viewable region to iMainWindowUI

				CloseLine = Model.GetProp<bool>("closed"),

				Layer = SKScene.LayerStructures,
				Color = Color,
                LineSize = 4,
                ColorHover = ColorHover,
                ColorDragging = ColorDragging
            };

            foreach (var v in Model.points)
                VisualPoly.AddSegment(new SKPoint(v.x, v.y), SKFreePolyLine.SegmentType.Line);

            // TODO: onMove dei singoli segmenti
            VisualPoly.OnVertexDragStart += (cp) => VisualPoly.Color = ColorDragging;
			VisualPoly.OnVertexDragEnd += (cp) => VisualPoly.Color = Color;
			VisualPoly.OnVertexMove += OnVertexMove;
            VisualPoly.OnVertexAdd += OnVertexAdd;
            VisualPoly.OnVertexRemove += OnVertexRemove;

            VisualPoly.onKey += OnKey;


            Visual = VisualPoly;

            // NO Move for polylines, they have no pivot
            // Visual.onMove += (o, pos) => OnMove(pos);

            if (was_selected)
                Visual.Scene.SelectObject(Visual);

            Visual.onSelected += OnSelected;
            Visual.onUnSelected += OnUnSelected;

            base.CreateVisual();
        }

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            lock (this)
            {
                if (fieldname == nameof(Model.points))
                {
                    CreateVisual();

                    return true;
                }

                // Not setting the position
                //var v2 = View.Model.position;
                //Visual.SetPosition(new SKPoint(v2.x, v2.y));
            }

            return true;
        }

        public void OnVertexMove(SKControlPoint cp, int index, SKPoint pos)
        {
            // optimize syncing only right segment
            VisualPoly.SyncFromControlPoint(index);

            // special check, model and visual should have the same number of vertices
            if (View.points.Count != VisualPoly.Vertices.Count)
            {
                SARGAME.App.logError("PMPolyLineView.OnVertexMove() different number of vertices");
                return;
            }

            View.CommitBegin();
                View.points[index].x = VisualPoly.Vertices[index].X;
                View.points[index].y = VisualPoly.Vertices[index].Y;
            View.CommitEnd();

            SelectControlPoint(cp);
        }

        public void OnVertexAdd(int index, SKPoint pos)
        {            
            View.points_Add(new Vector2f(pos.X, pos.Y), index);

            SelectControlPoint(null);
        }

        public void OnVertexRemove(int index)
        {
            if (Model.points.Count <= 2)
                return;

            View.points_Remove(index);

            SelectControlPoint(null);
        }

        public void OnSelected()
		{
            SelectControlPoint(null);
        }

        public void OnUnSelected()
		{
            SelectControlPoint(null);
        }        

        protected override void OnKey(bool is_down, KeyEventArgs args)
		{
            if (is_down)
                switch (args.Key)
			    {             
                   
                }

            base.OnKey(is_down, args);   
        }

    }
}
