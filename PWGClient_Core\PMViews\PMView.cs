﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Input;
using kcp2k;
using Newtonsoft.Json;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;
using Tabula.WebServices.Arguments;

// Base class for a visual view of a GuidObject class

// Visually modeled as a rectangle now, ready to accept sprites etc..

namespace Tabula.PMCore
{
    public interface IPMView
    {
        Tabula.SharedObjectMap.GuidObject GetModel();
        IGuidObjectSyncView GetView();

        SKRenderGraph.SKObject GetVisual();

        bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField);

		void Destroy();
    }


    public static class PMViewExt
    {
        // View map 
        public static Dictionary<Tabula.SharedObjectMap.GuidObject, List<IPMView>> Views = new Dictionary<Tabula.SharedObjectMap.GuidObject, List<IPMView>>();

        public static void AddPMView(IPMView view)
        {
            lock (Views)
            {
                var m = view.GetModel();

                if (!Views.ContainsKey(m))
                    Views.Add(m, new List<IPMView>());

                Views[m].Add(view);
            }
        }

        public static List<T> GetPMViewsOfType<T>() where T : IPMView
		{
            List<T> found_views = new List<T>();
            foreach (var kvp in Views)
                foreach (var v in kvp.Value)
                    if (v is T)
						found_views.Add((T) v);

            return found_views;
		}

        // TODO: should return a list?
		public static T GetPMViewOfType<T>(long guid) where T : IPMView
		{
			foreach (var kvp in Views)
				foreach (var v in kvp.Value)
					if (v is T)
                        if (((T) v).GetModel().__guid == guid)
                            return (T) v;

            return default(T);
		}

		public static List<IPMView> GetPMViewsFromModel(Tabula.SharedObjectMap.GuidObject model)
        {
            if (Views.TryGetValue(model, out List<IPMView> views))
                return views;
            else
                return null;
        }

		public static List<T> GetPMViewsFromModel<T>(Tabula.SharedObjectMap.GuidObject model)
		{
            List<T> _views = new List<T>();
            if (Views.TryGetValue(model, out List<IPMView> views))
            {
				foreach (var v in views)
					if (v is T)
                        _views.Add((T) v);

                return _views;
			}
            else
                return null;
		}

		public static List<IPMView> GetPMViewsFromView(Tabula.SharedObjectMap.IGuidObjectSyncView view)
		{
            return GetPMViewsFromModel(view.GetModel());
		}

		public static List<T> GetPMViewsFromView<T>(Tabula.SharedObjectMap.IGuidObjectSyncView view)
		{
			return GetPMViewsFromModel<T>(view.GetModel());
		}

		public static IPMView GetPMViewFromVisual(SKRenderGraph.SKObject visual)
        {
            foreach (var kvp in Views)
                foreach (var v in kvp.Value)
                    if (v.GetVisual() == visual)
                        return v;

            return null;

            /*
            var view = (from v in Views where v.Value.GetVisual() == visual select v.Value).FirstOrDefault();

            return view;
            */
        }

        public static async Task<T> WaitForPMViewWithGuid<T>(long guid, int timeout = 1000) where T: IPMView
        {
            T ret = default;
            var sw = new Stopwatch();
            sw.Start();
            while (sw.ElapsedMilliseconds < timeout)
            {
                ret = GetPMViewOfType<T>(guid);
                if (ret != null)
                    return ret;

                await Task.Delay(10);
            }

            return default;
        }

        // throws timeout
		public static async Task WaitForNoPMView<T>(int timeout = 1000) where T : IPMView
		{
            List<T> found_views = new List<T>();
			var sw = new Stopwatch();
            sw.Start();
			while (sw.ElapsedMilliseconds < timeout)
			{
                if (GetPMViewsOfType<T>().Count == 0)
                    return;

				await Task.Delay(10);
			}

            throw new TimeoutException();
		}

		public static void RemovePMView(Tabula.SharedObjectMap.GuidObject model)
        {
            lock (Views)
            {
                Views.Remove(model);
            }
        }

        public static void RemovePMView(IPMView view) => RemovePMView(view.GetModel());

        public static void DestroyAllPMViews()
        {
            lock (Views)
            {
                foreach (var kvp in Views)
                    foreach (var v in kvp.Value)
                        v.Destroy();
            }
        }

        // Helpers

        /*
        public static IEnumerable<T> GetPMViewsOfType<T>()
            => Views.Values.OfType<T>();
        */

        public static List<SKRenderGraph.SKObject> GetVisuals(this IGuidObjectSyncView view)
        {
            var views = GetPMViewsFromModel(view.GetModel());

            if (views == null)
                return null;

            var list = new List<SKRenderGraph.SKObject>();
            foreach (var v in views)
                list.Add(v.GetVisual());

            return list;
        }

		// IMPORTANT: since only ONE view is updated for model, this function will handle other views that would not get the update
		public static bool OnUpdateAllPMViews(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
		{
            var views = GetPMViewsFromModel(view.GetModel());
            if (views == null)
                return false;

            bool ret = false;
            foreach (var v in views)
                if (v.OnUpdate(from_server, view, fieldname, item_index_or_key, update_type))
                    ret = true;

            return ret;
		}

	}

    public abstract class PMView<model,view> : IPMView
        where model : Tabula.SharedObjectMap.GuidObject
        where view : IGuidObjectSyncView
    {        
        // Binding to a View
        public model    Model;
        public virtual view     View => (view) Model.__view;

        public SKScene  Scene;

        // Physical decorator is a real SKObject, to be instantiated
        public SKRenderGraph.SKObject Visual;

        public bool VisualIsDirty = false;  // in order to re-configure the visual if needed
                
        // using this contructor the view is marked as NON visual
        public PMView(Tabula.SharedObjectMap.GuidObject model)
        {
			Model = (model)model;
            Model.OnViewChanged = (v) =>
            {
                if (v != null)
                    View.onNotifyUpdate = OnUpdate;
			};


			// View will be hooked in Update when available                        
			PMViewExt.AddPMView(this);
		}

        public PMView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null)
        {
			Debug.WriteLine($"Create PMView type:{this.GetType()}");

			if (scene == null)
                return; // TODO: nothing to do, need to issue warning

            Model = (model) model;

            Scene = scene;

            // Visual is dependent on the implementation
            CreateVisual();

            Visual.Name = name;

            // View will be hooked in Update when available                        
            PMViewExt.AddPMView(this);

            // NEW: dirty by default
            VisualIsDirty = true;

		}

        public Tabula.SharedObjectMap.GuidObject GetModel() => Model;
        public SKRenderGraph.SKObject GetVisual() => Visual;
        public IGuidObjectSyncView GetView() => View;

        // Will be called from base initializer, so specialized class will create Visual first, then propagate up
        public virtual void CreateVisual()
        {
            void _ensure_view_is_created()
            {
				// "stimulate" View creation if still not there, if there are no XAML bindings it would not be queried or created
				if (SharedObjectMap.SharedObjectMap.RootView != null && View == null)
					SharedObjectMap.SharedObjectMap.getViewFromModel(Model);
			}

            if (Visual != null)
            {
                Visual.onBeforeUpdate += (o) =>
                {
                    //_ensure_view_is_created();

                    // hook view when available
                    if (View != null && View.onNotifyUpdate == null)
                    {
                        View.onNotifyUpdate += _OnInternalUpdate;   // We need a proxy updater that will handle additional views
                        OnUpdate();
                    }

                    BeforeVisualUpdate();

                    //FIXME: disables IsEnabled functionality!
                    //o.IsEnabled = View != null;
                };

                Scene.Add(GetVisual());
            }
        }

		// IMPORTANT: since only ONE view is updated for model, this function will handle other views that would not get the update
		public virtual bool _OnInternalUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            // Debug.WriteLine($"_OnInternalUpdate() {view.GetModel().GetType()}");

            // First notify the real one
            bool ret_original_view = OnUpdate(from_server,view,fieldname, item_index_or_key,update_type);

			// The look for additional ones and notify as well
            // NOTE: we need to get additional views for THIS view because the source one (vertices) do not have it
			// var views = PMViewExt.GetPMViewsFromModel(view.GetModel());
			var views = PMViewExt.GetPMViewsFromModel(this.GetModel());
			if (views == null)
				return ret_original_view;

			bool ret_additional_views = false;
			foreach (var v in views)
                if (v != this)
				    if (v.OnUpdate(from_server, view, fieldname, item_index_or_key, update_type))
					    ret_additional_views = true;

            //TEST: correct?
            return ret_original_view;
        }

		// Updates to the view (server or client)
		public virtual bool OnUpdate(bool from_server=false, IGuidObjectSyncView view=null, string fieldname=null, object item_index_or_key=null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            return false;
        }                
        
        // Before the visual is updated
        public virtual void BeforeVisualUpdate()
        { }

        public virtual void Destroy()
        {
            Scene?.Remove(GetVisual());
            PMViewExt.RemovePMView(this);
        }
    }

	// Base class for editable polygons (structure, outputsurface), based on a SKFreePolygon visual, not to duplicate code
	public abstract class PMEditablePolygonView<model, view> : PMView<model, view>
       where model : Tabula.SharedObjectMap.GuidObject
       where view : IGuidObjectSyncView
    {
        protected SKFreePolygon     VisualPoly;
        protected SKControlPoint    last_cp = null;

        // TODO: specify controlpoint selection colors

        public PMEditablePolygonView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name)
        { }

        protected void SelectControlPoint(SKControlPoint cp)
        {
            last_cp = cp;

            // assign colors
            if (last_cp != null)
                last_cp.Color = new SKColor(255, 255, 0, 120);

            foreach (var s in VisualPoly.Segments)
                foreach (var c in s.ControlPoints)
                    if (c != cp)
                        c.Color = new SKColor(0, 255, 250, 120);

            Scene.Refresh();
        }

        protected void SelectNextControlPoint()
        {
            // get all cp in linear array
            List<SKControlPoint> cpoints = new List<SKControlPoint>();

            foreach (var s in VisualPoly.Segments)
                foreach (var c in s.ControlPoints)
                    cpoints.Add(c);

            if (last_cp == null)
                SelectControlPoint(cpoints[0]);
            else
            {
                var cp_idx = (from c in cpoints where c == last_cp select cpoints.IndexOf(c)).FirstOrDefault();
                SelectControlPoint(cpoints[(cp_idx + 1) % cpoints.Count]);
            }
        }

        protected virtual void OnKey(bool is_down, KeyEventArgs args)
        {
            if (!is_down)
                return;

            int multiplier = 1;

            if (args.KeyModifiers.HasFlag(KeyModifiers.Shift))
                multiplier = 10;

            SKPoint rel_move = new SKPoint(0, 0);

            switch (args.Key)
            {
                case Key.V:
                    SelectNextControlPoint();
                    return;

                /*
                case Key.Delete:

                    // TODO: delete and move to the next
                    if (last_cp != null)
					{
                        last_cp.Destroy();
                        last_cp = null;
                        SelectNextControlPoint();
                    }
                    
                    return;
                */

                case Key.Left:
                    rel_move.X = -1 * multiplier;
                    break;

                case Key.Right:
                    rel_move.X = 1 * multiplier;
                    break;

                case Key.Up:
                    rel_move.Y = -1 * multiplier;
                    break;

                case Key.Down:
                    rel_move.Y = 1 * multiplier;
                    break;
            }

            last_cp?.Move(rel_move);
        }
    }

	// Base class for editable polylines (road tracks), based on a SKFreePolyLine visual, not to duplicate code
	public abstract class PMEditablePolyLineView<model, view> : PMView<model, view>
	   where model : Tabula.SharedObjectMap.GuidObject
	   where view : IGuidObjectSyncView
	{
		protected SKFreePolyLine VisualPoly;
		protected SKControlPoint last_cp = null;

		// TODO: specify controlpoint selection colors

		public PMEditablePolyLineView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name = null) : base(scene, model, name)
		{ }

		protected void SelectControlPoint(SKControlPoint cp)
		{
			last_cp = cp;

			// assign colors
			if (last_cp != null)
				last_cp.Color = new SKColor(255, 255, 0, 120);

			foreach (var s in VisualPoly.Segments)
				foreach (var c in s.ControlPoints)
					if (c != cp)
						c.Color = new SKColor(0, 255, 250, 120);

			Scene.Refresh();
		}

		protected void SelectNextControlPoint()
		{
			// get all cp in linear array
			List<SKControlPoint> cpoints = new List<SKControlPoint>();

			foreach (var s in VisualPoly.Segments)
				foreach (var c in s.ControlPoints)
					cpoints.Add(c);

			if (last_cp == null)
				SelectControlPoint(cpoints[0]);
			else
			{
				var cp_idx = (from c in cpoints where c == last_cp select cpoints.IndexOf(c)).FirstOrDefault();
				SelectControlPoint(cpoints[(cp_idx + 1) % cpoints.Count]);
			}
		}

		protected virtual void OnKey(bool is_down, KeyEventArgs args)
		{
			if (!is_down)
				return;

			int multiplier = 1;

			if (args.KeyModifiers.HasFlag(KeyModifiers.Shift))
				multiplier = 10;

			SKPoint rel_move = new SKPoint(0, 0);

			switch (args.Key)
			{
				case Key.V:
					SelectNextControlPoint();
					return;

				/*
                case Key.Delete:

                    // TODO: delete and move to the next
                    if (last_cp != null)
					{
                        last_cp.Destroy();
                        last_cp = null;
                        SelectNextControlPoint();
                    }
                    
                    return;
                */

				case Key.Left:
					rel_move.X = -1 * multiplier;
					break;

				case Key.Right:
					rel_move.X = 1 * multiplier;
					break;

				case Key.Up:
					rel_move.Y = -1 * multiplier;
					break;

				case Key.Down:
					rel_move.Y = 1 * multiplier;
					break;
			}

			last_cp?.Move(rel_move);
		}
	}

	// Extension of the auto-generated view class with methods to call from xaml/GUI
	// NOTE: since callable methods have a Task signature they cannot be called from CallMethodAction... so many wrappers methods.... hope to get rid of them
	public partial class ModelView : GuidObjectSyncView<Model>
    {
        #region Model

        public void ChangeModeToEdit()
        {
            ChangeMode("edit");
        }

        public void ChangeModeToPlay()
        {
            ChangeMode("play");
        }

        #endregion

        #region Structures

        public void AddStructure()
        {            
            Structures_Add(new Structure()
            {
                position = new Vector2f() { x = new Random().Next(0, 1920), y = new Random().Next(0, 1080) }, // ??
                vertices = new List<Vector2f>()
                {
                    new Vector2f(-100,-100),
                    new Vector2f(100,-100),
                    new Vector2f(100,100),
                    new Vector2f(-100,100)
                }
            });
        }

        // doesn't work? perhaps because my Y is downards
		private	List<Vector2f> JarvisMarch_ReorderVertices(List<Vector2f> points)
			{
				double CrossProduct(Vector2f a, Vector2f b, Vector2f c)
				{
					return (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
				}

				double DistanceSquared(Vector2f a, Vector2f b)
				{
					double dx = a.x - b.x;
					double dy = a.y - b.y;
					return dx * dx + dy * dy;
				}


				if (points.Count < 3)
				{
					return points;
				}

				List<Vector2f> hull = new List<Vector2f>();

				Vector2f leftMost = points[0];

				for (int i = 1; i < points.Count; i++)
				{
					if (points[i].x < leftMost.x)
					{
						leftMost = points[i];
					}
				}

				Vector2f current = leftMost;
				Vector2f next;

				do
				{
					hull.Add(current);
					next = points[0];

					for (int i = 1; i < points.Count; i++)
					{
						double orientation = CrossProduct(current, next, points[i]);

						if (orientation > 0 || (orientation == 0 && DistanceSquared(current, next) < DistanceSquared(current, points[i])))
						{
							next = points[i];
						}
					}

					current = next;
				} while (current != leftMost);

				return hull;
			}

        public void RemoveStructure(StructureView s)
        {
            Structures_Remove(Structures.IndexOf(s));
        }

        public async Task AddStructuresFromCalibration(
            ImageData image_data,
            bool delete_existing_structures = true)
        {
            // current structures should be deleted first
            if (delete_existing_structures)
            {
                await Structures_ClearAsync();
			}

            // List<PMStructureView> new_structures_views = new List<PMStructureView>();

            var shapes = image_data.Shapes;

            // HOMOGRAPHY
            // If we had exactly 4 markers the shapes are already transformed by the found homography.
            // If not, we obtained shapes in raw coordinates (image pixels) and we must transform it by our own homography           

            // the shapes that are found to be markers
            List<ImageData.Shape> shapes_markers = new List<ImageData.Shape>();

            // OLD CODE: check shapes that are markers
            /*
            if (image_analysis.markers.Count != 4)
            {
                App.Client.View.Screen.CreateHomography();

				foreach (var s in shapes)
				{
                    // TODO: in this case also markers are in the shapes, so we could find and skip them if the shape polygon contains the marker center in image coords
                    Vector2f[] vertices = new Vector2f[s.points.Count];
                    for (int i = 0; i < s.points.Count; i++)
                        vertices[i] = new Vector2f((float) s.points[i].x, (float) s.points[i].y);

                    Tabula.PWG.SARGAME.Utils.Polygon polygon = new PWG.SARGAME.Utils.Polygon(vertices);

                    // cycle through markers
                    foreach (var m in rated_makers)
                        if (polygon.PointInPolygon((float)m.position.x, (float)m.position.y))
                        {
                            shapes_markers.Add(s);
                            break;
                        }

                    if (shapes_markers.Contains(s))
                        continue;

					foreach (var p in s.points)
					{
                        float warp_x = 0, warp_y = 0;
                        App.Client.View.Screen.HomographyTransform((float) p.x, (float) p.y, ref warp_x, ref warp_y);

                        p.x = warp_x;   
                        p.y = warp_y;
					}
				}

			}
            */


			// NOTE: Shapes coordinates are relative to the crop, so we must add it
            // NOTE: skip shapes that are markers! special case
			foreach (var s in shapes)
            {
                if (shapes_markers.Contains(s))
                    continue;

                foreach (var p in s.points)
                {
                    p.x += image_data.screen_crop.x;
					p.y += image_data.screen_crop.y;
				}    
            }

            // CommitBegin();

            try
            {
                foreach (var s in shapes)
                {
					if (shapes_markers.Contains(s))
						continue;

					// compute center
					var center = new Vector2f(0, 0);
                    var vertices = new List<Vector2f>();

                    for (int i = 0; i < s.points.Count; i++)
                    {
                        center.x += (float)s.points[i].x;
                        center.y += (float)s.points[i].y;

                        vertices.Add(new Vector2f((float)s.points[i].x, (float)s.points[i].y));
                    }

                    center.x /= (float)s.points.Count;
                    center.y /= (float)s.points.Count;

                    foreach (var v in vertices)
                    {
                        v.x -= center.x;
                        v.y -= center.y;
                    }

                    /*
                    var new_guid = Structures_Add(new Structure()
                    {
                        position = center,
                        vertices = vertices
                    });

                    new_structures_guids.Add(new_guid);
                    */

                    var new_sv = await Structures_AddAsync(new Structure()
                    {
                        position = center,
                        vertices = vertices
                    });

                    /*
                    if (new_sv != null)
                    {
                        var pmview = PMViewExt.GetPMViewsFromView<PMStructureView>(new_sv).FirstOrDefault();
                        if (pmview != null)
						    new_structures_views.Add(pmview);
                    }*/
                }
            }
            catch(Exception ex)
            {
                SARGAME.App.logException("AddStructuresFromImageAnalysis", ex);
                return;
            }

			// CommitEnd();

            // wait for all the pmviews to be created
            /*
            foreach (var g in new_structures_guids)
            {
				var view = await PMViewExt.WaitForPMViewWithGuid<PMStructureView>(g,5000);
                if (view == null)
                {
                    // General error
                    SARGAME.App.logError($"AddStructuresFromImageAnalysis() timeout creating structure {new_structures_guids.IndexOf(g)}/{new_structures_guids.Count}");
                    return null;
                }

                new_structures_views.Add(view);
			}
            */

            // Refresh all views
			Tabula.SharedObjectMap.SharedObjectMap.InitializeAllViews();

            //return new_structures_views;
		}

		#endregion


		#region Entities

		// Deprecated: use API.CreateEntity
		/*
        public long AddEntity(EntityView parent, Entity e)
		{
            if (parent == null)
                return Entities_Add(e);
            else
                return parent.items_Add(e);
		}

        // Deprecated: use API.CreateEntity        
		public async Task<EntityView> AddEntityAsync(Entity e, EntityView parent=null)
		{
			if (parent == null)
				return await Entities_AddAsync(e);
			else
				return await parent.items_AddAsync(e);
		}*/

		public void RemoveEntity(EntityView ev)
		{
            var parent = ev.GetParent();

            switch (parent)
			{                
                case ModelView model: model.Entities_Remove(model.Entities.IndexOf(ev)); break;
                case EntityView entity: entity.items_Remove(entity.items.IndexOf(ev)); break;
            }
		}

		public async Task RemoveEntityAsync(EntityView ev)
		{
			var parent = ev.GetParent();

			switch (parent)
			{
				case ModelView model: await model.Entities_RemoveAsync(model.Entities.IndexOf(ev)); break;
				case EntityView entity: await entity.items_RemoveAsync(entity.items.IndexOf(ev)); break;
			}
		}

		#endregion


		#region Players

		public void PlayerJoin()
        {
            Task.Run(async () =>
            {
                // var res = await NetworkPlayerJoin(App.Client.ConnectedClientInfo,"0000");
            });
        }

		#endregion

		
    }
}
