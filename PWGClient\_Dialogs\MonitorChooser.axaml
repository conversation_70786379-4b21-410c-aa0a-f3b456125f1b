<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			  xmlns:suki="clr-namespace:SukiUI.Controls;assembly=SukiUI"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:dialogHost="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:sargame="clr-namespace:Tabula.PWG.SARGAME"
			 xmlns:tabula_unity="clr-namespace:Tabula.Unity;assembly=PWGClient_Core"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
			 Width="{Binding DialogMediumSize.Width}" Height="{Binding DialogMediumSize.Height}"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.MonitorChooser" >

	<dialogHost:DialogHost Identifier="monitorchooser_dialog" Background="{DynamicResource ExpanderBackground}" DialogMargin="-10" DisableOpeningAnimation="True">
		<dialogHost:DialogHost.DialogContent>
		</dialogHost:DialogHost.DialogContent>

		<Border BorderBrush="White" BorderThickness="1" Background="{DynamicResource ExpanderBackground}">
			<Grid RowDefinitions="Auto,*,Auto,Auto">
				<StackPanel Grid.Row="0"  Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
					<materialIcons:MaterialIcon Width="30" Height="30" Kind="Projector" />
					<Label  Content="Choose a Projector" FontSize="14" FontWeight="Bold" Margin="10" HorizontalAlignment="Center"/>
				</StackPanel>
				<Grid Grid.Row="1" Margin="10">
					<ScrollViewer HorizontalAlignment="Center" VerticalAlignment="Center" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Hidden">
						<ItemsControl Name="displays_control" ItemsSource="{Binding UnityToolData.displays}" HorizontalAlignment="Center" VerticalAlignment="Center">

							<ItemsControl.ItemsPanel>
								<ItemsPanelTemplate>
										<StackPanel Orientation="Horizontal"/>
								</ItemsPanelTemplate>
							</ItemsControl.ItemsPanel>

							<ItemsControl.ItemTemplate>
								<DataTemplate DataType="{x:Type tabula_unity:Display}">
									<StackPanel Orientation="Vertical">	
										<Button Width="200" Height="112" Margin="10" Padding="10" Click="bt_choose_Click">
											<TextBlock Text="{Binding Description}" VerticalAlignment="Center" HorizontalAlignment="Center" TextAlignment="Center" FontSize="14" FontWeight="Bold"/>
										</Button>
										<TextBlock Text="current" FontStyle="Italic" Foreground="Cyan" Margin="0,3,0,0" HorizontalAlignment="Center" IsVisible="{Binding IsCurrentScreen}"/>
									</StackPanel>
								</DataTemplate>
							</ItemsControl.ItemTemplate>

						</ItemsControl>
					</ScrollViewer>
				</Grid>
				
				<!-- info message -->
				<Border Grid.Row="2" Classes="Card" IsVisible="{Binding MonitorInfoMessage, Converter={StaticResource StringToBoolConverter}}" HorizontalAlignment="Stretch" Margin="5" Padding="5">
					<TextBlock Text="{Binding MonitorInfoMessage}" HorizontalAlignment="Center"/>
				</Border>

				<StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
					<Button Margin="20" Classes="Rounded" HorizontalAlignment="Center"
							Click="bt_cancel_Click">
						<TextBlock Text="Cancel"/>
					</Button>

					<Button Margin="20" Classes="Rounded" HorizontalAlignment="Center"
							Click="bt_refresh_Click">
						<TextBlock Text="Refresh"/>
					</Button>

					<Button Name="bt_custom_resolution" Margin="20" Classes="Rounded" HorizontalAlignment="Center"
							Content="Custom"
							ToolTip.Tip="Use custom and multi-screen resolution, need Business license"
							Click="bt_custom_resolution_Click">
						<TextBlock Text="Custom"/>
					</Button>

					<!-- Custom Resolution Popup -->
					<Popup Name="custom_resolution_popup" PlacementTarget="{Binding #bt_custom_resolution}" DataContext="{Binding}">
						<Border Classes="Card" Background="{DynamicResource ExpanderBackground}" Padding="10" CornerRadius="10">

							<StackPanel Orientation="Vertical">

								<TextBlock Text="Choose custom resolution" HorizontalAlignment="Center" TextAlignment="Center" Margin="10"/>

								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Width" VerticalAlignment="Center"/>
									<TextBox Grid.Column="1"  Classes="property" Text="{Binding CustomResolutionWidth}"/>
								</Grid>

								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Height" VerticalAlignment="Center"/>
									<TextBox Grid.Column="1"  Classes="property" Text="{Binding CustomResolutionHeight}"/>
								</Grid>

								<Grid ColumnDefinitions="*,*">
									<TextBlock Grid.Column="0"  Classes="property" Text="Spout" VerticalAlignment="Center"/>
									<CheckBox Grid.Column="1"  Classes="property" IsChecked="{Binding CustomResolutionSpout}" IsEnabled="{Binding CanUseSpout}"/>
								</Grid>
								

								<Button Content="Start" Click="bt_custom_resolution_start_Click" Margin="10"/>

								<Button Content="Cancel" Click="bt_custom_resolution_cancel_Click" Margin="10"/>

							</StackPanel>
						</Border>
					</Popup>
					
				</StackPanel>


			</Grid>

		</Border>
	</dialogHost:DialogHost>
</UserControl>
