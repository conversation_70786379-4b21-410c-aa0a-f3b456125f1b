﻿using Microsoft.CodeAnalysis;
using NetSparkleUpdater.Enums;
using NetSparkleUpdater.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;

namespace NetSparkleUpdater
{
	public class NetSparkleSARGAMELogWriter : ILogger
	{
		public static string tag = "netsparkle:";

		public NetSparkleSARGAMELogWriter()
		{
			PrintDiagnosticToConsole = false;
		}

		public NetSparkleSARGAMELogWriter(bool printDiagnosticToConsole)
		{
			PrintDiagnosticToConsole = printDiagnosticToConsole;
		}

		public bool PrintDiagnosticToConsole { get; set; }

		public virtual void PrintMessage(string message, params object[] arguments)
		{
			if (PrintDiagnosticToConsole)
			{
				Console.WriteLine(tag + " " + message, arguments);
				Debug.WriteLine(tag + " " + message, arguments);
			}
			else
			{
				Trace.WriteLine(string.Format(tag + " " + message, arguments));

				if (SARGAME.App != null)
					SARGAME.App.log(string.Format(tag + " " + message, arguments));
			}
		}
	}
}
