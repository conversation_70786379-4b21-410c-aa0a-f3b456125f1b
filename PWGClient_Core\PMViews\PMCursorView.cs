﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.RPC;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

// Base class for a visual view of a GuidObject class

// Visually modeled as a rectangle now, ready to accept sprites etc..

namespace Tabula.PMCore
{
    public class PMCursorView : PMView<Tabula.PMCore.Cursor, Tabula.PMCore.CursorView>
    {
        public PMCursorView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name="") : base(scene, model, name) { }        

        public override void CreateVisual()
        {
            Visual = new SKRectangle(Scene, new SKRect(0, 0, 100, 100))
            {
                Color = new SKColor(0, 0, 0, 255),
                ColorHover = new SKColor(0, 0, 255, 255),
                ColorDragging = new SKColor(255, 255, 255, 150)
            };

            Visual.IsHitTestVisible = false;
            Visual.IsDrawnInScene = false;

            /*
            Visual.onMove += (o, pos) =>
            {
                var center = Visual.GetObjectCenter();

                View.CommitBegin();
                View.position.x = center.X;
                View.position.y = center.Y;
                View.CommitEnd();
            };
            */

            Visual.onUpdate += (o) =>
            {          
                if (Scene.IsMouseInScene)                
                {
                    // using GuidObjectFastUpdateList there is model value setting and equality check to avoid sending, therefore simulating a view update
                    var updates = new Shared.GuidObjectFastUpdateList()
                    {
                        CheckForEquality = true,
                        UpdateModel = true,
                        UpdateView = false
                    };

                    updates.Add(Model, nameof(Model.draw), Model.draw, SARGAME.iMainWindowUI.DrawCursor && !Scene.IsDraggingObject);
                    updates.Add(Model.position, nameof(Model.position.x), Model.position.x, Scene.MousePosition.X);
					updates.Add(Model.position, nameof(Model.position.y), Model.position.y, Scene.MousePosition.Y);

                    SARGAME.iMainWindowUI.SyncGuidObjectsDirectWithoutNotify(updates.Updates, reliable: false);

                    /*
					View.CommitBegin();
                    View.draw = SARGAME.iMainWindowUI.DrawCursor && !Scene.IsDraggingObject;
                    View.position.x = Scene.MousePosition.X;
                    View.position.y = Scene.MousePosition.Y;
                    View.CommitEnd();
                    */
                }
            };

            base.CreateVisual();
        }

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            var v2 = View.Model.position;
            Visual.SetPosition(new SKPoint(v2.x, v2.y));

            return true;
        }       
    }
}
