﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;

namespace Tabula.PMCore
{
    public class PMScreenMarkerView : PMView<Tabula.PMCore.ScreenMarker, Tabula.PMCore.ScreenMarkerView>
    {
        public PMScreenMarkerView(SKScene scene, Tabula.SharedObjectMap.GuidObject model, string name=null) : base(scene, model, name) { }        

        public override void CreateVisual()
        {
			/*
            int index = (Model.__Parent as Screen).calibration_points.IndexOf(Model) + 1;

            Visual = SKSprite.CreateFromEmbeddedResource(Scene, new SKRect(0, 0, 100, 100), 
                $"Tabula.PMCore.Images.embedded.calibration_point_{index}.png");
            */

			// NOTE: now a single image is OK because the order of markers is always sorted on the server side
			// TODO: rear projection?

			Visual = SKSprite.CreateFromEmbeddedResource(Scene, new SKRect(0, 0, 100, 100),
			   $"Tabula.PMCore.Images.embedded.calibration_point.png");

			Visual.onMove += (o, pos) =>
            {
                var center = Visual.GetPosition();

                View.CommitBegin();
                View.position.x = center.X;
                View.position.y = center.Y;
                View.CommitEnd();
            };            

            base.CreateVisual();
        }

        public SKRect Rect => (Visual as SKSprite).GetBoundingBox();

        public override bool OnUpdate(bool from_server = false, IGuidObjectSyncView view = null, string fieldname = null, object item_index_or_key = null, SharedObjectMap.UpdateType update_type = SharedObjectMap.UpdateType.SetField)
        {
            var v2 = View.Model.position;
            Visual.SetPosition(new SKPoint(v2.x, v2.y));

            return true;
        }       
    }
}
