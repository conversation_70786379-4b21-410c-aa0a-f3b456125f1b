﻿//TABULA_GUID:{0E377A94-5215-4BB9-98BE-04322B2D92AD}
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using Tabula.Log;
using System.IO;
using Tabula.RPC;
using Newtonsoft.Json;
using System.Net.NetworkInformation;
using System.Net;
using System.Net.Sockets;
using Tabula.API;
using System.IO.Compression;
using System.Threading;
using System.Collections.Concurrent;
using kcp2k;
using System.Diagnostics;
using System.Reflection;


#if TABULARPC_OSC
using Tabula.OscCommand;
#endif


#if SHAREDOBJECTMAP_SERVER
using Tabula.APIServer;
#endif


// SharedObjectMap_ClientServer
// Wraps all the complexity of hooking a SharedObjecMap to APIServer RPC,OSC and server->client updates

// Defines
//  SHAREDOBJECTMAP_CLIENT: includes the client
//  SHAREDOBJECTMAP_SERVER: includes the server (depends on APIWrapper and rpc/osc/http servers)

// NOTE: You MUST add to the project a view local implementation helper classes (dispatcher etc)

namespace Tabula.SharedObjectMap
{
	public static class ClientServer_Constants
    {
        public const int RPCServerPort = 16100;
        public const int RPCServerUpdatesPort = RPCServerPort+10; // server->client
        public const int OSCServerPort = RPCServerPort+100;
        public const int HTTPServerPort = 8080; // ?
	}    

    public class ClientInfo
    {
        // KCP_PORTING:
        public int          connection_id;  // the socket connection where to send data, this is also the Client ID
        public IPEndPoint   remote_endpoint;

        public bool         receive_updates;    // is server sending model updates to this client
        public bool         receive_commands;
    }

    // The interface must be implemented by the generated clients
    public interface ISharedObjectMap_ManagedClient
    {
        Task<int> checkConnection(IPEndPoint remote_ep, int number);
        Task<GuidObject> getModel(IPEndPoint remote_ep=null);
        Task<byte[]> getModelAsCompressedBytes(IPEndPoint remote_ep = null);
        Task setModel(IPEndPoint remote_ep, GuidObject model);
        Task<Tabula.SharedObjectMap.ServerUpdateResult> GetUpdates(IPEndPoint remote_ep, long client_update_sequence);
        //Task<ClientInfo> ConnectToUpdateServer(IPEndPoint remote_ep, string address, int port, bool receive_updates = true, List<string> selective_updates = null);
        //Task<ClientInfo> DisconnectFromUpdateServer(IPEndPoint remote_ep, ClientInfo client_info);
        Task<Tabula.SharedObjectMap.SingleUpdateResult> UpdateField(IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidUpdate update);
        Task<Tabula.SharedObjectMap.SingleUpdateResult[]> UpdateFieldBatch(IPEndPoint remote_ep, List<Tabula.SharedObjectMap.GuidUpdate> updates, bool get_results = false);

		// new optimized version, the missing result will skip any wait in the receive queue
		// These are generated thanks to GenerateNoResult in APIMethodAttribute derived classes
		Task UpdateField_NoResult(IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidUpdate update);
		Task UpdateFieldBatch_NoResult(IPEndPoint remote_ep, List<Tabula.SharedObjectMap.GuidUpdate> updates, bool get_results = false);


		// OSC
#if TABULARPC_OSC
        void OSC_UpdateField(IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidUpdate update);
        void OSC_UpdateFieldBatch(IPEndPoint remote_ep, List<Tabula.SharedObjectMap.GuidUpdate> updates, bool get_results = false);
        void OSC_UpdateFieldDirectDouble(IPEndPoint remote_ep, long guid, string fieldname, double value, int index);
        void OSC_UpdateFieldDirectFloat(IPEndPoint remote_ep, long guid, string fieldname, float value, int index);
        void OSC_UpdateFieldDirectInt(IPEndPoint remote_ep, long guid, string fieldname, int value, int index);
#endif
	}

#if SHAREDOBJECTMAP_CLIENT

	public class ConnectionInfo
    {
        public string   server_address = "127.0.0.1";       // the server the client will connect to
        public int      rpc_port = ClientServer_Constants.RPCServerPort;
        public int      osc_port = ClientServer_Constants.OSCServerPort;
        public int      base_port = 0;

        public string   local_address="127.0.0.1";          // matching address for local connection to the server (if existing)

        // Server Updates
        public bool     receive_updates = false;
        public bool     receive_commands = false;   

        // Unreliable
        public bool     PreferDirectSync = false;       // will use OSC for updates not needing result (with persistent OSC transmitter, fast!)


        
        public string ServerAddress => server_address.Trim();  

        public int ServerRpcPort => rpc_port + base_port;
        public int ServerOscPort => osc_port + base_port;
    }

    public class SharedObjectMap_Client<model, model_view, managed_rpc_client>
        where model : GuidObject
        where model_view : IGuidObjectSyncView, new()
        where managed_rpc_client : ManagedClient, ISharedObjectMap_ManagedClient, new()

    {
        public model Model;
        public model_view View;

		// Events
		public Action<string> onLog;
		public Action<string, Exception> onLogException;

        public Action<bool> OnConnectionChanged;  // used for connection or disconnection
        public Action       OnModelCleared;       // called after model clearing to allow for refresh at higher levels

        public Action<KcpChannel, Shared.KcpMessage> OnCustomMessageReceived;  // to intercept non default messages

        // incoming file transfer, return false to deny it
		public Func<Shared.FileTransfer, bool> OnFileTransferRequest
        {
            get => Client.Client.OnFileTransferRequest; 

            set
            {
                Client.Client.OnFileTransferRequest = value;
			}

        }

		// result and outcome of an incoming file transfer
		public Action<Shared.FileTransfer, bool> OnFileTransferEnded
        {
            get => Client.Client.OnFileTransferEnded;

            set
			{
                Client.Client.OnFileTransferEnded = value;
			}
		}

		// NOTE Dispatcher is as platform-dependent static class (WPF,Avalonia,Unity) needs the local implementation .cs file

        public managed_rpc_client Client { get; private set; }

#if TABULARPC_OSC
        public int  OSCRetry = 2;

        // OSC Client is kept persistent for performance
        private managed_rpc_client client_osc;
        public managed_rpc_client ClientOSC
        {
            get
            {
                if (client_osc == null)
                    client_osc = (managed_rpc_client)new managed_rpc_client().Create(ClientConnectionInfo.server_address, ClientConnectionInfo.ServerRpcPort, ClientConnectionInfo.ServerOscPort, ClientConnectionInfo.OSCRetry);

                return client_osc;
            }
        }
#endif

		// Connection setup
		public ConnectionInfo ClientConnectionInfo { get; private set; }

        private long UpdateSequence = 0L;

        // Server->client commands
        public Action<RPCCall> OnClientCommand;

        public bool IsConnected => Model != null;

        // Connection is IP based, the ClientInfo will embed the server generated id
        // NOTE: now every client MUST have a different IP (so in the same wifi/vpn, not behind nat/internet)
        // TODO: at the moment IPEndPoint is used to lookup ClientInfo, in the future ClientInfo will be sent in every call
        //public async Task<ClientInfo> Connect(string address, int _base_port=0, bool receive_updates = true)

        // RETURNS:
        // -1: error creating native client
        // -3: error checking connection
        public async Task<int> Connect(ConnectionInfo info, CancellationToken token=default)
        {
            ClientConnectionInfo = info;

            //OSC.NET.OSCMessage.OnObjectSerialize += JsonConvert.SerializeObject;
            //OSC.NET.OSCMessage.OnObjectDeserialize += JsonConvert.DeserializeObject;

            // In case we are using local connection let's choose the local IP that matches the server's network
            // NOTE: this was needed before KCP
            /*
            var local_ip_addresses = GetLocalIPAddressesInTheSameNetwork(ClientConnectionInfo.ServerAddress, "*************");

            if (local_ip_addresses.Count() > 0)
            {
				ClientConnectionInfo.local_address = local_ip_addresses.FirstOrDefault().ToString();
			}

            // debug
#if DEBUG
            foreach (var ip in local_ip_addresses)
                Logger.DefaultLog.WriteLine($"LocalIP: {ip}");

            Logger.DefaultLog.WriteLine($"Chosen local address: {ClientConnectionInfo.local_address}");
#endif
            */

			// Create the client

#if TABULARPC_OSC
			Client = (managed_rpc_client) new managed_rpc_client().Create(
						address: ClientConnectionInfo.server_address,
						port: ClientConnectionInfo.ServerRpcPort,
						osc_retry: ClientConnectionInfo.OSCRetry);
#else
            Client = (managed_rpc_client) new managed_rpc_client().Create(
						address: ClientConnectionInfo.server_address,
						port: ClientConnectionInfo.ServerRpcPort);
#endif

			Client.OnConnected += OnConnected;
			Client.OnDisconnected += OnDisconnected;

			// Initialize manage client so that underlying client is created (in KCP there is only one) this fires OnConnected
			var client = await Client.GetClient(token);

            if (client == null)
            {
                // client cannot connect, or user canceled
                return -1;
            }

			// Handle higher level messages (updates, commands)
			Client.Client.OnCustomKcpMessageReceived += _oncustomKCPmessagereceived;			

			// Test a call to check if RPC works
			int connection_test = await Client.checkConnection(null, 13724);
            if (connection_test != (13724+1))
			{
                return -3; // error checking connection 
			}            

            // Configure updates and command reception, through special messages
            if (info.receive_updates)
                Shared.SendServerUpdatesToggle_KCPClient(Client.Client.NativeClient, true);
            else if (info.receive_commands)
                Shared.SendServerCommandsToggle_KCPClient(Client.Client.NativeClient, true);
            
            return 1;
        }

        private void _oncustomKCPmessagereceived(KcpChannel channel, Shared.KcpMessage msg)
        {
            switch (msg.header.code)
            {
                // NOTE: does not support chunked messages
                case Shared.KCPMSG_SRV_UPDATE:
                    try
                    {
                        if (msg.header.chunk_count > 1)
                            throw new Exception("SharedObjectMap_Client cannot receive server updates in chunked messages");

                        var server_updates = msg.DecodePayloadAsJson<RPCCall>();

                        // TODO: lock?
                        ReceiveUpdates(server_updates);
                    }
                    catch (Exception ex)
                    {
                        Logger.DefaultLog.logException("OnCustomKcpMessageReceived() KCPMSG_SRV_UPDATE", ex);
                    }

					break;

				case Shared.KCPMSG_GUID_FASTUPDATE:
					// NEW: receiving fast updates from server to model wihtout notify!

					if (msg.header.chunk_count > 1)
						throw new Exception("SharedObjectMap_Client cannot receive server updates in chunked messages");
	
					var updates = msg.DecodePayloadAsJson<List<Tabula.RPC.Shared.GuidObjectFastUpdate>>();

					// not in UI thread, just updating values and setting __is_dirty, no notifications
					if (updates != null)
						foreach (var u in updates)
							SharedObjectMap.UpdateFieldDirect(null, u.guid, u.field, u.value);

					break;

				default:
                    OnCustomMessageReceived?.Invoke(channel, msg);
                    break;
            }
        }
		
        public async Task Disconnect()
        {
            // Will trigger deconnection event
            Client?.Dispose();
            Client = null;
        }

        private void OnConnected()
        {
			OnConnectionChanged?.Invoke(true);
		}

        private void OnDisconnected() 
        {
			//OSC.NET.OSCMessage.OnObjectSerialize -= JsonConvert.SerializeObject;
			//OSC.NET.OSCMessage.OnObjectDeserialize -= JsonConvert.DeserializeObject;

			OnConnectionChanged?.Invoke(false);

			Client.Client.OnCustomKcpMessageReceived -= _oncustomKCPmessagereceived;
			Client.Client.OnConnected -= OnConnected;
			Client.Client.OnDisconnected -= OnDisconnected;

			Clear();			
		}

        public void Clear()
		{
            Model = null;
            Dispatch(() => SharedObjectMap.ClearModel());

            OnModelCleared?.Invoke();
        }

        // process updates directly from server
        private async Task<RPCResult> ReceiveUpdates(RPCCall call)
        {
			//lock (UpdateClient)
            {
               
				if (call.Name == "updates_test")
                {
                    return RPCResult.Create(call, (bool)true);
                }
                else if (call.Name == "updates")
                {   
                    //FIXME: if model is not received yet we cannoot process updates!
                    if (Model == null)
					{
                        return RPCResult.CreateError(call, "model is null");
					}

                    //var server_update = (call.Arguments[0] as JToken).ToObject<ServerUpdateResult>();
                    var server_update = (ServerUpdateResult) SharedObjectMap.ConvertAny(call.Arguments[0], typeof(ServerUpdateResult));

                    await Task.Run(async () =>
                    {                 
                        // Process updates in dispatcher (if defined), as they will trigger Views
                        await Dispatcher.DispatchAsync(() =>
                        {
                            foreach (var u in server_update.Updates)
                                SharedObjectMap.UpdateField(null, u);
                        });

                    });

                    // TODO: interlocked increment
					UpdateSequence = server_update.UpdateSequence;

					return RPCResult.Create(call, UpdateSequence);
				}
                else
                {
                    // anything else is treated like a server->client command
                    // KCP_PORTING: todo!
                    OnClientCommand?.Invoke(call);

                    return RPCResult.Create(call);
				}
            }

            
        }

        // Helper: runs action within a dispatcher (client-tech dependent wpf,avalonia..)
        public async Task Dispatch(Action action)
        {
            // Process updates in dispatcher, as they will trigger Views
            await Dispatcher.DispatchAsync(action);                
        }        

        private async Task<model> _get_model(bool compatibility_mode)
		{
            if (compatibility_mode)
            {

                var model = (model)await Client.getModel();

				return model;
			}
            else
            {
                //REFACTOR: this was a quick hack that turned out not needed, but it's anyway good, to refactor
                //NOTE: sometimes that doesn't work and returns a bad string (to look into)
                var bytes = await Client.getModelAsCompressedBytes();
                var model_string = DecompressString(bytes);
                var model = (model)JsonConvert.DeserializeObject<model>(model_string);

				return model;
			}
        }


        // This will effectivley replace the existing Model and re-create the View, tipically done only once
        public async Task<model> ReceiveModel(bool compatibility_mode = false, bool receive_updates = true, Action<model> beforeSetModel=null)
        {
            var model = await _get_model(compatibility_mode);

            // chance to modify the model before setting it (ex: templates)
            beforeSetModel?.Invoke(model);

            SetModel(model);

            // Sets up remote updates
            Shared.SendServerUpdatesToggle_KCPClient(Client.Client.NativeClient, receive_updates);

            return Model;
        }

		// Will setup connection (update server) without receiving model and creating views
		public void ConnectOnly(bool receive_commands = true)
        {
			// Optonally ask the server to send commands, in order toreceive custom RPCCalls 
			// updates are excluded, since there is no received model

			Shared.SendServerCommandsToggle_KCPClient(Client.Client.NativeClient, receive_commands);
		}

        public async Task<int> TransferFile(FileInfo file_info, int timeout = -1)
            => await Client.Client.TransferFile(file_info, timeout);

		//REFACTOR: move into shared utilities?
        // version2, using CopyTo() for biggest streams
		private string DecompressString(byte[] gZipBuffer)
		{
			int dataLength = BitConverter.ToInt32(gZipBuffer, 0);
			using (var memoryStream = new MemoryStream(gZipBuffer, 4, gZipBuffer.Length - 4))
			using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
			using (var resultStream = new MemoryStream())
			{
				gZipStream.CopyTo(resultStream);
				byte[] buffer = resultStream.ToArray();
				return Encoding.UTF8.GetString(buffer, 0, dataLength);
			}
		}

		// Sets directly
		public void SetModel(model model)
		{
            Model = model;
            SharedObjectMap.InitializeModelAsClient(Model);

            View = SharedObjectMap.GetRootView<model_view>();

            // TEST: Initialize all views (in order to receive all notifications on client)
            SharedObjectMap.InitializeAllViews();

            // Setup sync methods
            View.onViewSync = Sync;
            View.onViewSyncBatch = SyncBatch;

            // TEST: hook object added event, in order to refresh views immediatel
            // This should fix problems of __view remaining null, and not giving notifications ALSO for runtime created views
            // suspended because path cannot be found.. strange
            /*
            SharedObjectMap.notifyObjectAdded = (o) =>
            {
				var view = SharedObjectMap.getViewFromModel(o);
                Debug.WriteLine($"notifyObjectAdded guid:{o.__guid} view:{view}");
			};
            */
        }

        // Will just download a copy of the model from the server, without changing the existing client-side model
        public async Task<model> DownloadModel(bool compatibility_mode = false)
        {
            try
            {
                var _model_copy = await _get_model(compatibility_mode);

                return _model_copy;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        // Real implementation for sync event
        public SingleUpdateResult Sync(GuidUpdate update, bool need_result)
        {
            // Communicate with server,Task.Run is needed to accomodate also UI dispatchers
            try
            {
                // TODO: timeout?                
                if (need_result)
                {
                    return Task.Run(async () => await Client.UpdateField(null,update)).Result;
                }
                else
                {
                    //KCP_PORTING: TEST using KCP unreliable instead of OSC ?
                    // Well, yes, it's not super fast only convenience is to use the same port

                    // NOTE: there is now no real difference between DirectSync as KCP is quick
                    // TEST: Using _NoResult stubs to avoid waiting for a not-used result

                    if (!update.Flags.HasFlag(UpdateFlags.NoDirectSync) &&
                        (ClientConnectionInfo.PreferDirectSync || update.Flags.HasFlag(UpdateFlags.DirectSync)))
                    {
                        update.IsDirectSync = true; // will result in an unreliable call
                        Client.UpdateField_NoResult(null, update);
                    }
                    else
                        //Task.Run(() => Client.UpdateField(null, update));
                        Client.UpdateField_NoResult(null, update);

					return SingleUpdateResult.OK;
                }
            }
            catch (Exception ex)
            {
                Logger.DefaultLog.logException("Sync", ex);
                return SingleUpdateResult.ResultGenericError;
            }
        }

        public SingleUpdateResult[] SyncBatch(List<GuidUpdate> updates, bool get_results)
        {                        
            try
            {

#if TABULARPC_OSC
                // if OSC is to be preferred, use it only if all updates do not need a result          
                if (ClientConnectionInfo.PreferOSC)
                {
                    if (!updates.Any(u => u.Flags.HasFlag(UpdateFlags.NeedResult)))
                    {
                        ClientOSC.OSC_UpdateFieldBatch(null, updates);
                        return null;
                    }
                }
#endif
                // TODO: timeout?             
                // KCP_PORTING: waiting the results is just slow and useless.. anyway

                if (get_results)
                    return Task.Run(async () => await Client.UpdateFieldBatch(null, updates, get_results)).Result;
                else
                {
                    //Task.Run(async () => await Client.UpdateFieldBatch(null, updates, get_results));
                    // TEST: using _NoResult stub so there will be no wait in queues
                    Client.UpdateFieldBatch_NoResult(null, updates, get_results);
					return null;
                }
			}
            catch (Exception ex)
            {
                Logger.DefaultLog.logException("SyncBatch", ex);
                return null;
            }
        }

		//NEW: KCP way for direct-sync of primitive data, without notifying on the server
		// This should be the quickest method to update values on the server.

		#region Sync GuidObjects Direct Client -> Server

        // higher level
        public void SendFastUpdates(params (GuidObject guidobject, string field_name, object old_value, object new_value)[] updates)
        {
            var list = new Shared.GuidObjectFastUpdateList();   // standard options

            foreach (var u in updates)
                list.Add(u.guidobject, u.field_name, u.old_value, u.new_value);

            SyncGuidObjectsDirectWithoutNotify(list.Updates);
		}

        // lower level
		public void SyncGuidObjectsDirectWithoutNotify<T>(long guid, string field, object value)
            => SyncGuidObjectsDirectWithoutNotify(new Shared.GuidObjectFastUpdate() { guid = guid, field = field, value = value });

		public void SyncGuidObjectsDirectWithoutNotify(Shared.GuidObjectFastUpdate update)
            => SyncGuidObjectsDirectWithoutNotify(new List<Shared.GuidObjectFastUpdate>() { update });

		public void SyncGuidObjectsDirectWithoutNotify(List<Shared.GuidObjectFastUpdate> updates, bool reliable = true)
		{
            if (updates.Count == 0)
                return;

            try
            {
                Shared.SendJsonObject_KCPClient(Client.Client.NativeClient, updates, reliable ? KcpChannel.Reliable : KcpChannel.Unreliable, Shared.KCPMSG_GUID_FASTUPDATE, 0);
            }
            catch(Exception ex)
            {
				Logger.DefaultLog.logException("SyncGuidObjectsDirectWithoutNotify", ex);
			}
        }

		#endregion

		public void GenerateViewContract(string name_space, string output_folder)
        {
            SharedObjectMap.GenerateViewsFromInstance(typeof(model), name_space, name_space, Path.Combine(output_folder, "view_generated.cs"));
        }

    #region Utilities

        // gets local ip addresses, optionally only the ones in the same network/subnet as the specified one
        List<IPAddress> GetLocalIPAddresses(string ip_address_preferred = null, string subnet = null)
        {
            // special case, loopback
            if (ip_address_preferred == "127.0.0.1")
                return new List<IPAddress>() { IPAddress.Parse("127.0.0.1") };

            List<IPAddress> local_ip_addresses = new List<IPAddress>();
            foreach (NetworkInterface netInterface in NetworkInterface.GetAllNetworkInterfaces())
            {
                // only wi-fi and ethernet
                if (netInterface.NetworkInterfaceType != NetworkInterfaceType.Wireless80211 &&
                    netInterface.NetworkInterfaceType != NetworkInterfaceType.Ethernet)
                    continue;

                // for example: do not enumerate previous wi-fi ips if interface is down
                if (netInterface.OperationalStatus == OperationalStatus.Down)
                    continue;

                IPInterfaceProperties ipProps = netInterface.GetIPProperties();
                foreach (UnicastIPAddressInformation addr in ipProps.UnicastAddresses)
                {
                    // only Ipv4
                    if (addr.Address.AddressFamily != System.Net.Sockets.AddressFamily.InterNetwork)
                        continue;

                    if (!string.IsNullOrEmpty(ip_address_preferred) && !string.IsNullOrEmpty(subnet))
                    {
                        if (CheckWhetherInSameNetwork(addr.Address.ToString(), subnet, ip_address_preferred))
                            local_ip_addresses.Add(addr.Address);
                    }
                    else
                        local_ip_addresses.Add(addr.Address);
                }
            }

            return local_ip_addresses;
        }

        // Alternative version, the first fails more?
        List<IPAddress> GetLocalIPAddressesInTheSameNetwork(string ip_address_preferred = null, string subnet = null)
        {
            // special case, loopback
            if (ip_address_preferred == "127.0.0.1")
                return new List<IPAddress>() { IPAddress.Parse("127.0.0.1") };

            var local_ip_addresses = new List<IPAddress>();
            var hostname = Dns.GetHostName();
            var host = Dns.GetHostEntry(hostname);
            foreach (var ip in host.AddressList)
            {
                if (ip.AddressFamily == AddressFamily.InterNetwork)
                {
                    if (!string.IsNullOrEmpty(ip_address_preferred) && !string.IsNullOrEmpty(subnet))
                    {
                        if (CheckWhetherInSameNetwork(ip.ToString(), subnet, ip_address_preferred))
                            local_ip_addresses.Add(ip);
                    }
                    else
                        local_ip_addresses.Add(ip);
                }
            }

            return local_ip_addresses;
        }

        bool CheckWhetherInSameNetwork(string firstIP, string subNet, string secondIP)
        {
            uint subnetmaskInInt = ConvertIPToUint(subNet);
            uint firstIPInInt = ConvertIPToUint(firstIP);
            uint secondIPInInt = ConvertIPToUint(secondIP);
            uint networkPortionofFirstIP = firstIPInInt & subnetmaskInInt;
            uint networkPortionofSecondIP = secondIPInInt & subnetmaskInInt;
            if (networkPortionofFirstIP == networkPortionofSecondIP)
                return true;
            else
                return false;
        }

        uint ConvertIPToUint(string ipAddress)
        {
            System.Net.IPAddress iPAddress = System.Net.IPAddress.Parse(ipAddress);
            byte[] byteIP = iPAddress.GetAddressBytes();
            uint ipInUint = (uint)byteIP[3] << 24;
            ipInUint += (uint)byteIP[2] << 16;
            ipInUint += (uint)byteIP[1] << 8;
            ipInUint += (uint)byteIP[0];
            return ipInUint;
        }

    #endregion
    }

#endif

#if SHAREDOBJECTMAP_SERVER

	public interface ISharedObjectMap_Server
    {
        GuidObject ModelAsGuidObject { get; set; }
        byte[] ModelAsCompressedBytes { get; }
    }

    public class SharedObjectMap_Server<model, api, apimethod> : ISharedObjectMap_Server
        where model : GuidObject
        where api : SharedObjectMap_Server_API
    {
        // This will be the shared object
        private model _Model;

        public APIWrapper APIWrapper { get; private set; }
        public APIServer_RPC ServerRPC { get; private set; }
        //public APIServer_OSC ServerOSC { get; private set; }  // excluded in new KCP implementation
        //private APIServer_HTTP server_http;

        public Action<bool, int>            onRPCServerStarted;   // called when RPC is started with result and port
        public Action<string>               onLog = Logger.DefaultLog.WriteLine;
        public Action<string>               onLogError = Logger.DefaultLog.WriteLine;
        public Action<string, Exception>    onLogException = Logger.DefaultLog.logException;

        public Action<int, bool>            onClientConnectionChanged;  // both for connection and disconnection

		public Action<Shared.KcpMessage>    onCustomMessageReceived; // higher level intercept custom messages (not RPC) (brother of the same event in Tabula.RPC.Server)

		// incoming file transfer, return false to deny it
		public Func<Shared.FileTransfer, bool> OnFileTransferRequest
        {
            get => ServerRPC.Server.OnFileTransferRequest; 

            set
            {
                ServerRPC.Server.OnFileTransferRequest = value;

			}

        }

		// result and outcome of an incoming file transfer
		public Action<Shared.FileTransfer, bool> OnFileTransferEnded
        {
            get => ServerRPC.Server.OnFileTransferEnded;

            set
			{
                ServerRPC.Server.OnFileTransferEnded = value;
			}
		}

		// Special options
		public bool SendUpdatesInTask = false;  // will send updates in a separate task (useful for Unity and other thread-locked apps)

        // the connected clients, indexed by (ip address,updates_port)
        public HashSet<ClientInfo> ConnectedClients { get; private set; } = new HashSet<ClientInfo>();

        // persistent methods
        public string PersistentMethodFile = null;

        // Custom Ports
        public int CustomRPCPort = -1;
        //public int CustomOSCPort = -1;

        [Flags]
        public enum LogLevel { None=0, OSC, }

        public SharedObjectMap_Server<model, api, apimethod> Instance { get; private set; }       

        public SharedObjectMap_Server(string _persistent_method_file = null)
        {
            Instance = this;

            // The API must derive from this class as the static server reference will be used for standard methods
            SharedObjectMap_Server_API.Server = this;

            PersistentMethodFile = _persistent_method_file;

            // API and contract generation

            // at runtime, check if _api_methods is defined and extract the dictionary to pass persistent ids to APIWrapper for coherence
            SerializedDictionary<string, int> _persistent_method_dictionary = null;
			Type enumType = Shared.GetTypeAllAssemblies("Tabula.PMCore.PMCore_Client+_api_methods");
			if (enumType != null && enumType.IsEnum)
			{
				SerializedDictionary<string, int> _get_enum_dictionary(Type enumType)
				{
					SerializedDictionary<string, int> result = new SerializedDictionary<string, int>();
					var values = Enum.GetValues(enumType);
					foreach (var value in values)
					{
						result.Add(value.ToString(), (int)value);
					}
					return result;
				}

				// Get dictionary of enum names and values
				_persistent_method_dictionary = _get_enum_dictionary(enumType);
			}

			APIWrapper = new APIWrapper(typeof(api), typeof(apimethod), persistent_method_dictionary: _persistent_method_dictionary);

            //OSC.NET.OSCMessage.OnObjectSerialize += (o) => JsonConvert.SerializeObject(o);
            //OSC.NET.OSCMessage.OnObjectDeserialize += (s) => JsonConvert.DeserializeObject(s);
        }

        // Starting the server, the Model must be set
        public bool Start(int base_port=0)
        {
            bool server_started = true;

            // RPC Server wired to the API wrapper
            ServerRPC = new APIServer_RPC(CustomRPCPort!=-1 ? CustomRPCPort : ClientServer_Constants.RPCServerPort + base_port, APIWrapper);
            
            // OSC Server wired to the API wrapper
            //ServerOSC = new APIServer_OSC(CustomOSCPort != -1 ? CustomOSCPort : ClientServer_Constants.OSCServerPort + base_port, APIWrapper);

            // Wire the started event
            if (onRPCServerStarted != null)
                ServerRPC.onServerStarted += onRPCServerStarted;

            // Logging

            if (onLog != null)
            {
                ServerRPC.onLog += onLog;
                //ServerOSC.onLog += onLog;
            }
            else
            {
                ServerRPC.onLog += Logger.DefaultLog.WriteLine;
                //ServerOSC.onLog += Logger.DefaultLog.WriteLine;
            }

            if (onLogException != null)
            {
                ServerRPC.onLogException += onLogException;
                //ServerOSC.onLogException += onLogException;
            }
            else
            {
                ServerRPC.onLogException += Logger.DefaultLog.logException;
                //ServerOSC.onLogException += Logger.DefaultLog.logException;
            }

            // HTTP Server wired to the API wrapper
            /*
            server_http = new APIServer_HTTP(ClientServer_Constants.HTTPServerPort + base_port, ".", "api/", api_wrapper);
            server_http.onLog += Logger.DefaultLog.WriteLine;
            server_http.onLogException += Logger.DefaultLog.logException;
            */

            if (ServerRPC.Start())
                onLog?.Invoke($"RPC Server started on port {ServerRPC.Port}");
            else
            {
                onLogError?.Invoke($"ERROR: Cannot start RPC Server on port {ServerRPC.Port}");
                server_started = false;
            }

            /*
            if (ServerOSC.Start())
                onLog?.Invoke($"OSC Server started on port {ServerOSC.Port}");
            else
            {
                onLogError?.Invoke($"ERROR: Cannot start OSC Server on port {ServerOSC.Port}");
                server_started = false;
            }
            */

            /*
            server_http.Start();
            Logger.DefaultLog.WriteLine($"HTTP Server started on port {Defaults.HTTPServerPort}");
            */

            // Handle higher level messages (server updates etc.)
            ServerRPC.Server.OnCustomKcpMessageReceived += _onCustomMessageReceived;

            // Client disconnection will trigger cleanup of connected clients for updates
            ServerRPC.Server.OnClientConnected += OnClientConnected;
            ServerRPC.Server.OnClientDisconnected += OnClientDisconnected;


			// Prepare setup for direct updates, if requested from client
			SharedObjectMap.OnUpdateAdded += SendUpdatesToClients;

            return server_started;
        }

        public void Stop()
        {
			ServerRPC.Server.OnCustomKcpMessageReceived -= _onCustomMessageReceived;
			ServerRPC.Server.OnClientDisconnected -= OnClientDisconnected;

			ServerRPC?.Stop();
            //ServerOSC?.Stop();
        }

        public model Model
        { 
            get => _Model;

            set
            {
                // Clears the model if already set
                if (_Model != null)
                {
                    SharedObjectMap.ClearModel();
                }

                _Model = value;
                if (_Model != null)
                    SharedObjectMap.InitializeModelAsServer(_Model);    

                // TODO: extra notify to clients?
            }
        }

        // Implements the standard interface
        public GuidObject ModelAsGuidObject
        {
            get => Model;

            set => Model = (model) value;            
        }

        public byte[] ModelAsCompressedBytes
		{
            get
			{
                string json = JsonConvert.SerializeObject(Model);
                return _compressString(json);
            }
		}

        private byte[] _compressString(string text)
        {
            byte[] buffer = Encoding.UTF8.GetBytes(text);
            var memoryStream = new MemoryStream();
            using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Compress, true))
            {
                gZipStream.Write(buffer, 0, buffer.Length);
            }

            memoryStream.Position = 0;

            var compressedData = new byte[memoryStream.Length];
            memoryStream.Read(compressedData, 0, compressedData.Length);

            var gZipBuffer = new byte[compressedData.Length + 4];
            Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
            Buffer.BlockCopy(BitConverter.GetBytes(buffer.Length), 0, gZipBuffer, 0, 4);
            //return Convert.ToBase64String(gZipBuffer);
            return gZipBuffer;
        }

        // KCP_PORTING
        public bool TryGetClientInfo(int connection_id, out ClientInfo ci)
        {
            lock (ConnectedClients)
            {
                var existing_client = (from c in ConnectedClients where c.connection_id == connection_id select c).FirstOrDefault();
                ci = existing_client;
                return ci != null;
            }
        }

        private void _onCustomMessageReceived(Shared.KcpMessage msg)
        {
            switch (msg.header.code)
            {
                case Shared.KCPMSG_RCV_UPDATES_ENABLE:
                    ServerToClientSetupToggle(msg.header.connection_id, true, true);
                    break;

				case Shared.KCPMSG_RCV_UPDATES_DISABLE:
					ServerToClientSetupToggle(msg.header.connection_id, false, false);
					break;

				case Shared.KCPMSG_RCV_COMMANDS_ENABLE:
					ServerToClientSetupToggle(msg.header.connection_id, false, true);
					break;

				case Shared.KCPMSG_RCV_COMMANDS_DISABLE:
					ServerToClientSetupToggle(msg.header.connection_id, false, false);
					break;

                case Shared.KCPMSG_GUID_FASTUPDATE:
                    // NEW: receiving fast updates to model wihtout notify!
                    var updates = msg.DecodePayloadAsJson<List<Tabula.RPC.Shared.GuidObjectFastUpdate>>();

                    // not in unity thread, just updating values and setting __is_dirty, no notifications
                    if (updates != null)
                        foreach (var u in updates)
                            SharedObjectMap.UpdateFieldDirect(null, u.guid, u.field, u.value);

					break;

                default:
                    onCustomMessageReceived?.Invoke(msg);
                    break;
			}
        }

        private void OnClientConnected(int connectionId, IPEndPoint remote_ip)
        {
            ClientInfo client_info = null;

            lock (ConnectedClients)
            {
                client_info = (from c in ConnectedClients where c.connection_id == connectionId && c.remote_endpoint.Equals(remote_ip) select c).FirstOrDefault();
            }

            if (client_info != null)
            {
				// exaclty the same client !
				onLog?.Invoke($"RPC_UpdateServer: client re-connected connection_id={client_info.connection_id}");
                return;
			}

			// Every client connection is kept, indendently from server updates or commands
			client_info = new ClientInfo()
			{
				connection_id = connectionId,   // this is also the client id
				remote_endpoint = remote_ip
			};

			lock (ConnectedClients)
			{
				ConnectedClients.Add(client_info);
			}

			onLog?.Invoke($"RPC_UpdateServer: client connected connection_id={client_info.connection_id}");

			onClientConnectionChanged?.Invoke(connectionId, true);
		}

		private void OnClientDisconnected(int connectionId, IPEndPoint remote_ip)
        {
			onLog?.Invoke($"RPC_UpdateServer: client disconnected connection_id={connectionId}");

            ClientInfo connected_client = null;

            // disconnect the client from receiving updates, not necessarily each client has updates
            lock (ConnectedClients)
            {
                connected_client = (from c in ConnectedClients where c.connection_id == connectionId select c).FirstOrDefault();
            }

			if (connected_client != null)
			{
				//onLog?.Invoke($"RPC_UpdateServer: client connection_id={connected_client.connection_id} disconnected");

				lock (ConnectedClients)
				{
					ConnectedClients.Remove(connected_client);
				}
			}

			onClientConnectionChanged?.Invoke(connectionId, false);
		}

       
        // Sets up the update server for a connection, on the client side that client will never be pooled
        private void ServerToClientSetupToggle(int connectionId, bool receive_updates, bool receive_commands)
        {
            // NOTE: ClientInfo must already exists
            if (TryGetClientInfo(connectionId, out ClientInfo client_info))
            {
				client_info.receive_updates = receive_updates;
				client_info.receive_commands = receive_commands;
			}
            else
            {
				onLogError?.Invoke($"RPC_UpdateServer: cannot setup updates for unknown client connection_id={client_info.connection_id}");
			}
		}

        // As usual re-create the client to have single socket transfers
        // NOTE: the UpdateServer is a very slim, protocol-less, use of the API, so a single OnProcessCall handler is created
        // client-side to receive specific calls (ex: "updates") like below
        // TODO: handle client keepalive to disconnect them

        // new implementation, does not rely on ServerUpdates, just sends them
        // does not implement selective updates for now
        // former name: send_updates2
        private void SendUpdatesToClients(List<GuidUpdate> updates)
        {
            if (updates.Count == 0 || ConnectedClients.Count==0)
                return;

            // KCP_PORTING: added to avoid concurrent updates
            lock (this)
            {
                // Sends updates to ALL connected clients that choose to receive, which can have filters
                ServerUpdateResult server_update = new ServerUpdateResult()
                {
                    UpdateSequence = 0L // FIXME: still useful?
                };

                ClientInfo[] _connected_clients;

                lock (ConnectedClients)
                {
                    _connected_clients = ConnectedClients.ToArray();
                }

                foreach (var client in _connected_clients)
                {
                    if (!client.receive_updates)    // only Editors should receive updates
						continue;

                    server_update.Updates = updates.ToArray();

                    var update_call = Tabula.RPC.RPCCall.Create("updates", false, server_update);

                    //KCP_PORTING: send cannot block, receiver implements an async queue
                    Action send_action = ()
                        => Shared.SendJsonObject_KCPServer(
                            ServerRPC.Server.NativeServer,
                            update_call,
                            client.connection_id,
                            KcpChannel.Reliable,
                            message_code: Shared.KCPMSG_SRV_UPDATE,
                            message_id: 0);


                    if (SendUpdatesInTask)
                    {
                        Task.Run(send_action);
                    }
                    else
                    {
                        send_action.Invoke();
                    }
                }
                
            }
        }
		
        #region Client Commands

		// Sends a generic command to all connected client
		public async Task<List<bool>> BroadcastClientCommand(string command, params object[] args)
		{
            List<bool> results = new List<bool>();
            ClientInfo[] _connected_clients;
            lock (ConnectedClients)
            {
                _connected_clients = ConnectedClients.ToArray();
            }

			foreach (var c in _connected_clients)
				if (c.receive_commands)
					results.Add(await SendClientCommand(c, command, args));

			return results;
		}

        // Sends a generic command to a specific connected client
        // TODO: return?
		public async Task<bool> SendClientCommand(ClientInfo client_info, string command, params object[] args)
        {
            var command_call = Tabula.RPC.RPCCall.Create(command, false, args);

			Shared.SendJsonObject_KCPServer(
							ServerRPC.Server.NativeServer,
			                command_call,
							client_info.connection_id,
							KcpChannel.Reliable,
							message_code: Shared.KCPMSG_SRV_UPDATE,
							message_id: 0);

			return true;
        }

		#endregion

		// TODO: should send ONLY to the editor client, if connected
		#region Sync GuidObjects Direct Server -> Client

        // higher level
		public void SendFastUpdates(params (GuidObject guidobject, string field_name, object old_value, object new_value)[] updates)
		{
			var list = new Shared.GuidObjectFastUpdateList();   // standard options

			foreach (var u in updates)
				list.Add(u.guidobject, u.field_name, u.old_value, u.new_value);

			SyncGuidObjectsDirectWithoutNotify(list.Updates);
		}

        // lower level
		public void SyncGuidObjectsDirectWithoutNotify<T>(long guid, string field, object value)
			=> SyncGuidObjectsDirectWithoutNotify(new Shared.GuidObjectFastUpdate() { guid = guid, field = field, value = value });

		public void SyncGuidObjectsDirectWithoutNotify(Shared.GuidObjectFastUpdate update)
			=> SyncGuidObjectsDirectWithoutNotify(new List<Shared.GuidObjectFastUpdate>() { update });

		public void SyncGuidObjectsDirectWithoutNotify(List<Shared.GuidObjectFastUpdate> updates, bool reliable = true)
		{
			if (updates.Count == 0)
				return;

			ClientInfo[] _connected_clients;

			lock (ConnectedClients)
			{
				_connected_clients = ConnectedClients.ToArray();
			}

            foreach (var client in _connected_clients)
            {
                if (!client.receive_updates)    // only Editors should receive updates
                    continue;

                try
                {
                    Shared.SendJsonObject_KCPServer(
                        ServerRPC.Server.NativeServer, 
                        updates,
                        client.connection_id,
                        reliable ? KcpChannel.Reliable : KcpChannel.Unreliable, 
                        Shared.KCPMSG_GUID_FASTUPDATE, 0);
                }
                catch (Exception ex)
                {
                    Logger.DefaultLog.logException("SyncGuidObjectsDirectWithoutNotify", ex);
                }
            }
		}
        
		#endregion

		#region Contracts

		public class ContractOptions
        {
            public string NameSpace;
            public string ClientClassName;
            public string OutputFolder;         // used for everyone

            public string OutputFolder_RPC = null;
            public string OutputFolder_OSC = null;
            public string OutputFolder_Wrapper = null;
            public string OutputFolder_View = null;

            // if defined will  be used directly
            public APIServer_RPC.ContractOptions RPCContractOptions;
            public APIServer_OSC.ContractOptions OSCContractOptions;
        }

        public void GenerateContracts(string name_space, string client_class, string output_folder)
            => GenerateContracts(new ContractOptions() 
                { 
                    NameSpace = name_space,
                    ClientClassName = client_class,
                    OutputFolder = output_folder
                });
                
        // A single call to generate all cntracts rpc/osc, model wrappers and views
        public void GenerateContracts(ContractOptions options)
        {
            // Load persistent method ids and recreate APIWrapper
            // try loading persistent methd dictionary
            SerializedDictionary<string, int> _persistent_method_dictionary = null;

			if (!string.IsNullOrEmpty(PersistentMethodFile) && File.Exists(PersistentMethodFile))
            {
                try
                {
					_persistent_method_dictionary = JsonConvert.DeserializeObject<SerializedDictionary<string, int>>(File.ReadAllText(PersistentMethodFile));
                }
                catch{}
            }

			APIWrapper = new APIWrapper(typeof(api), typeof(apimethod), persistent_method_dictionary: _persistent_method_dictionary);

			// Contract generation is never at runtime
			if (options.RPCContractOptions != null)
                APIServer_RPC.GenerateContract(options.RPCContractOptions);
            else if (options.OutputFolder_RPC != null)
				APIServer_RPC.GenerateContract(new APIServer_RPC.ContractOptions()
                {
                    ApiWrapper = APIWrapper,
                    NameSpace = options.NameSpace,
                    GenerateCallEnum = true,    // added as default, to test
                    ClientClassName = options.ClientClassName,
                    ClientInterfaces = new List<Type>() { typeof(Tabula.SharedObjectMap.ISharedObjectMap_ManagedClient) },
                    OutputFile = Path.Combine(options.OutputFolder_RPC ?? options.OutputFolder, "api_generated_rpc.cs")
                });

            if (options.OSCContractOptions != null)
                APIServer_OSC.GenerateContract(options.OSCContractOptions);
            else if (options.OutputFolder_OSC != null)
                APIServer_OSC.GenerateContract(new APIServer_OSC.ContractOptions()
                {
                    ApiWrapper = APIWrapper,
                    NameSpace = options.NameSpace,
                    ClientClassName = options.ClientClassName,
                    ClientInterfaces = new List<Type>() { typeof(Tabula.SharedObjectMap.ISharedObjectMap_ManagedClient) },
                    OutputFile = Path.Combine(options.OutputFolder_OSC ?? options.OutputFolder, "api_generated_osc.cs")
                });

            // Views generation
            if (options.OutputFolder_View!=null)
                SharedObjectMap.GenerateViewsFromInstance(typeof(model), options.NameSpace, options.NameSpace, 
                    Path.Combine(options.OutputFolder_View ?? options.OutputFolder, "view_generated.cs"));

            // Wrapper generation (server side with auto-schedule)            
            if (options.OutputFolder_Wrapper!=null)
                SharedObjectMap.GenerateWrappersFromInstance(typeof(model), options.NameSpace,
                    Path.Combine(options.OutputFolder_Wrapper ?? options.OutputFolder, "wrapper_generated.cs"));

			// Serialize the APIWrapper persistent method id
            if (!string.IsNullOrEmpty(PersistentMethodFile))
			    File.WriteAllText(PersistentMethodFile, JsonConvert.SerializeObject(APIWrapper.PersistentMethodDictionary, Formatting.Indented));
		}

		#endregion
	}

	// Inheritable static API that implements the basic communication interface, it is based on  the presence of a static server
	// NOTE: in C# 7.0 cannot define static interfaces

	public class SharedObjectMap_ServerAPIMethod : APIMethodAttribute { }

    public class SharedObjectMap_Server_API
    {
        // Singleton reference to server, set by the server itself (only one server per application!)
        public static ISharedObjectMap_Server Server;
        
        [SharedObjectMap_ServerAPIMethod]
        public static int checkConnection(IPEndPoint remote_ep, int number)
            => number+1;

        [SharedObjectMap_ServerAPIMethod]
        public static GuidObject getModel(IPEndPoint remote_ep) 
            => Server.ModelAsGuidObject;

        [SharedObjectMap_ServerAPIMethod]
        public static byte[] getModelAsCompressedBytes(IPEndPoint remote_ep)
            => Server.ModelAsCompressedBytes;

        [SharedObjectMap_ServerAPIMethod]
        public static void setModel(IPEndPoint remote_ep, GuidObject model) 
            => Server.ModelAsGuidObject = model;

        // Polling to get updates
        [SharedObjectMap_ServerAPIMethod]
        public static ServerUpdateResult GetUpdates(IPEndPoint remote_ep, long client_update_sequence)
            => new ServerUpdateResult();    // Deprecated?
                                            //=> SharedObjectMap.GetUpdates(remote_ep, client_update_sequence);

        // Connects back to the client's update-server to direcly send updates
        /*
        [SharedObjectMap_ServerAPIMethod]
        public static ClientInfo ConnectToUpdateServer(IPEndPoint remote_ep, string address, int port, bool receive_updates, List<string> selective_updates) 
            => Task.Run(() => Server.ConnectToUpdateServer(remote_ep, address, port, receive_updates, selective_updates)).Result;

        // Disconnects from update server, and removes ClientInfo, a real disconnection
        [SharedObjectMap_ServerAPIMethod]
        public static ClientInfo DisconnectFromUpdateServer(IPEndPoint remote_ep, ClientInfo client_info) 
            => Task.Run(() => Server.DisconnectFromUpdateServer(remote_ep, client_info)).Result;
        */

        [SharedObjectMap_ServerAPIMethod(GenerateNoResult = true)]
        public static SingleUpdateResult UpdateField(IPEndPoint remote_ep, GuidUpdate update)
        {
            SingleUpdateResult result = default;
            try
            {
                result = UnityMainThreadDispatcher.Instance().EnqueueAndWait(() => SharedObjectMap.UpdateField(remote_ep, update), Constants.EnqueueAndWaitTimeout);
            }
            catch(Exception ex)
            {
				UnityEngine.Debug.LogException(ex);
				return default;
            }

            return result;
        }


        [SharedObjectMap_ServerAPIMethod(GenerateNoResult = true)]
        public static SingleUpdateResult[] UpdateFieldBatch(IPEndPoint remote_ep, List<GuidUpdate> updates, bool get_results = false)
        {
			SingleUpdateResult[] result = default;
            try
            {
				result = UnityMainThreadDispatcher.Instance().EnqueueAndWait(() => SharedObjectMap.UpdateFieldBatch(remote_ep, updates, get_results), Constants.EnqueueAndWaitTimeout);
			}
			catch (Exception ex)
			{
				UnityEngine.Debug.LogException(ex);
				return default;
			}

			return result;
		}
        

        // direct sync (OSC)

        [SharedObjectMap_ServerAPIMethod]
        public static void UpdateFieldDirectInt(IPEndPoint remote_ep, long guid, string fieldname, int value, int index)
            => SharedObjectMap.UpdateFieldDirect(remote_ep, guid, fieldname, value, index);

        [SharedObjectMap_ServerAPIMethod]
        public static void UpdateFieldDirectFloat(IPEndPoint remote_ep, long guid, string fieldname, float value, int index)
            => SharedObjectMap.UpdateFieldDirect(remote_ep, guid, fieldname, value, index);

        [SharedObjectMap_ServerAPIMethod]
        public static void UpdateFieldDirectDouble(IPEndPoint remote_ep, long guid, string fieldname, double value, int index)        
            => SharedObjectMap.UpdateFieldDirect(remote_ep, guid, fieldname, value, index);        
    }

#endif


}
