using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Rendering.SceneGraph;
using Avalonia.Skia;
using Avalonia.Threading;
using ReactiveUI;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Tabula;
using Tabula.SKRenderGraph;

// Generic AXAML containing a drawing operation based on skiagraph

namespace Tabula.PWGClient
{
    // NOTE: in XAML background must be set to Transparent for UI events to function
    public partial class SKSceneUserControl : UserControl
    {
        public  SKScene                         Scene;

		protected Point                         NativeMousePosition = new Point(-1, -1);
        protected PointerPointProperties        MouseProperties = PointerPointProperties.None;
        protected bool                          KeyboardPanPressed => MainView.KeyboardPanPressed;    // TODO: softer dependency?
        public float                            DeltaScale = 1;

        public bool                             UpdateScene = true;
        public bool                             RenderScene = true;

        public long                             RenderedFrameCount = 0;

        public SKSceneUserControl()
        {
            InitializeComponent();
        }

        // Implement overrides depending on visual tree
        public virtual bool IsSceneVisible => true;

        public virtual void OnBecameVisible() { RenderedFrameCount = 0; }

        public virtual void OnBecameInvisible() { }

		#region INotiifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
        {
            ((IReactiveObject)this).RaisePropertyChanged(args);
        }

        protected void RaisePropertyChanged(string name)
        {
            ((IReactiveObject)this).RaisePropertyChanged(name);
        }

		#endregion

		private void InitializeComponent()
        { 
            AvaloniaXamlLoader.Load(this);

            PointerEntered += (s, e) => NativeMousePosition = e.GetPosition(this);
            PointerMoved += (s, e) => NativeMousePosition = e.GetPosition(this);
            PointerExited += (s, e) => NativeMousePosition = new Point(-1, -1); // todo: buttons? (-1,-1) is the "null" position

            PointerPressed += (s, e) =>
            {
                MouseProperties = e.GetCurrentPoint(this).Properties;                
                this.Focus();
            };

            PointerReleased += (s, e) => MouseProperties = e.GetCurrentPoint(this).Properties;
           
            // NOTE: key events don't get fired in the UserControl, so let's route from the MainWindow
            /*
            KeyDown += (s, e) => KeyboardPanPressed = (e.Key == Key.LeftCtrl || e.Key == Key.RightCtrl);
            KeyUp += (s, e) => KeyboardPanPressed = (e.Key == Key.LeftCtrl || e.Key == Key.RightCtrl);
            */
            PointerWheelChanged += (s, e) =>
            {
                if (e.Delta.Y > 0)
                    DeltaScale = 1.05f;

                if (e.Delta.Y < 0)
                    DeltaScale = 0.95f;

                this.Focus();
            };

            Scene = new SKScene();
            Scene.OnGetMouseState += () =>
              {
				  Scene.NativeMousePosition = new SKPoint((float)NativeMousePosition.X, (float)NativeMousePosition.Y);

                  // Debug.WriteLine($"MousePosition: {MousePosition.X} {MousePosition.Y} CanvasSize: {Scene.CanvasSize.Width} {Scene.CanvasSize.Height}");

                  Scene.MousePressed[SKScene.MouseLeftButton] = MouseProperties.IsLeftButtonPressed;
                  Scene.MousePressed[SKScene.MouseMiddleButton] = MouseProperties.IsMiddleButtonPressed;
                  Scene.MousePressed[SKScene.MouseRightButton] = MouseProperties.IsRightButtonPressed;

				  Scene.KeyboardPanPressed = KeyboardPanPressed || MainWindowUI.Instance.PanToggle;

                  Scene.DeltaScale = DeltaScale;
                  if (Scene.DeltaScale!=1 && !Scene.IsMouseInScene)
				  {
                      // Special case, let's pretend the mouse in the center of the canvas
                      Scene.NativeMousePosition.X = Scene.CanvasSize.Width / 2;
                      Scene.NativeMousePosition.Y = Scene.CanvasSize.Height / 2;
                  }
                  
                  DeltaScale = 1;
              };

            // Sub-classes configure the scene
            Dispatcher.UIThread.InvokeAsync(async () => await ConfigureScene());
        }
        
        // FIrst frame is rendered and canvas available, just called once
        public virtual void OnCanvasAvailable()
        {}

        public virtual Task ConfigureScene()
        {
            return Task.CompletedTask;
        }

        // Routes a key event to the selected object, or general handlers
        public virtual void HandleKey(bool is_down, KeyEventArgs key_args)
        {
            // Scaling with CTRL + up/down
            if (is_down && key_args.KeyModifiers.HasFlag(KeyModifiers.Control))
			{
                if (key_args.Key == Key.Up)
                    DeltaScale = 1.05f;
                else if (key_args.Key == Key.Down)
                    DeltaScale = 0.95f;
            }

            Scene.HandleKey(is_down, key_args);
        }

        public void Refresh()
            => Scene?.Refresh();

        class SkiaPageDrawOperation : ICustomDrawOperation
        {           
            private SKSceneUserControl control;
            private SKScene scene;

            public Func<long> onFrameRendered;

            public SkiaPageDrawOperation(SKSceneUserControl _control, Rect bounds, SKScene _scene)
            {
                control = _control;
                Bounds = bounds;
                scene = _scene;
            }

            public void Dispose()
            {}

            public Rect Bounds { get; }
            public bool HitTest(Point p) => false;
            public bool Equals(ICustomDrawOperation other) => false;

            public void Render(ImmediateDrawingContext context)
            {
                try
                {
                    // https://github.com/AvaloniaUI/Avalonia/issues/8615
                    var leaseFeature = context.TryGetFeature<ISkiaSharpApiLeaseFeature>();
                    if (leaseFeature == null)
                        return; // back screen ?
                    using var lease = leaseFeature.Lease();
                    var canvas = lease.SkCanvas;

                    // var canvas = (context as ISkiaDrawingContextImpl)?.SkCanvas;

                    if (canvas == null)
                        return;

                    if (!control.RenderScene)
                        return;

                    canvas.GetDeviceClipBounds(out SKRectI bounds);

                    //scene.CreateSurfaceIfNeeded((int) Bounds.Size.Width, (int) Bounds.Size.Height);
                    bool surface_created = scene.CreateSurfaceIfNeeded(bounds.Width, bounds.Height);

                    // NOTE: an exception here will abort the render we should show an error notice instead
                    bool is_error = false;
                    try
                    {
                        if (control.UpdateScene)
                            scene.Update();

                        if (scene.IsDrawNeeded || surface_created)
                        {
                            // draw internally
                            scene.Draw();

                            if (control.RenderedFrameCount == 0)
                                control.OnCanvasAvailable();

                            control.RenderedFrameCount++;
                            //frame = onFrameRendered?.Invoke();
                        }
                    }
                    catch(Exception ex)
                    {
                        // TODO: just once to avoid spikes?
                        App.Local.logException("SKSceneUserControl.Render() during scene Update() and Draw()", ex);
                        is_error = true;
                    }

                    // blit in any case
                    canvas.DrawSurface(scene.Surface, new SKPoint(0, 0), new SKPaint() { IsAntialias = true, FilterQuality = SKFilterQuality.Low });

                    if (is_error)
                        canvas.DrawText("ERROR: In rendered objects", 20, 20, new SKPaint() { TextSize=12, Color = SKColors.Red });
                }
                catch(Exception ex)
                {
					// Rendering problem ?
					App.Local.logException("SKSceneUserControl.Render() problem", ex);
				}
            }            
        }

        private Rect last_rect = default;

        private long frame = 0;
        public override void Render(DrawingContext context)
        {
            var current_rect = new Rect(0, 0, Bounds.Width, Bounds.Height);
            if (!current_rect.Equals(last_rect))
            {
                Scene.Refresh();
                last_rect = current_rect;
            }

            var op = new SkiaPageDrawOperation(this, current_rect, Scene);
            //op.onFrameRendered = () => frame++;

            context.Custom(op);
            Dispatcher.UIThread.InvokeAsync(InvalidateVisual, DispatcherPriority.Background);
        }
    }
}
