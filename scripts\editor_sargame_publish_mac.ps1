c:
cd "C:\G\Codice\TABULA.PMCORE\PWGClient_xplat"

# Main dotnet compile, as a single file (only some DYLIBs are external)
dotnet publish PWGClient.Desktop -c Release /p:PublishProfile=PWGClient.Desktop\Properties\PublishProfiles\MacOS.pubxml

# Extract the "FileVersion" string and save it to version.txt
versioninfo -j PWGClient.Desktop\bin\Release\net7.0\osx-x64\S-ARGAME_Editor.dll >PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\version.json
$jsonData = Get-Content -Raw -Path "PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\version.json" | ConvertFrom-Json
$jsonData[0].FileVersion | Out-File -FilePath "PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\version.txt" -Encoding UTF8

# Build the Info.plist
$templateFilePath = "Info.plist.template"
$outputFilePath = "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest\Info.plist"
$versionNumber = Get-Content -Path "PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\version.txt" -Raw
$versionNumber = $versionNumber.Trim()
$templateContent = Get-Content -Path $templateFilePath -Raw
$resultContent = $templateContent -replace "##VERSION##", $versionNumber # Replace "##VERSION##" with the version number
$resultContent | Set-Content -Path $outputFilePath # Write the modified content to the output file

# Protect (no necrobit would be OK on Mac, but randomly somehting else breaks notarization, AntiDebug and Obfuscation is enabled worked once...)
# SUSPENDED for now
# # dotNET_Reactor.Console.exe -project "Reactor_S-ARGAME_Editor_dotnet_mac.nrproj"

# (Not Protect) Just copy the compiled executable do deploy
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\S-ARGAME_Editor deploy\osx-x64\ /Y

# Copy important DYLIBs not in the single file
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\*.dylib deploy\osx-x64\ /Y

# Copy version.txt
xcopy PWGClient.Desktop\bin\Release\net7.0\publish\osx-x64\version.txt deploy\osx-x64\ /Y

# Remove unwanted files

# # del deploy\S-ARGAME_Editor.dll

# Copy the deploy (executable and dylibs) also to OneDrive Mac build folder (for later Mac packaging)

xcopy deploy\osx-x64\S-ARGAME_Editor "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest" /Y
xcopy deploy\osx-x64\*.dylib "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest" /Y
xcopy deploy\osx-x64\version.txt "D:\OneDrive\TABULA_Builds\S-ARGAME_Editor_MacOS_latest" /Y

# Copy the changelogs to _DEPLOY\s-argame\editor

xcopy installer\changelogs "D:\OneDrive\_DEPLOY\s-argame\macos_x64\editor\changelogs\" /Y

# RSync to KeyCDN using WSL and private key (PLEASE DO MANUALLY ON THE WHOLE FOLDER)
d:




