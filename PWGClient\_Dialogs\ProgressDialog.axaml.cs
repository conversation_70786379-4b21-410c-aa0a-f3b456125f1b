using Avalonia.Controls;
using Avalonia.Controls.Platform;
using Avalonia.Interactivity;
using Avalonia.Threading;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using PropertyChanged;
using System;
using System.Threading;
using System.Threading.Tasks;
using Tabula.PWG.SARGAME;

namespace Tabula.PWGClient
{
	// View
	[AddINotifyPropertyChangedInterface]
	public class ProgressDialogView
	{
		public ProgressDialog Dialog;

		public MainWindowUI Main => MainWindowUI.Instance;

		// Optional logo
		public bool		ShowLogo { get; set; } = false;

		public string	Message { get; set; }

		public bool		ShowProgress { get; set; } = true;
		public bool		IsProgressIndeterminate { get; set; } = true;
		public int		ProgressValue { get; set; } = 0;

		// if valorized lifetime is related to a created task
		public Func<CancellationToken,Task> Action { get; set; }

		public CancellationToken CancellationToken { get; set; }

		public string	CancelText { get; set; } = "Cancel";
		public bool		ShowCancelButton { get; set; } = false;
		public Action	OnCancelButtonClicked { get; set; }

		public bool		ShowChoicePanel { get; set; } = false;
		public string	Choice1Text { get; set; } = "";
		public string	Choice2Text { get; set; } = "";
		public string	Choice3Text { get; set; } = "";
		public string	Choice4Text { get; set; } = "";
		public bool		ShowChoice1Button { get; set; } = false;
		public bool		ShowChoice2Button { get; set; } = false;
		public bool		ShowChoice3Button { get; set; } = false;
		public bool		ShowChoice4Button { get; set; } = false;

		public Action OnTaskEnded { get; set; }
		public Action<Exception> OnTaskException { get; set; }
		public Action OnTaskCanceled { get; set; }

		public bool CloseOnTaskFinish { get; set; } = false;

		public void StartTask(CancellationToken cancel_token=default)
		{
			if (Action == null)
				return;

			try
			{
				Task.Run(async () =>
				{
					try
					{
						// NOTE: action should use cts.ThrowIfCancellationRequested() in a loop!
						await Action(cancel_token != default ? cancel_token : default);
					}
					catch (OperationCanceledException ex)
					{
						OnTaskCanceled?.Invoke();
						if (CloseOnTaskFinish)
							Dialog.Close();
						return;
					}
					catch (Exception ex)
					{
						if (OnTaskException != null)
							OnTaskException?.Invoke(ex);
						else
							SARGAME.App.logException("ProgressDialog task 1", ex);

						if (CloseOnTaskFinish)
							Dialog.Close();

						return;
					}


					OnTaskEnded?.Invoke();

					if (CloseOnTaskFinish)
						Dialog.Close();
				});

			}
			catch (Exception ex)
			{
				if (OnTaskException != null)
					OnTaskException?.Invoke(ex);
				else
					SARGAME.App.logException("ProgressDialog task 2", ex);

				if (CloseOnTaskFinish)
					Dialog.Close();

				return;
			}
		}
	}


	[DoNotNotify]
	public partial class ProgressDialog : UserControl
	{
		public ProgressDialogView View;
		public string DialogIdentifier;

		public static bool IsOpen { get; private set; }

		public enum Choice
		{
			None = 0,
			Choice1 = 1,
			Choice2 = 2,
			Choice3 = 3,
			Choice4 = 4
		}

		public ProgressDialog()
		{
			InitializeComponent();
		}

		public static ProgressDialog Show(ProgressDialogView view, string identifier = "dialog")
		{
			var dialog = new ProgressDialog();

			dialog.View = view;
			view.Dialog = dialog;			

			dialog.DialogIdentifier = identifier;

			dialog.Execute();

			_close_existing_dialog(identifier);

			dialog.DataContext = view; // Update DataContext here

			IsOpen = true;

			DialogHostAvalonia.DialogHost.Show(dialog, identifier);

			return dialog;
		}

		public static ProgressDialog ShowMessage(
			string message, 
			string cancel_button_text = "", 
			CancellationToken cancellation_token=default, 
			Action on_cancel_button_click=null,
			bool progress=false,
			bool show_logo=false,
			string identifier = "dialog")
		{
			var v = new ProgressDialogView()
			{
				ShowLogo = show_logo,
				Message = message,
				ShowProgress = progress,
				IsProgressIndeterminate = true,
				ShowCancelButton = !string.IsNullOrEmpty(cancel_button_text),
				CancellationToken = cancellation_token,
				CancelText = cancel_button_text,
				OnCancelButtonClicked = on_cancel_button_click,
			};

			return Show(v, identifier);			
		}

		public static async Task<object?> ShowMessageAsync(string message, string cancel_button_text="OK", bool show_logo=false, string identifier="dialog")
		{
			var v = new ProgressDialogView()
			{
				ShowLogo = show_logo,
				Message = message,
				ShowProgress = false,
				ShowCancelButton = true,
				CancelText = cancel_button_text
			};

			return await ShowAsync(v, identifier: identifier);
		}

		public static async Task<object?> ShowProgressAsync(
			string message, 
			Func<CancellationToken, Task> action,
			string cancel_button_text = "", 
			string identifier = "dialog")
		{
			var v = new ProgressDialogView()
			{
				Message = message,
				IsProgressIndeterminate = true,
				Action = action,
				ShowCancelButton = !string.IsNullOrEmpty(cancel_button_text),
				CancelText = cancel_button_text,
				CloseOnTaskFinish = true
			};

			return await ProgressDialog.ShowAsync(v, identifier: identifier);
		}

		public static async Task<Choice> ShowChoicesAsync(
			string message,
			string[] choice_texts,
			string identifier = "dialog")
		{
			var v = new ProgressDialogView()
			{
				Message = message,
				ShowProgress = false,
				ShowChoicePanel = true,
				ShowCancelButton = false,
				ShowChoice1Button = choice_texts.Length > 0 && !string.IsNullOrEmpty(choice_texts[0]),
				ShowChoice2Button = choice_texts.Length > 1 && !string.IsNullOrEmpty(choice_texts[1]),
				ShowChoice3Button = choice_texts.Length > 2 && !string.IsNullOrEmpty(choice_texts[2]),
				ShowChoice4Button = choice_texts.Length > 3 && !string.IsNullOrEmpty(choice_texts[3]),
				Choice1Text = choice_texts.Length > 0 ? choice_texts[0] : "",
				Choice2Text = choice_texts.Length > 1 ? choice_texts[1] : "",
				Choice3Text = choice_texts.Length > 2 ? choice_texts[2] : "",
				Choice4Text = choice_texts.Length > 3 ? choice_texts[3] : "",
				CloseOnTaskFinish = true
			};

			var ret = await ProgressDialog.ShowAsync(v, identifier: identifier);

			if (ret == null)
				return Choice.None;	// dialog has been canceled (another dialog closed it)
			else
				return (Choice) ret;
		}

		public static async Task<object?> ShowAsync(ProgressDialogView view, string identifier= "dialog")
		{
			var dialog = new ProgressDialog();

			dialog.View = view;
			view.Dialog = dialog;

			dialog.DialogIdentifier = identifier;

			if (view.ShowChoice3Button)
			{
				dialog.stackpanel_choice.Orientation = Avalonia.Layout.Orientation.Vertical;
			}

			dialog.Execute();

			_close_existing_dialog(identifier);

			dialog.DataContext = view; // Update DataContext here

			IsOpen = true;
			var ret = await DialogHostAvalonia.DialogHost.Show(dialog, identifier);
			IsOpen = false;
			return ret;
		}

		private static void _close_existing_dialog(string identifier= "dialog")
		{
			// closing previous dialog if it's opened

			if (DialogHostAvalonia.DialogHost.IsDialogOpen(identifier))
			{
				DialogHostAvalonia.DialogHost.Close(identifier, null);
				IsOpen = false;
			}
		}

		protected override void OnLoaded(RoutedEventArgs e)
		{
			// DataContext = View;

			base.OnLoaded(e);
		}

		protected void Execute()
		{
			if (View.Action != null)
			{
				View.StartTask(View.CancellationToken);
			}
		}

		public void SetProgress(double progress)
		{
			View.IsProgressIndeterminate = false;
			View.ShowProgress = true;
			View.ProgressValue = (int) progress;
		}

		bool _is_closed = false;
		public void Close(object result=null)
		{
			if (_is_closed)
				return;

			_is_closed = true;
			IsOpen = false;

			Dispatcher.UIThread.Invoke(() => DialogHostAvalonia.DialogHost.Close(DialogIdentifier, result));
		}

		void bt_cancel_Click(object sender, RoutedEventArgs args)
		{
			View.OnCancelButtonClicked?.Invoke();
			
			Close();
		}

		void bt_choice_Click(object sender, RoutedEventArgs args)
		{
			var choice_tag = (sender as Button).Tag;

			switch (choice_tag)
			{
				case "1": Close(Choice.Choice1); break;
				case "2": Close(Choice.Choice2); break;
				case "3": Close(Choice.Choice3); break;
				case "4": Close(Choice.Choice4); break;
			}

			Close(Choice.None);			
		}
	}
}

