using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform;
using Avalonia.Rendering.SceneGraph;
using Avalonia.Skia;
using Avalonia.Threading;
using ReactiveUI;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Tabula;
using Tabula.PMCore;
using Tabula.SKRenderGraph;
using Tabula.AvaloniaUtils;
using System.Reflection;
using System.IO;
using Avalonia.VisualTree;

namespace Tabula.PWGClient
{
    // NOTE: Scene will be populated in App.axaml.cs, when model is available

    public partial class ScreenRegionEditor : SKSceneUserControl
    {
        public static ScreenRegionEditor Instance;

        public override bool IsSceneVisible
        {
            get
            {
				var tab = this.FindAncestor<TabItem>();

				if (tab != null)
                    return tab.IsSelected;
                else
                    return false;
            }
        }

        public ScreenRegionEditor()
        {
            Instance = this;
        }
       
        private void InitializeComponent()
        { 
            AvaloniaXamlLoader.Load(this);

            Dispatcher.UIThread.InvokeAsync(async () => await ConfigureScene());
        }       

        public override Task ConfigureScene()
        {
            Scene.AfterDraw += DrawScreenBoundsAndSize;

            return Task.CompletedTask;
        }

        public void DrawScreenBoundsAndSize()
        {
            var paint = new SKPaint()             
                { 
                    Color = new SKColor(255, 255, 255), 
                    IsStroke= true,
                    StrokeWidth = 2,
                    IsAntialias = true             
                };
            
            if (App.IsConnected)
                Scene.Canvas.DrawRect(new SKRect(0, 0, App.Client.Model.Screen.size.width, App.Client.Model.Screen.size.height), paint);
		}
      
    }
}
