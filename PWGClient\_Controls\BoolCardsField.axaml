<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
              xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 mc:Ignorable="d" d:DesignWidth="200" d:DesignHeight="200"
             x:Class="Tabula.PWGClient.BoolCardsField">
	<Grid ColumnDefinitions="Auto,Auto" HorizontalAlignment="Center">
		<Button Grid.Column="0" Margin="0,0,-6,0" Classes.Primary="{Binding IsTrue}" Padding="0" Width="{Binding width}" Height="{Binding height}" ZIndex="{Binding ZTrue}"  ClickMode="Press" Click="bt_true">
			<TextBlock Text="{Binding true_label}" VerticalAlignment="Center" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10" FontWeight="Bold"/>
		</Button>
		<Button Grid.Column="1" Margin="-6,0,0,0"  Classes.Primary="{Binding !IsTrue}" Padding="0" Width="{Binding width}" Height="{Binding height}" ZIndex="{Binding ZFalse}" ClickMode="Press" Click="bt_false">
			<TextBlock Text="{Binding false_label}" VerticalAlignment="Center" HorizontalAlignment="Center" TextAlignment="Center" FontSize="10" FontWeight="Bold"/>
		</Button>
	</Grid>
</UserControl>
