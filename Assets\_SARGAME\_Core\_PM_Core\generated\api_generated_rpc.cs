﻿
// Generated PMCore_Client API inheriting from Tabula.RPC.ManagedClient (include TabulaJsonRPC.cs in project)
// DateTime: giovedì 17 aprile 2025

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tabula;
using Tabula.RPC;

namespace Tabula.PMCore {


#if !TABULARPC_EXCLUDESERIALIZEDCLASSES


#if __MOBILE__
public partial class SerializableAttribute : Attribute { }
#endif

[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public class IPEndPoint
{
	public System.Net.IPAddress	Address	{ get; set; }
	public int	Port	{ get; set; }
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public struct Byte
{
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public class GuidObject
{
	public long	__guid;
	public long	__guid_parent;
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public struct ServerUpdateResult
{
	public long	UpdateSequence;
	public Tabula.SharedObjectMap.GuidUpdate[]	Updates;
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public class GuidUpdate
{
	public long	Guid;
	public long	Seq;
	public Tabula.SharedObjectMap.UpdateType	UpdateType;
	public Tabula.SharedObjectMap.UpdateFlags	Flags;
	public string	FieldName;
	public string	FieldType;
	public object	FieldValue;
	public object	PreviousValue;
	public object	ItemIndexOrKey;
	public bool	IsFromServer;
	public bool	IsDirectSync;
	public string	MethodName;
	public object[]	MethodArguments;
	public string[]	MethodTypes;
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public struct SingleUpdateResult
{
	public Tabula.SharedObjectMap.UpdateResultFlags	Flags;
	public object	ChangedValue;
}


[Serializable]
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public class IPAddress
{
	public long	ScopeId	{ get; set; }
	public long	Address	{ get; set; }
}


[System.Reflection.ObfuscationAttribute(Exclude = true)]
public enum UpdateType
{
	SetField = 1,
	AddItem = 2,
	RemoveItem = 3,
	ClearItems = 4,
	CallMethod = 10
}


[System.Reflection.ObfuscationAttribute(Exclude = true)]
public enum UpdateFlags
{
	None = 0,
	NeedResult = 1,
	DoNotNotify = 2,
	DirectSync = 4,
	NoDirectSync = 8
}


[System.Reflection.ObfuscationAttribute(Exclude = true)]
public enum UpdateResultFlags
{
	None = 0,
	Updated = 1,
	ValueChanged = 2,
	ObjectNotFound = 4,
	FieldNotFound = 8,
	ErrorSettingValue = 16,
	ErrorGeneric = 32,
	InCommit = 512,
	NullGuid = 2014
}




#endif

[System.Reflection.ObfuscationAttribute(Exclude=true)]
public partial class PMCore_Client : ManagedClient, Tabula.SharedObjectMap.ISharedObjectMap_ManagedClient
{
    public PMCore_Client() {}

    public PMCore_Client(string address, int port) : base(address, port) {}



    [System.Reflection.ObfuscationAttribute(Exclude = true)]
    public enum _api_methods {
    None = 0,
	checkConnection = 1,
	CreateEntity = 26,
	CreateStructure = 29,
	DestroyObject = 27,
	GetAuthCodes = 2,
	GetCurrentSceneIndex = 3,
	GetEntitiesToCreate = 28,
	GetIcon = 4,
	GetIconList = 5,
	getModel = 6,
	getModelAsCompressedBytes = 7,
	GetProtocolVersion = 8,
	GetScenes = 9,
	GetStatistics = 22,
	GetUpdates = 10,
	GetWindowHandle = 23,
	LoadScene = 11,
	NetworkPlayerJoin = 12,
	NetworkPlayerLeave = 13,
	Ping = 14,
	Quit = 25,
	SetFocus = 15,
	setModel = 16,
	UpdateField = 17,
	UpdateFieldBatch = 18,
	UpdateFieldDirectDouble = 19,
	UpdateFieldDirectFloat = 20,
	UpdateFieldDirectInt = 21
}
public async Task<int> checkConnection(System.Net.IPEndPoint remote_ep, int number)
{
    var call = RPCCall.Create(1,true, remote_ep,number);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "checkConnection";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(int);
    if (result == null)
        throw new RPCException("Error in RPC Call 'checkConnection'");

    int conv_result = result.ConvertedResult!=null ? (int) result.ConvertedResult : default;

    return conv_result;
}

public async Task<Tabula.PMCore.Entity> CreateEntity(Tabula.PMCore.Entity e, long parent_id = -1)
{
    var call = RPCCall.Create(26,true, e,parent_id);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "CreateEntity";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.PMCore.Entity);
    if (result == null)
        throw new RPCException("Error in RPC Call 'CreateEntity'");

    Tabula.PMCore.Entity conv_result = result.ConvertedResult!=null ? (Tabula.PMCore.Entity) result.ConvertedResult : default;

    return conv_result;
}

public async Task<Tabula.PMCore.Structure> CreateStructure(Tabula.PMCore.Structure s)
{
    var call = RPCCall.Create(29,true, s);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "CreateStructure";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.PMCore.Structure);
    if (result == null)
        throw new RPCException("Error in RPC Call 'CreateStructure'");

    Tabula.PMCore.Structure conv_result = result.ConvertedResult!=null ? (Tabula.PMCore.Structure) result.ConvertedResult : default;

    return conv_result;
}

public async Task<bool> DestroyObject(long id)
{
    var call = RPCCall.Create(27,true, id);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "DestroyObject";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(bool);
    if (result == null)
        throw new RPCException("Error in RPC Call 'DestroyObject'");

    bool conv_result = result.ConvertedResult!=null ? (bool) result.ConvertedResult : default;

    return conv_result;
}

public async Task<List<string>> GetAuthCodes()
{
    var call = RPCCall.Create(2,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetAuthCodes";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(List<string>);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetAuthCodes'");

    List<string> conv_result = result.ConvertedResult!=null ? (List<string>) result.ConvertedResult : default;

    return conv_result;
}

public async Task<int> GetCurrentSceneIndex()
{
    var call = RPCCall.Create(3,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetCurrentSceneIndex";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(int);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetCurrentSceneIndex'");

    int conv_result = result.ConvertedResult!=null ? (int) result.ConvertedResult : default;

    return conv_result;
}

public async Task<List<Tabula.PMCore.EntityCreate>> GetEntitiesToCreate(string categories = null)
{
    var call = RPCCall.Create(28,true, categories);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetEntitiesToCreate";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(List<Tabula.PMCore.EntityCreate>);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetEntitiesToCreate'");

    List<Tabula.PMCore.EntityCreate> conv_result = result.ConvertedResult!=null ? (List<Tabula.PMCore.EntityCreate>) result.ConvertedResult : default;

    return conv_result;
}

public async Task<System.Byte[]> GetIcon(string name)
{
    var call = RPCCall.Create(4,true, name);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetIcon";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(System.Byte[]);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetIcon'");

    System.Byte[] conv_result = result.ConvertedResult!=null ? (System.Byte[]) result.ConvertedResult : default;

    return conv_result;
}

public async Task<string[]> GetIconList()
{
    var call = RPCCall.Create(5,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetIconList";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(string[]);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetIconList'");

    string[] conv_result = result.ConvertedResult!=null ? (string[]) result.ConvertedResult : default;

    return conv_result;
}

public async Task<Tabula.SharedObjectMap.GuidObject> getModel(System.Net.IPEndPoint remote_ep)
{
    var call = RPCCall.Create(6,true, remote_ep);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "getModel";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.SharedObjectMap.GuidObject);
    if (result == null)
        throw new RPCException("Error in RPC Call 'getModel'");

    Tabula.SharedObjectMap.GuidObject conv_result = result.ConvertedResult!=null ? (Tabula.SharedObjectMap.GuidObject) result.ConvertedResult : default;

    return conv_result;
}

public async Task<System.Byte[]> getModelAsCompressedBytes(System.Net.IPEndPoint remote_ep)
{
    var call = RPCCall.Create(7,true, remote_ep);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "getModelAsCompressedBytes";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(System.Byte[]);
    if (result == null)
        throw new RPCException("Error in RPC Call 'getModelAsCompressedBytes'");

    System.Byte[] conv_result = result.ConvertedResult!=null ? (System.Byte[]) result.ConvertedResult : default;

    return conv_result;
}

public async Task<int> GetProtocolVersion()
{
    var call = RPCCall.Create(8,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetProtocolVersion";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(int);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetProtocolVersion'");

    int conv_result = result.ConvertedResult!=null ? (int) result.ConvertedResult : default;

    return conv_result;
}

public async Task<List<string>> GetScenes()
{
    var call = RPCCall.Create(9,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetScenes";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(List<string>);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetScenes'");

    List<string> conv_result = result.ConvertedResult!=null ? (List<string>) result.ConvertedResult : default;

    return conv_result;
}

public async Task<List<Tabula.PMCore.NamedValue>> GetStatistics()
{
    var call = RPCCall.Create(22,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetStatistics";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(List<Tabula.PMCore.NamedValue>);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetStatistics'");

    List<Tabula.PMCore.NamedValue> conv_result = result.ConvertedResult!=null ? (List<Tabula.PMCore.NamedValue>) result.ConvertedResult : default;

    return conv_result;
}

public async Task<Tabula.SharedObjectMap.ServerUpdateResult> GetUpdates(System.Net.IPEndPoint remote_ep, long client_update_sequence)
{
    var call = RPCCall.Create(10,true, remote_ep,client_update_sequence);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetUpdates";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.SharedObjectMap.ServerUpdateResult);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetUpdates'");

    Tabula.SharedObjectMap.ServerUpdateResult conv_result = result.ConvertedResult!=null ? (Tabula.SharedObjectMap.ServerUpdateResult) result.ConvertedResult : default;

    return conv_result;
}

public async Task<string> GetWindowHandle()
{
    var call = RPCCall.Create(23,true);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "GetWindowHandle";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(string);
    if (result == null)
        throw new RPCException("Error in RPC Call 'GetWindowHandle'");

    string conv_result = result.ConvertedResult!=null ? (string) result.ConvertedResult : default;

    return conv_result;
}

public async Task LoadScene(int scene_index)
{
    var call = RPCCall.Create(11,false, scene_index);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "LoadScene";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'LoadScene'");

}

public async Task<Tabula.PMCore.PlayerJoinResult> NetworkPlayerJoin(string player_client_uid)
{
    var call = RPCCall.Create(12,true, player_client_uid);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "NetworkPlayerJoin";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.PMCore.PlayerJoinResult);
    if (result == null)
        throw new RPCException("Error in RPC Call 'NetworkPlayerJoin'");

    Tabula.PMCore.PlayerJoinResult conv_result = result.ConvertedResult!=null ? (Tabula.PMCore.PlayerJoinResult) result.ConvertedResult : default;

    return conv_result;
}

public async Task<Tabula.PMCore.PlayerLeftResult> NetworkPlayerLeave(string player_client_uid)
{
    var call = RPCCall.Create(13,true, player_client_uid);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "NetworkPlayerLeave";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.PMCore.PlayerLeftResult);
    if (result == null)
        throw new RPCException("Error in RPC Call 'NetworkPlayerLeave'");

    Tabula.PMCore.PlayerLeftResult conv_result = result.ConvertedResult!=null ? (Tabula.PMCore.PlayerLeftResult) result.ConvertedResult : default;

    return conv_result;
}

public async Task<int> Ping(int i = 0)
{
    var call = RPCCall.Create(14,true, i);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "Ping";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(int);
    if (result == null)
        throw new RPCException("Error in RPC Call 'Ping'");

    int conv_result = result.ConvertedResult!=null ? (int) result.ConvertedResult : default;

    return conv_result;
}

public async Task Quit(int exit_code)
{
    var call = RPCCall.Create(25,false, exit_code);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "Quit";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'Quit'");

}

public async Task SetFocus()
{
    var call = RPCCall.Create(15,false);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "SetFocus";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'SetFocus'");

}

public async Task setModel(System.Net.IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidObject model)
{
    var call = RPCCall.Create(16,false, remote_ep,model);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "setModel";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'setModel'");

}

public async Task<Tabula.SharedObjectMap.SingleUpdateResult> UpdateField(System.Net.IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidUpdate update)
{
    var call = RPCCall.Create(17,true, remote_ep,update);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateField";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.SharedObjectMap.SingleUpdateResult);
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateField'");

    Tabula.SharedObjectMap.SingleUpdateResult conv_result = result.ConvertedResult!=null ? (Tabula.SharedObjectMap.SingleUpdateResult) result.ConvertedResult : default;

    return conv_result;
}

public async Task UpdateField_NoResult(System.Net.IPEndPoint remote_ep, Tabula.SharedObjectMap.GuidUpdate update)
{
    var call = RPCCall.Create(17,false, remote_ep,update);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateField";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateField NO RESULT'");

}

public async Task<Tabula.SharedObjectMap.SingleUpdateResult[]> UpdateFieldBatch(System.Net.IPEndPoint remote_ep, List<Tabula.SharedObjectMap.GuidUpdate> updates, bool get_results = false)
{
    var call = RPCCall.Create(18,true, remote_ep,updates,get_results);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateFieldBatch";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default(Tabula.SharedObjectMap.SingleUpdateResult[]);
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateFieldBatch'");

    Tabula.SharedObjectMap.SingleUpdateResult[] conv_result = result.ConvertedResult!=null ? (Tabula.SharedObjectMap.SingleUpdateResult[]) result.ConvertedResult : default;

    return conv_result;
}

public async Task UpdateFieldBatch_NoResult(System.Net.IPEndPoint remote_ep, List<Tabula.SharedObjectMap.GuidUpdate> updates, bool get_results = false)
{
    var call = RPCCall.Create(18,false, remote_ep,updates,get_results);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateFieldBatch";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateFieldBatch NO RESULT'");

}

public async Task UpdateFieldDirectDouble(System.Net.IPEndPoint remote_ep, long guid, string fieldname, double value, int index)
{
    var call = RPCCall.Create(19,false, remote_ep,guid,fieldname,value,index);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateFieldDirectDouble";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateFieldDirectDouble'");

}

public async Task UpdateFieldDirectFloat(System.Net.IPEndPoint remote_ep, long guid, string fieldname, float value, int index)
{
    var call = RPCCall.Create(20,false, remote_ep,guid,fieldname,value,index);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateFieldDirectFloat";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateFieldDirectFloat'");

}

public async Task UpdateFieldDirectInt(System.Net.IPEndPoint remote_ep, long guid, string fieldname, int value, int index)
{
    var call = RPCCall.Create(21,false, remote_ep,guid,fieldname,value,index);
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = "UpdateFieldDirectInt";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException("Error in RPC Call 'UpdateFieldDirectInt'");

}


}
}
