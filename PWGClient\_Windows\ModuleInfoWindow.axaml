<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:local="clr-namespace:Tabula.PWGClient"
        xmlns:pmcore="clr-namespace:Tabula.PMCore"
		xmlns:dialogHost="clr-namespace:DialogHost;assembly=DialogHost.Avalonia"
        xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
        xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
		xmlns:av="clr-namespace:TheArtOfDev.HtmlRenderer.Avalonia;assembly=Avalonia.HtmlRenderer"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
		Width="1024"
        x:Class="Tabula.PWGClient.ModuleInfoWindow"
        Title="Module Info">
	<Grid RowDefinitions="Auto,*" Margin="20">

		<Grid Grid.Row="0" RowDefinitions="Auto,1">
			<TextBlock Grid.Row="0" Text="Game Info" Classes="h4" Margin="5,0,0,10" TextAlignment="Left"/>
			<Rectangle Grid.Row="1" Fill="{DynamicResource SukiPrimaryColor}" Margin="5,0,0,0" />
		</Grid>

		<ScrollViewer Grid.Row="1" Margin="5,5,0,0">
			<ScrollViewer.Background>
				<SolidColorBrush Color="Black" Opacity="0.4"/>
			</ScrollViewer.Background>

			<av:HtmlPanel x:Name="html_panel" Margin="0"/>

		</ScrollViewer>

	</Grid>
</Window>
