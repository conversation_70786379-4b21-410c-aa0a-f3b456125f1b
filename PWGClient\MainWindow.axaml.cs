using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using System;

namespace Tabula.PWGClient
{

	// NOTE: DataContext is set by App.OnStartup() after initialization

	[DoNotNotify]
	public partial class MainWindow : Window
	{
		public static MainWindow Instance;

		public MainWindow()
		{
			Instance = this;

			InitializeComponent();

			this.GetObservable(ClientSizeProperty).Subscribe(OnWindowResized);

			Closing += (s,a) =>
			{
				// Closes external windows (NetSparkle)
				if (NetSparkleUpdater.UI.Avalonia.UpdateAvailableWindow.Instance != null)
					NetSparkleUpdater.UI.Avalonia.UpdateAvailableWindow.Instance.Close();
			};
		}

		public void OnWindowResized(Size size)
		{
			MainWindowUI.Instance.WindowSize = size;
			MainWindowUI.Instance.Fit();
		}

	}
}
