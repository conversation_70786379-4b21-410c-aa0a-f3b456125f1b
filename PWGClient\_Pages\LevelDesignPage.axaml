<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:i="clr-namespace:Avalonia.Xaml.Interactivity;assembly=Avalonia.Xaml.Interactivity"
			 xmlns:ia="clr-namespace:Avalonia.Xaml.Interactions.Core;assembly=Avalonia.Xaml.Interactions"
			 xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
			 xmlns:local="clr-namespace:Tabula.PWGClient"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Tabula.PWGClient.LevelDesignPage">
	<Border Classes="Card" Margin="7,7,-5,8">
		<Grid ColumnDefinitions="Auto,*">

			<!-- Tools -->
			<StackPanel Grid.Column="0" Orientation="Vertical" Margin="0,0,10,0">
				<!--
				<ToggleButton IsChecked="{Binding DrawCursor}" Margin="2" Background="Gray">
					<ToggleButton.Styles>
						<Style Selector="ToggleButton Image.tbchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton Image.tbunchecked">
							<Setter Property="IsVisible" Value="true"/>
						</Style>
						<Style Selector="ToggleButton:checked Image.tbunchecked">
							<Setter Property="IsVisible" Value="false"/>
						</Style>
					</ToggleButton.Styles>
					<Panel>
						<Image Source="/Images/buttons/cursor.png" Width="30" Height="30" Classes="tbunchecked"/>
						<Image Source="/Images/buttons/cursor.png" Width="30" Height="30" Classes="tbchecked"/>
					</Panel>
				</ToggleButton>
				-->

				<!-- Create Dialog -->
				<Button  IsVisible="{Binding CanCreateEntities}" Classes="Primary" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0"  Padding="5"
						ToolTip.Tip="Create Object or Effect"
						Click="bt_create_entity_Click">

					<materialIcons:MaterialIcon Kind="Plus" Width="27" Height="25"/>
				</Button>

				<!-- Settings  -->
				<!--
				<Button Name="bt_settings"
						HorizontalAlignment="Center"
						Margin="0" Padding="5" BorderThickness="1.2"
						ToolTip.Tip="Settings"
						Width="45" Height="44"
						Click="bt_settings_Click">
					<materialIcons:MaterialIcon Kind="Settings" Width="20" Height="20"/>
				</Button>
				-->

				<!-- Draw Structure -->
				<Button HorizontalAlignment="Center"
						Classes.Primary="{Binding IsDrawingStructure}"
						Margin="0" Padding="5" BorderThickness="1.2"
						ToolTip.Tip="Draw Structure"
						Click="bt_add_structure_Click">
					<Image Source="/Images/buttons/edit.png" Width="25" Height="25"/>
				</Button>
				
				<Button HorizontalAlignment="Center" Margin="0" Padding="5" ToolTip.Tip="Delete selected"
						Click="bt_delete_click">
					<Image Source="/Images/buttons/delete.png" Width="25" Height="25"/>
				</Button>

				<!-- Fit -->
				<Button HorizontalAlignment="Center" Margin="0,20,0,0" Padding="5" ToolTip.Tip="Fit into view">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="Fit"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/fit.png" Width="25" Height="25" />
				</Button>

				<!-- ZoomOut -->
				<Button HorizontalAlignment="Center" Margin="0" Padding="5" ToolTip.Tip="Zoom out">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="ZoomOut"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/zoom_in.png" Width="25" Height="25"/>
				</Button>

				<!-- ZoomIn -->
				<Button HorizontalAlignment="Center" Margin="0" Padding="5" ToolTip.Tip="Zoom in">
					<i:Interaction.Behaviors>
						<ia:EventTriggerBehavior EventName="Click" SourceObject="{Binding $parent[Button]}">
							<ia:CallMethodAction TargetObject="{Binding}" MethodName="ZoomIn"/>
						</ia:EventTriggerBehavior>
					</i:Interaction.Behaviors>

					<Image Source="/Images/buttons/zoom_out.png" Width="25" Height="25"/>
				</Button>


				<!-- Settings Popup -->
				<Popup Name="settings_popup" PlacementTarget="{Binding #bt_settings}" PlacementGravity="Right" PlacementAnchor="Right" PlacementMode="Right" DataContext="{Binding}">
					<Border Classes="Card" Background="{DynamicResource ExpanderBackground}" Padding="10" CornerRadius="10">

						<StackPanel Orientation="Vertical">

							<TextBlock Text="Scene Settings" HorizontalAlignment="Center" TextAlignment="Center" Margin="10"/>
							<!--
							<Grid ColumnDefinitions="*,*">
								<TextBlock Grid.Column="0"  Classes="property" Text="Width" VerticalAlignment="Center"/>
								<TextBox Grid.Column="1"  Classes="property" Text="{Binding CustomResolutionWidth}"/>
							</Grid>

							<Grid ColumnDefinitions="*,*">
								<TextBlock Grid.Column="0"  Classes="property" Text="Height" VerticalAlignment="Center"/>
								<TextBox Grid.Column="1"  Classes="property" Text="{Binding CustomResolutionHeight}"/>
							</Grid>

							<Grid ColumnDefinitions="*,*">
								<TextBlock Grid.Column="0"  Classes="property" Text="Spout" VerticalAlignment="Center"/>
								<CheckBox Grid.Column="1"  Classes="property" IsChecked="{Binding CustomResolutionSpout}" IsEnabled="{Binding CanUseSpout}"/>
							</Grid>


							<Button Content="Start" Click="bt_custom_resolution_start_Click" Margin="10"/>

							<Button Content="Cancel" Click="bt_custom_resolution_cancel_Click" Margin="10"/>
							-->
						</StackPanel>
					</Border>
				</Popup>
				

				<!-- Lock Structures -->

				<Button HorizontalAlignment="Center"
						Classes.Primary="{Binding AreStructuresLocked}"
					
						Margin="0,20,0,0" Padding="6" ToolTip.Tip="Lock/Unlock Structures"
						Click="bt_lock_structures_Click">
					<materialIcons:MaterialIcon Kind="Lock" Width="20" Height="20"/>
				</Button>			

			</StackPanel>


			<Grid Grid.Column="1">
				<local:LevelEditor x:Name="level_editor" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
				<Canvas x:Name="canvas" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
			</Grid>
			

			<!-- Info box -->
			<local:InfoBox Grid.Column="1" IsVisible="{Binding InfoBox.IsVisible}"/>
		</Grid>
	</Border>

</UserControl>
