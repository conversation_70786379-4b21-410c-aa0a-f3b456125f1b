using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using DynamicData.Kernel;
using PropertyChanged;
using Sentry;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Tabula.Licensing.LicenseActivator;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;
using Tabula.SKRenderGraph;

namespace Tabula.PWGClient
{
	// UI panel for Warp / Region / Calibrate

	[DoNotNotify]
    public partial class ScreenEditorPanel : UserControl
    {
        public ScreenEditorPanel()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public void Warp()
        {
            if (MainWindowUI.Instance.View == null)
                return;

			_show_output_surface(true);

			MainWindowUI.Instance.MainSelectedItem = null;

			MainWindowUI.Instance.ProjectionSetupMode = MainWindowUI.ProjectionSetupModes.Warp;

			// infobox test:
			MainWindowUI.Instance.InfoBox.IsVisible = true;
			MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
			MainWindowUI.Instance.InfoBox.Message = "Move the corners to create a rectangular projection zone.";
			MainWindowUI.Instance.InfoBox.ErrorMessage = null;
			MainWindowUI.Instance.InfoBox.IsActionButtonVisible = false;
			MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = false;

			InfoBox.StopCheckAction();

			var surface_views = PMViewExt.GetPMViewsFromModel(MainWindowUI.Instance.View.Model.OutputSurfaces[0]);

            if (surface_views == null) return;

            ScreenEditor.Instance.Scene.UnSelectAllObjects();

            foreach (var v in surface_views)
                switch (v)
                {
                    case PMOutputSurfaceView osv:
                        osv.Visual.IsEnabled = true;
						osv.VisualIsDirty = true;
                        osv.Visual.Scene.SelectObject(osv.Visual);
                        break;

					case PMOutputSurfaceRegionView osrv:
						osrv.VisualIsDirty = true;
						osrv.Visual.IsEnabled = false;
						break;
				}

			// enable all calibration points
			_show_screen_markers(false);
			_show_image_markers(false);

			// try setting up calibrator (if we are in that scene)
			// public void ConfigureCalibrator(bool toggle_grid, bool toggle_spatialgrid, bool toggle_pattern_background, bool toggle_structures,  bool toggle_calibration_points)
			Task.Run(async () => await MainWindowUI.Instance.View.CallMethod("ConfigureCalibrator", false, true, false, true, false));
		}

		public void Region()
		{
			if (MainWindowUI.Instance.View == null)
				return;

			_show_output_surface(true);

			MainWindowUI.Instance.MainSelectedItem = null;

			MainWindowUI.Instance.ProjectionSetupMode = MainWindowUI.ProjectionSetupModes.Region;

			// infobox test:
			MainWindowUI.Instance.InfoBox.IsVisible = true;
			MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
			MainWindowUI.Instance.InfoBox.Message = "If needed, resize the projection area until you see perfect circles and squares, even if not complete.";
			MainWindowUI.Instance.InfoBox.ErrorMessage = null;
			MainWindowUI.Instance.InfoBox.IsActionButtonVisible = false;
			MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = false;

			// checks that the area is not too small
			InfoBox.StartCheckAction(() =>
			{
				if (App.Client == null || App.Client.Model == null)
					return;

				try
				{
					var region = App.Client.Model.GetVisualScreenSize(0);

					if (region.width < 800 || region.height < 600)
						MainWindowUI.Instance.InfoBox.ErrorMessage = "The region may be too small for playing";
					else
						MainWindowUI.Instance.InfoBox.ErrorMessage = null;
				}
				catch(Exception ex)
				{

				}
			});

			var pmviews = PMViewExt.GetPMViewsFromModel(MainWindowUI.Instance.View.Model.OutputSurfaces[0]);

			ScreenEditor.Instance.Scene.UnSelectAllObjects();

			foreach (var v in pmviews)
				switch (v)
				{
					case PMOutputSurfaceView osv:
						osv.Visual.IsEnabled = false;
						break;

					case PMOutputSurfaceRegionView osrv:
						osrv.Visual.IsEnabled = true;
						osrv.Visual.Scene.SelectObject(osrv.Visual);
                        break;
                }

			// disable all calibration points
			_show_screen_markers(false);
			_show_image_markers(false);

			// try setting up calibrator (if we are in that scene)
			// public void ConfigureCalibrator(bool toggle_grid, bool toggle_spatialgrid, bool toggle_pattern_background, bool toggle_structures,  bool toggle_calibration_points)
			Task.Run(async () => await MainWindowUI.Instance.View.CallMethod("ConfigureCalibrator", true, false, false, false, false));
		}

        public async Task Calibrate()
        {
			if (MainWindowUI.Instance.View == null)
				return;

			MainWindowUI.Instance.MainSelectedItem = null;

			if (!App.CalibrationEnabled)
			{
				await ProgressDialog.ShowMessageAsync("Calibration is not available yet");
				return;
			}


			MainWindowUI.Instance.ProjectionSetupMode = MainWindowUI.ProjectionSetupModes.CalibrateScreenMarkers;

			// clear the current image
			ScreenEditor.Instance.LoadCalibrationImage("");

			// Calibrate can only be used when using Calibrator
			if (MainWindowUI.Instance.View.ProjectName != "calibrator")
			{
				await ProgressDialog.ShowMessageAsync("Calibration can only be used with the Calibrator module");
				Warp();
				return;
			}

			// Warn the user that starting the calibration will cancel current one
#if DEBUG

			var choice = await ProgressDialog.ShowChoicesAsync(
						"You are about to start a new calibration, discarding the current one.",
						new string[] { "Continue", "Cancel", "Re-Try" });

#else
			var choice = await ProgressDialog.ShowChoicesAsync(
						"You are about to start a new calibration, discarding the current one.",
						new string[] { "Continue", "Cancel" });

#endif


			if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
			{
				// go back to Warp
				Warp();
				return;
			}

			ScenarioEntry CurrentScenario = App.Instance.GetCurrentScenario();
			if (CurrentScenario == null)
			{
				await ProgressDialog.ShowMessageAsync("ERROR: Scenario cannot be found");
				return;
			}

			// DEBUG: re-tries calibration simulating a new upload of the same image
			if (choice == ProgressDialog.Choice.Choice3)
				MainWindowUI.Instance.RetryCalibration = true;
			else
			{
				MainWindowUI.Instance.RetryCalibration = false;

				// should delete current calibration image
				if (File.Exists(CurrentScenario.CalibrationImagePath))
					try
					{
						File.Delete(CurrentScenario.CalibrationImagePath);
					}
					catch { }
			}

			// Tracks user action in infobox to advance the calinration process
			int user_calibration_step = 0;	// 1: start 2: confirm markers



			// infobox
			MainWindowUI.Instance.InfoBox.IsVisible = true;
			MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
			MainWindowUI.Instance.InfoBox.Message = "Make sure all four calibration points are visible and positioned near the corner areas and on the the wall plane.\nThey should not overlap objects and be surrounded by enough white space.";
			MainWindowUI.Instance.InfoBox.ErrorMessage = null;
			MainWindowUI.Instance.InfoBox.IsActionButtonVisible = true;
			MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = false;
			MainWindowUI.Instance.InfoBox.ActionButtonText = "Start Calibration";
			MainWindowUI.Instance.InfoBox.OnActionButton = () => user_calibration_step = 1; // advance			

			var pm_screen_markers = PMViewExt.GetPMViewsOfType<PMScreenMarkerView>();

			bool markers_are_well_positioned = false;

			// checks that calibration points are inside the region
			InfoBox.StartCheckAction(() =>
			{
				if (App.Client == null || App.Client.Model == null)
					return;

				try
				{
					var marker_rect = App.Client.Model.GetVisualMarkerSafeArea(0);
					var marker_skrect = new SKRect(marker_rect.x, marker_rect.y, marker_rect.x + marker_rect.width, marker_rect.y + marker_rect.height);		

					bool error = false;

					foreach (var m in pm_screen_markers)
					{
						if (!m.Rect.IsInside(marker_skrect))
						{
							error = true;
							break;
						}
					}

					if (error)
						MainWindowUI.Instance.InfoBox.ErrorMessage = "All calibration points must be inside the dashed marker area.";
					else
						MainWindowUI.Instance.InfoBox.ErrorMessage = null;

					markers_are_well_positioned = !error;
				}
				catch (Exception ex)
				{}

				
			});

			var pmviews = PMViewExt.GetPMViewsFromModel(MainWindowUI.Instance.View.Model.OutputSurfaces[0]);

			ScreenEditor.Instance.Scene.UnSelectAllObjects();

			foreach (var v in pmviews)
				switch (v)
				{
					case PMOutputSurfaceView osv:
						osv.Visual.IsEnabled = false;
						break;

					case PMOutputSurfaceRegionView osrv:
						osrv.Visual.IsEnabled = false;
						break;
				}

			_show_output_surface(false);

			// enable all calibration points
			_show_screen_markers(true);
			_show_image_markers(false);

			// try setting up calibrator (if we are in that scene)
			//public void ConfigureCalibrator(bool toggle_grid, bool toggle_spatialgrid, bool toggle_pattern_background, bool toggle_structures,  bool toggle_calibration_points)
			Task.Run(async () => await MainWindowUI.Instance.View.CallMethod("ConfigureCalibrator", false, false, true, false, true));

			//// USER STEP 1: Confirm the markers

			// TODO: make sure user has app?
			// await ProgressDialog.ShowMessageAsync("Position markers in the projection.\nThey must be near the corners, not overlapping objects.");

			// Clicking on the action button will start the process
screen_markers_confirm:

			while (user_calibration_step != 1)
				await Task.Delay(50);

			if (!markers_are_well_positioned)
			{
				await ProgressDialog.ShowMessageAsync("Position markers in the projection.\nThey must be near the corners, not overlapping objects and inside the yellow area.");
				user_calibration_step = 0;
				goto screen_markers_confirm;
			}

			MainWindowUI.Instance.InfoBox.IsVisible = false;

			string _generate_random_authcode(int length)
			{
				var random = new Random();
				string s = string.Empty;
				for (int i = 0; i < length; i++)
					s = string.Concat(s, random.Next(10).ToString());
				return s;
			}

			CalibrationProcessing.Result calib_processing_result = null;
			string calib_error_message = string.Empty;

			if (MainWindowUI.Instance.LicenseSerial == null)
			{
				// cannot proceed, not licensed, should not happen
				calib_error_message = "Not licensed!";
				goto calibration_failed;
			}

			// get authcode (anche check connectivity)
			string authcode = null;

			Task.Run(async () =>
			{
				try
				{
					authcode = LicenseActivatorLib.GetAuthCode(MainWindowUI.Instance.LicenseSerial, admin: true, use_legacy_hid: true);
				}
				catch (Exception ex)
				{
					SARGAME.App.logException("GetAuthCode() in StartCalibration()", ex);
					authcode = null;
				}

				if (authcode == null)
					authcode = "ERROR: Please check your internet connection";
			});

			var calib_view = new CalibrationProcessing.View()
			{
				Message = "Receiving Auth Code...",
				Icon = Material.Icons.MaterialIconKind.CameraOutline,
				IsProgressVisible = false,
				IsAuthCodeVisible = false,
				AuthCode = null
			};

			// Simulates randomized authcodes until one is available
			calib_view.IsProgressVisible = true;
			DispatcherTimer timer_authcode = new DispatcherTimer(TimeSpan.FromSeconds(0.05), DispatcherPriority.Default,
				(e, a) =>
				{
					if (!string.IsNullOrEmpty(authcode))					
					{
						calib_view.Message = "Take a picture of the projection using the S-ARGAME Mobile App and the provided Auth Code.";

						calib_view.AuthCode = authcode;
						calib_view.IsAuthCodeVisible = true;
						calib_view.IsProgressVisible = true;
						calib_view.StartCalibration = true;

						(e as DispatcherTimer).Stop();
					}
				});

			// CALIBRATION STEP 1 -> find image markers

			calib_processing_result = await CalibrationProcessing.CalibrationStep1(calib_view);

			if (calib_processing_result.outcome != CalibrationProcessing.CalibrationOutcome.Success)
				goto calibration_failed;

			// download now the original calibration image, because it is needed for marker confirmation
			var calibration_image_temp_path = await LicenseActivatorLib.CalibrationImageDownload(calib_view.AuthCode);

			if (string.IsNullOrEmpty(calibration_image_temp_path))
			{
				calib_processing_result.outcome = CalibrationProcessing.CalibrationOutcome.ErrorCannotDownloadImage;
				SARGAME.App.logError($"Calibration ERROR, cannot download image");

				Task.Run(() => LicenseActivatorLib.CalibrationArchive(calib_view.AuthCode));

				goto calibration_failed;
			}

			// Save calibration image in scenario
			try
			{
				File.Copy(calibration_image_temp_path, CurrentScenario.CalibrationImagePath, true);
			}
			catch
			{
				calib_error_message = "Error saving calibration image";
				goto calibration_failed;
			}

			// update the model with the detected image markers, up to the max
			// this is just needed to let the user move them or disable them
			// then in CalibrationStep2 only the positions will be update re-using the reiceived ImageData.ImageMarkers (which have rating, raius etc..)
			// since any number of markers could be returned, we make sure at least 4 are there
			var image_markers = calib_processing_result.image_data.ImageMarkers;

			App.Client.View.Screen.CommitBegin();

			for (int i = 0; i < App.Client.View.Screen.image_markers.Count; i++)
			{
				var im = App.Client.View.Screen.image_markers[i];

				if (i < image_markers.Count)
				{
					im.position.x = (float)image_markers[i].position.x;
					im.position.y = (float)image_markers[i].position.y;
					im.enabled = true;
				}
				else
				{
					im.position.x = 0;
					im.position.y = 0;
					im.enabled = false;
				}
			}

			// at least 4 must be enabled
			int enabled_markers = image_markers.Count;
			if (enabled_markers < 4)
			{
				// since we received < 4, we are going to enable the model ones, that have no rating/radius..
				// we will average those later

				for (int i = 0; i < App.Client.View.Screen.image_markers.Count; i++)
				{
					var im = App.Client.View.Screen.image_markers[i];

					if (!im.enabled)
					{
						im.enabled = true;
						enabled_markers++;
					}

					if (enabled_markers == 4)
						break;
				}
			}


			App.Client.View.Screen.CommitEnd();


			MainWindowUI.Instance.ProjectionSetupMode = MainWindowUI.ProjectionSetupModes.CalibrateImageMarkers;

			// Will ask for marker visual confirmation only if we have a wrong number and not good rating.
			bool wait_user_marker_confirm =
				 !((calib_processing_result.image_data.ImageMarkers.Count == 4 &&
				  (from m in calib_processing_result.image_data.ImageMarkers where m.rating >= 1.0 select m).Count() == 4));

			_show_screen_markers(false);

			if (wait_user_marker_confirm)
			{
				MainWindowUI.Instance.InfoBox.IsVisible = true;
				MainWindowUI.Instance.InfoBox.Icon = Material.Icons.MaterialIconKind.InformationOutline;
				MainWindowUI.Instance.InfoBox.Message = "Review the detected markers. There must be exactly 4 of them.";
				MainWindowUI.Instance.InfoBox.ErrorMessage = null;
				MainWindowUI.Instance.InfoBox.IsActionButtonVisible = true;
				MainWindowUI.Instance.InfoBox.ActionButtonText = "Confirm Markers";
				MainWindowUI.Instance.InfoBox.OnActionButton = () => user_calibration_step = 2; // advance
				MainWindowUI.Instance.InfoBox.IsCancelButtonVisible = true;
				MainWindowUI.Instance.InfoBox.OnCancelButton = () => user_calibration_step = -1; // cancel

				// make it available to the ScreenEditor for showing
				ScreenEditor.Instance.LoadCalibrationImage(CurrentScenario.CalibrationImagePath);
				_show_image_markers(true);
				ScreenEditor.Instance.Refresh();    // otherwise it won't update

				await ProgressDialog.ShowMessageAsync("Review the detected markers.\nThere must be exactly 4 of them.");

				IEnumerable<ImageMarkerView> enabled_image_markers;

			// wait
			image_marker_confirm:
				while (user_calibration_step != 2)
				{
					// PERSPECTIVE ERROR
					/*
					// while the user is moving the image marker, calculate homography and the perspective error
					enabled_image_markers = (from i in App.Client.View.Screen.image_markers where i.enabled select i);

					if (enabled_image_markers.Count() == 4)
					{
						double perspective_error = App.Client.View.Screen.GetHomographyPerspectiveError();

						System.Diagnostics.Debug.WriteLine($"PerspectiveError = {perspective_error}");
					}
					*/

					if (user_calibration_step == -1)
					{
						calib_processing_result.outcome = CalibrationProcessing.CalibrationOutcome.Cancel;
						goto calibration_failed;
					}


					await Task.Delay(50);
				}

				// check there are exactly 4 markers
				enabled_image_markers = (from i in App.Client.View.Screen.image_markers where i.enabled select i);

				if (enabled_image_markers.Count() != 4)
				{
					await ProgressDialog.ShowMessageAsync("There must be exactly 4 detected markers.");
					user_calibration_step = 1;
					goto image_marker_confirm;
				}

			}

			ScreenEditor.Instance.Refresh();

			// CALIBRATION STEP 2

			calib_view.Message = "Detecting shapes...";
			calib_view.IsProgressVisible = true;
			calib_view.IsAuthCodeVisible = false;

			calib_processing_result = await CalibrationProcessing.CalibrationStep2(calib_view, calib_processing_result.image_data);

			// NOTE: no shapes is considered an error
			if (calib_processing_result.outcome != CalibrationProcessing.CalibrationOutcome.Success)
				goto calibration_failed;

			// update the model with the calibration
			// also screen_markers must be reset because they could have been reordered on the server
			var screen_markers = calib_processing_result.image_data.screen_markers;
			image_markers = calib_processing_result.image_data.ImageMarkers;

			App.Client.View.Screen.CommitBegin();

			for (int i = 0; i < screen_markers.Count; i++)
			{
				App.Client.View.Screen.screen_markers[i].position.x = (float) screen_markers[i].x;
				App.Client.View.Screen.screen_markers[i].position.y = (float) screen_markers[i].y;
			}

			for (int i = 0; i < image_markers.Count; i++)
			{
				App.Client.View.Screen.image_markers[i].position.x = (float) image_markers[i].position.x;
				App.Client.View.Screen.image_markers[i].position.y = (float) image_markers[i].position.y;
			}

			App.Client.View.Screen.CommitEnd();			

			// create the structures !
			if (calib_processing_result.image_data.Shapes.Count > 0)
			{
				var cts = new CancellationTokenSource();

				// TODO: cancellation!

				var msgbox = ProgressDialog.ShowMessage(
					message: $"Creating shapes...",
					progress: true,
					cancel_button_text: "Cancel",
					cancellation_token: cts.Token,
					on_cancel_button_click: () => cts.Cancel()
					);

				await Task.Run(async () =>
				{
					await MainWindowUI.Instance.View.AddStructuresFromCalibration(calib_processing_result.image_data);
				}, cts.Token);

				msgbox.Close();

				MainWindowUI.Instance?.RefreshHierarchy();


				// switch to LevelDesign and show notice
				MainView.Instance.tab_level_design.IsSelected = true;
			}
			else
			{
				// NO SHAPES, should not arrive here
			}

			await ProgressDialog.ShowMessageAsync("The calibration succeeded!");

			return;



		calibration_failed:
		
			if (calib_processing_result != null)
				switch (calib_processing_result.outcome)
				{
					case CalibrationProcessing.CalibrationOutcome.ErrorFromServer:
						calib_error_message = "The calibration failed on the server.";
						break;

					case CalibrationProcessing.CalibrationOutcome.ErrorCannotDownloadImage:
						calib_error_message = "The calibration failed.\nCannot download image.";
						break;

					case CalibrationProcessing.CalibrationOutcome.ErrorNoShapes:
						calib_error_message = "No shapes could be found.";
						break;

					case CalibrationProcessing.CalibrationOutcome.Success:
						break;

					case CalibrationProcessing.CalibrationOutcome.Cancel:
						Warp();
						break;

					default:
						calib_error_message = "Unknown error";
						break;
				}

			if (!string.IsNullOrEmpty(calib_error_message))
				await ProgressDialog.ShowMessageAsync(calib_error_message);

			Warp();

		}

		private void _show_screen_markers(bool toggle)
		{
			var pm_screen_markers = PMViewExt.GetPMViewsOfType<PMScreenMarkerView>();
			foreach (var c in pm_screen_markers)
			{
				c.Visual.IsEnabled = toggle;
				c.Visual.IsHitTestVisible = toggle;
			}
		}

		private void _show_image_markers(bool toggle)
		{
			var pm_image_markers = PMViewExt.GetPMViewsOfType<PMImageMarkerView>();
			foreach (var c in pm_image_markers)
			{
				c.Visual.IsEnabled = toggle;
				c.Visual.IsHitTestVisible = toggle;
			}
		}

		public void _show_output_surface(bool toggle)
		{
			foreach (var c in PMViewExt.GetPMViewsOfType<PMOutputSurfaceView>())
			{
				c.Visual.IsEnabled = toggle;
				c.Visual.IsHitTestVisible = toggle;
			}

			foreach (var c in PMViewExt.GetPMViewsOfType<PMOutputSurfaceRegionView>())
			{
				c.Visual.IsEnabled = toggle;
				c.Visual.IsHitTestVisible = toggle;
			}
		}

	}
}
