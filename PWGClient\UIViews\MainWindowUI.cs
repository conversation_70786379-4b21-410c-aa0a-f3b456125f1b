﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Animation;
using Avalonia.Controls;
using Avalonia.Controls.Chrome;
using Avalonia.Controls.Shapes;
using Avalonia.Controls.Templates;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Media;
using HomographySharp;
using Microsoft.CodeAnalysis;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1;
using PropertyChanged;
using ReactiveUI;
using SkiaSharp;
using Tabula.Licensing;
using Tabula.Log;
using Tabula.PMCore;
using Tabula.PWG.SARGAME;
using Tabula.PWGClient;
using Tabula.SharedObjectMap;
using Tabula.SKRenderGraph;
using Tabula.Unity;
using static System.Runtime.InteropServices.JavaScript.JSType;
using static Tabula.PMCore.Entity;

namespace Tabula.PWGClient
{

    public class MainWindowUI : BasePropertyChanged
    {
        public static MainWindowUI? Instance;

        //public ModelView View { get; set; } // the view of the Model, when active and received
        public ModelView View => SARGAME.View;

        internal SARGAME SARGAME => App.SARGAME;

        #region Bridge to App, for single access in core using dynamic

        public LevelEditor LevelEditor => LevelEditor.Instance;

        public static async Task<object?> ShowMessageAsync(string message, string cancel_button_text = "OK", string identifier = "dialog")
            => await ProgressDialog.ShowMessageAsync(message, cancel_button_text, identifier: identifier);

        public byte[] GetIcon(string icon_name) => App.GetIcon(icon_name);

        public void SyncGuidObjectsDirectWithoutNotify(List<Tabula.RPC.Shared.GuidObjectFastUpdate> updates, bool reliable = true)
            => App.Client.SyncGuidObjectsDirectWithoutNotify(updates, reliable);

		#endregion

		public string Version => App.Instance.GetVersionDescriptive();

        public string WindowTitle => $"S-ARGAME Editor ({Version})";

        public string EditedLicenseSerial { get; set; }     // the one in the license textbox
        public string EditedLicenseSerialPasswordChar => !CanEditLicenseSerial ? "*" : null;
        public string LicenseSerial => App.LicenseInfo != null ? App.LicenseInfo.Serial : null;
        public Color LicenseColor => IsLicensed ? Colors.Green : Colors.Red;

        public bool IsLicensed => App.IsLicensed;   //NOTE: this is not the low level Licensed bool
        public bool IsTrial => App.IsTrial;
        public bool IsTrialExpired => App.IsTrialExpired;
        public bool IsTimeLicense => App.IsTimeLicense;
        public bool IsTimeLicenseExpired => App.IsTimeLicenseExpired;

        public bool CanEditLicenseSerial => string.IsNullOrEmpty(LicenseSerial) || IsTrial || IsTimeLicenseExpired;
        public bool CanActivateLicense
        {
            get
            {
                if (IsTimeLicenseExpired)
                    return true;

                if (IsTrial)
                    return true;

                if (IsLicensed)
                    return false;
                else
                    return string.IsNullOrEmpty(LicenseSerial);
            }

        }
		public bool     CanDeactivateLicense => !string.IsNullOrEmpty(LicenseSerial) && !IsTrial && !IsTimeLicenseExpired;
		public bool     CanUpdateLicense => !string.IsNullOrEmpty(LicenseSerial) && !IsTrial;
		public bool     CanActivateTrial
        {
            get
            {
                if (!IsLicensed)
                {
                    if (IsTrialExpired)
                        return false;

                    return true;
                }
                else
                {
                    if (IsTrial)
                        return false;
                }
            
                
                return false;
            }
        }

        public bool CanUseSpout => OperatingSystem.IsWindows() && (LocalAppConfig.Instance.CheckLicenseFeature("business") || LocalAppConfig.Instance.CheckLicenseFeature("spout"));

        public bool CanUseCustomResolution => LocalAppConfig.Instance.CheckLicenseFeature("business");

		public string LicenseDescription => App.Instance.GetLicenseDescription();

        [AlsoNotifyFor(nameof(LicenseDescription))]
        public bool LicenseNeedRestart { get; set; } = false;

        public UnityToolData UnityToolData { get; set; }

        public bool IsBusy { get; set; } = false;

        [DependsOn(nameof(IsBusy))]
        public double IsBusyOpacity => IsBusy ? 1.0 : 0.0;

        public bool IsInspectorVisible { get; set; } = true;

        [DependsOn(nameof(IsInspectorVisible))]
        public int MainTabColumnSpan => IsInspectorVisible ? 3 : 1;

        [DependsOn(nameof(IsInspectorVisible), nameof(SelectedItemForInspector))]
        public bool ShouldShowInspector => IsInspectorVisible && SelectedItemForInspector != null;

        public bool IsApplicationInFocus
        {
            get
            {
                if (App.Client == null)
                    return true;

                if (!App.Client.IsConnected)
                    return true;

                if (!App.Client.Model.ProjectFlags.HasFlag(Model.Flags.FocusNotice))
                    return true;

                if (GeneralSettingsEntity != null)
                {
                    var ev = (EntityView)GeneralSettingsEntity;
                    var detect_input_focus = ev.GetFieldValue<bool>("detect_input_focus");
                    if (detect_input_focus != null)
                    {
                        if (! (bool) detect_input_focus)
							return true;
					}

                }
				return App.Client.View.ModuleFocus;
            }
        }

        public bool IsGame => App.Client != null ? App.Client.IsConnected ? App.Client.Model.ProjectFlags.HasFlag(Model.Flags.Game) : false : false;
		public bool IsExperience => App.Client != null ? App.Client.IsConnected ? App.Client.View.ProjectFlags.HasFlag(Model.Flags.Experience) : false : false;
		public bool CanCreateEntities => App.Client != null ? App.Client.IsConnected ? App.Client.Model.ProjectFlags.HasFlag(Model.Flags.CreateEntities) && EntitiesToCreate.Count > 0 : false : false;
		public bool CanAddStructureEffects => App.Client != null ? App.Client.IsConnected ? App.Client.Model.ProjectFlags.HasFlag(Model.Flags.StructureEffects) : false : false;

        public bool UseRecentCreatedObject = false;

        // Last scenario/monitor (for quick restart)
        public ScenarioEntry LastScenario;
        public Unity.Display LastMonitor;
        public bool?         LastIsWindowed=null;

		// Auth codes
		public string AuthCode { get; set; }
		public string AdminAuthCode { get; set; }

        // Player stats
        public int PlayersWaiting { get; set; } = 0;
		public int PlayersSetup { get; set; } = 0;
		public int PlayersReady { get; set; } = 0;
		public int PlayersPlaying { get; set; } = 0;

		// Connections stats
		public int ServerConnections { get; set; } = 0;
		public int ServerExceptions { get; set; } = 0;
		public int ServerCalls { get; set; } = 0;
		public int ServerMessagesSent { get; set; } = 0;
		public int ServerMessagesReceived { get; set; } = 0;

		[DependsOn(nameof(PlayersWaiting), nameof(PlayersSetup), nameof(PlayersReady), nameof(PlayersPlaying))]
        public string PlayersStats // { get; set; }  = null; // TODO
             => $"playing:{PlayersPlaying}\twaiting:{PlayersWaiting}\t\tsetup:{PlayersSetup}\tready:{PlayersReady}";

		[DependsOn(nameof(ServerConnections), nameof(ServerExceptions), nameof(ServerCalls), nameof(ServerMessagesSent), nameof(ServerMessagesReceived))]
		public string ServerStats // { get; set; }  = null; // TODO
			 => $"connections:{ServerConnections}\tcalls:{ServerCalls}\tmsg_tx:{ServerMessagesSent}\tmsg_rx:{ServerMessagesReceived}\texceptions:{ServerExceptions}";

		[DependsOn(nameof(AuthCode), nameof(AdminAuthCode))]
        public bool HasAuthCodes => !string.IsNullOrEmpty(AuthCode) || !string.IsNullOrEmpty(AdminAuthCode);

        // Support

        public string SupportRequestEmail { get; set; }
		public string SupportRequestMessage { get; set; }

		// Info Box

		public InfoBox.View InfoBox = new InfoBox.View();

        public enum ProjectionSetupModes
        {
            Warp,
            Region,
            CalibrateScreenMarkers, // setup screen markers
            CalibrateImageMarkers   // confirm image markers
        }

        [AlsoNotifyFor(nameof(WarpButtonColor), nameof(RegionButtonColor), nameof(CalibrateButtonColor))]
        public ProjectionSetupModes ProjectionSetupMode { get; set; } = ProjectionSetupModes.Warp;

        public IBrush WarpButtonColor => ProjectionSetupMode == ProjectionSetupModes.Warp ? Brushes.Red : Brushes.DarkGray;

        public IBrush RegionButtonColor => ProjectionSetupMode == ProjectionSetupModes.Region ? Brushes.Red : Brushes.DarkGray;

        public IBrush CalibrateButtonColor => (ProjectionSetupMode == ProjectionSetupModes.CalibrateScreenMarkers || ProjectionSetupMode == ProjectionSetupModes.CalibrateImageMarkers) ? Brushes.Red : Brushes.DarkGray;

        public bool RetryCalibration { get; set; } = false; // DEBUG: will retry calibration

        public bool PanToggle { get; set; } // UI locking of the scene pan

        private bool _draw_cursor_toggle = true;
        public bool DrawCursor
        {
            get => _draw_cursor_toggle;

            set
            {
                _draw_cursor_toggle = value;
                View.Cursor.draw = value;
            }
        }

        private async Task<float> _get_BackgroundScale()
        {
            try
            {
                var scale = await App.Client.View.GetProperty("BackgroundScale");
                return Convert.ToSingle(scale);
            }
            catch
            {
                return 0.5f;
            }
        }

        float _bg_scale = 0.5f;
        public float BackgroundScale
        {
            get => _bg_scale;


            set
            {
                _bg_scale = value;
                App.Client?.View?.CallMethod("SetBackgroundScale", new object[] { value });
            }
        }

        // Shape drawing / polygon
        public float CloseShapePointDistance = 20;

        // Window and dialoghost sizes

        public Size WindowSize { get; set; }

        [DependsOn(nameof(WindowSize))]
        public Size DialogBigSize => new Size(WindowSize.Width - WindowSize.Width * 0.2f, WindowSize.Height - WindowSize.Height * 0.2);

        [DependsOn(nameof(WindowSize))]
        public Size DialogMediumSize => new Size(WindowSize.Width - WindowSize.Width * 0.4f, WindowSize.Height - WindowSize.Height * 0.4);

        [DependsOn(nameof(WindowSize))]
        public Size DialogSmallSize => new Size(WindowSize.Width - WindowSize.Width * 0.6f, WindowSize.Height - WindowSize.Height * 0.6);


        public string MonitorInfoMessage { get; set; } = "";

		// Custom resolution
		public bool CustomResolutionEnabled { get; set; } = false;
		public int CustomResolutionWidth { get; set; } = 3840;
		public int CustomResolutionHeight { get; set; } = 1080;
		public bool CustomResolutionSpout { get; set; } = false;

		// Notifications
		public string NotificationText { get; set; } = "";
        public double NotificationOpacity { get; set; } = 0;

        public int EntityMessagesCount { get; set; } = 0; // received entity messages (debug)

        [DependsOn(nameof(EntityMessagesCount))]
        public string EntityMessagesDesc => $"EntityMessages: {EntityMessagesCount}";

        public async Task<List<string>> _get_SceneNames()
        {
            if (!App.Client.IsConnected)
                return new List<string>();

            var scenes = await App.Client.Client.GetScenes();
            return scenes;
        }

        public Task<List<string>> SceneNames => _get_SceneNames();

        public string SelectedSceneName { get; set; }

        public OutputSurfaceGroup outputsurface_group;

        public StructureGroup structure_group;

        // NOTE: this is checked with timer
		public object MainSelectedItem { get; set; }

        public void ForceMainSelectedItem(object item)
        {
            MainSelectedItem = item;
			SelectedItem = null;
			SelectedItem = MainSelectedItem;
		}


        // NOTE: This is bound to the hierarchy TreeView
        private object _selecteditem;
        public object SelectedItem
        {
            get => _selecteditem;

			// NOTE: binding is one way, so this set is managed by MainSelectedItem logic, ForceMainSelectedItem() and CheckUI()
			set
			{
                _selecteditem = value;

                /*
                Debug.WriteLine($"Selected item: {value}");
				StackTrace stackTrace = new StackTrace();
				foreach (StackFrame frame in stackTrace.GetFrames())
				{
					Debug.WriteLine("\t" + frame.GetMethod().Name);
				}
                */


				if (value == null)
                {
                    /*
                    LevelEditor.Instance?.Scene.UnSelectAllObjects();
                    ScreenEditor.Instance?.Scene.UnSelectAllObjects();
                    */
                    return;
                }

                ExpandEntityHierarchy(_selecteditem);

                // If a Structure is selected, send the selection state to the server (and unselect others)
                if (value is StructureView)
                {
                    foreach (var s in View.Structures)
                    {
                        s.selected_for_editing = (s == value);
                    }
                }


                List<SKRenderGraph.SKObject> visuals = null;

                if (value is IGuidObjectSyncView)
                {
                    visuals = (value as IGuidObjectSyncView).GetVisuals();
                }

                // If the object has a visual, select it, otherwise unselect all
                if (visuals != null)
                {
                    try
                    {
                        foreach (var v in visuals)
                            v?.Scene?.SelectObject(v, false);
                    }
                    catch(Exception ex)
                    {

                    }
                }
                else
                {
					LevelEditor.Instance?.Scene.UnSelectAllObjects();
					ScreenEditor.Instance?.Scene.UnSelectAllObjects();
				}

                // TEST: always switch to level_editor if the selected object is a structure or an entity
                if (_selecteditem is StructureView || _selecteditem is EntityView)
                    MainView.Instance.tab_level_design.IsSelected = true;
            }
        }

        // Specialized getter to handle redirections (ex for wrapper objects)
        [DependsOn(nameof(SelectedItem))]
        public object SelectedItemForInspector
        {
            get
            {
                // MainView.Instance.inspector_content.DataContext = MainWindowUI.Instance;

                object obj = null;

                switch (SelectedItem)
                {
                    /*
                    case HierarchyEntityView ev: 
                        obj = ev.View; break;
                    */

                    default:
                        obj = SelectedItem;
                        break;
                }

                // ?
                /*
                var template = MainView.Instance.inspector_content.FindDataTemplate(obj);
                if (template != null)
                {
                    var templated_obj = template.Build(obj);
                }
                */

                return obj;
            }
        }

        private ObservableCollection<object> _hierarchy;
        public ObservableCollection<object> Hierarchy
        {
            get
            {
                if (View == null)
                    return null;

                // ObservableCollection<object> _prev_hierarchy = _hierarchy;

                BuildHierarchy();

                return _hierarchy;
            }
        }

        // Settings page

        public object SelectedGameSettingsTab { get; set; }


		const string _general_settings_entity_name = "#GeneralSettings";
        public object GeneralSettingsEntity
		{
			get
			{
				var e = App.Client?.Model?.FindEntityByName(_general_settings_entity_name);
				if (e == null)
					return null;

				return e.getView();
			}
		}

		const string _visual_settings_entity_name = "#VisualSettings";
        public object VisualSettingsEntity 
        {
            get
            {
                var e = App.Client?.Model?.FindEntityByName(_visual_settings_entity_name);
                if (e == null)
                    return null;

                return e.getView();
            }
        }

        const string _rules_settings_entity_name = "#RulesSettings";    // optional
		public EntityView RulesSettingsEntity
		{
			get
			{
				var e = App.Client?.Model?.FindEntityByName(_rules_settings_entity_name);
				if (e == null)
					return null;

				return e.getView();
			}
		}

        public bool HasRulesSettings =>
            RulesSettingsEntity != null &&
            RulesSettingsEntity.fields != null && RulesSettingsEntity.fields.Count > 0;


		// builds the visual hierarchy according to the model's template
		public void BuildHierarchy()
        {
            _hierarchy = new ObservableCollection<object>();

			// TODO: Use template for ordering?

			// Screen
			//_hierarchy.Add(new HierarchyItem() { Title = "Screen", Items = new ObservableCollection<object>() { View.Screen } });

			// Output
			//outputsurface_group = new OutputSurfaceGroup() { Title = "Output", Items = new ObservableCollection<object>(View.OutputSurfaces) };
			//_hierarchy.Add(outputsurface_group);

			// Structures
			bool was_structures_expanded = structure_group != null ? structure_group.IsExpanded : false;

            // fix: add structure icon here, overriding anything
            /*
            foreach (var s in View.Structures)
                s.Model.icon = "structure";
            */

            structure_group = new StructureGroup() 
                    { 
                        Title = "Structures", 
                        Items = new ObservableCollection<object>(View.Structures),
                        IsExpanded = was_structures_expanded
                    };

            _hierarchy.Add(structure_group);

            // Entities
            // NOTE: no need for a group, as Entity already has a nested datamodel
            // FIXME: preview5 has a bug it won't nest TreeDataTimes, so we need to wrap it
            if (View.Entities != null)
                foreach (var e in View.Entities)
                    if (e.show_editor)
                        _hierarchy.Add(e);                                // standard behaviour
																		  //_hierarchy.Add(new HierarchyEntityView(e));         // preview5 behavio                        

			// SelectedItem = null;

			// TEst
			/*
            var hier1 = new HierarchyItem()
            {
                Title = "H1",
                Items = new ObservableCollection<object>
                    {
                        new HierarchyItem()
                        {
                            Title = "H2",
                            Items = new ObservableCollection<object>()
                            {
                                new HierarchyItem()
                                {
                                    Title = "H3"
                                }
                            }
                        }
                    }

            };

            _hierarchy.Add(hier1);
            */

			//_hierarchy.Add(new HierarchyItem() { Title = "Cursor", Items = new ObservableCollection<object>() { View.Cursor } });
			//
			//_hierarchy.Add(new HierarchyItem() { Title = "Entities", Items = new ObservableCollection<object>(View.Entities) });
			//_hierarchy.Add(View.Entities[0]);
		}

        // Helper to get a entity view from any view, either self or parent
        public void ExpandEntityHierarchy(object obj)
        {
            dynamic v = obj;
            try
            {
                while (true)
                {
                    dynamic vp = v.GetParent();
                    vp.IsExpanded = true;
                    v = vp;
                }
            }
            catch { }
        }

        #region Entities Create

        public ObservableCollection<EntityCreate> EntitiesToCreate { get; set; } = new ObservableCollection<EntityCreate>();

		[DependsOn(nameof(EntitiesToCreate_ObjectsRecent))]
		public bool HasRecentObjects => _recent_objects_dict.Count > 0;

		private Dictionary<string, int> _recent_objects_dict = new Dictionary<string, int>();
		public ObservableCollection<EntityCreate> EntitiesToCreate_ObjectsRecent { get; private set; } = new ObservableCollection<EntityCreate>();

		[DependsOn(nameof(EntitiesToCreate))]
        public ObservableCollection<EntityCreate> EntitiesToCreate_Objects => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("objects") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_Characters => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("characters") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_Effects => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("effects") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_Media => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("media") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_Backgrounds => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("backgrounds") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_SceneEffects => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("scene_effects") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_StructureEffects
			=> new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("structure_effects") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_StructureFaceEffects 
            => new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("structure_effects") && e.properties.HasProperty("effect_type","face") select e));

		[DependsOn(nameof(EntitiesToCreate))]
		public ObservableCollection<EntityCreate> EntitiesToCreate_StructureEdgeEffects
			=> new ObservableCollection<EntityCreate>((from e in EntitiesToCreate where e.category.ToLower().Equals("structure_effects") && e.properties.HasProperty("effect_type", "edge") select e));

		public void ResetRecentObjects()
        {
            _recent_objects_dict.Clear();
		}

        public void AddRecentCreatedEntityObject(Entity e)
        {
            if (!UseRecentCreatedObject)
                return;

			if (!_recent_objects_dict.ContainsKey(e.type))
                _recent_objects_dict.Add(e.type, 0);
            else
                _recent_objects_dict[e.type]++;

            // we store types string, so get them from currently received create_entities
            EntitiesToCreate_ObjectsRecent.Clear();

			foreach (var type in _recent_objects_dict.OrderByDescending(kv => kv.Value).Select(kv => kv.Key))
            {
                var ec_type = (from ec in EntitiesToCreate where ec.type == type select ec).FirstOrDefault();
                if (ec_type == null)
                    continue;

                EntitiesToCreate_ObjectsRecent.Add(ec_type);
			}
        }

		public async Task<ObservableCollection<EntityCreate>> GetEntitiesToCreate(string categories=null)
        {
            var entities = await App.Client.Client.GetEntitiesToCreate(categories);

            // filter out based on show_editor
            // even not licensed feature will be shown, for marketing
            
            EntitiesToCreate = new ObservableCollection<EntityCreate>(
                from e in entities where e.show_editor select e);

            return EntitiesToCreate;
        }


		public async Task<Entity> CreateEntityOnServer(Entity e, long parent_guid=-1L, bool add_to_recent=true)
        {
            // Afer creation the returned entity is valid on the server (with assigned guids) but must also arrive on the client
            var created_entity = await App.Client.Client.CreateEntity(e, parent_guid);

            if (created_entity == null || created_entity.__guid == -1)
                return null;

            // Wait for it to be available in the map
            var obj = await SharedObjectMap.SharedObjectMap.waitForGuidObject(created_entity.__guid);

            if (obj == null)
                return null;
            
			// if it is an object, add it to recent
            if (add_to_recent)
                Dispatcher.Dispatch(() => AddRecentCreatedEntityObject(created_entity));

            return created_entity;
		}

		public async Task<Structure> CreateStructureOnServer(Structure s)
		{
			// Afer creation the returned structure is valid on the server (with assigned guids) but must also arrive on the client
			var created_structure = await App.Client.Client.CreateStructure(s);

			if (created_structure == null || created_structure.__guid == -1)
				return null;

			// Wait for it to be available in the map
			await SharedObjectMap.SharedObjectMap.waitForGuidObject(created_structure.__guid);

			return created_structure;
		}

		#endregion


		// public ObservableCollection<IGuidObjectSyncView> 

		// UI bound properties
		#region UI

		/*
        [DependsOn(nameof(View))]
        public string ConnectButtonText
        {
            get
            {
                if (View != null)
                    return "Disconnect";
                else
                    return "Connect";
            }
        }*/

		//[DependsOn(nameof(View))]
		//public bool IsConnected => App.Client.IsConnected && View != null;

		// Now managed by OnConnectionChanged event
		public bool IsConnected { get; set; } = false;

        [AlsoNotifyFor(nameof(IsBusy))]
        public bool IsConnectingOrDisconnecting { get; set; } = false;

        // Managed by the everending CheckModuleProcessTask
        public bool IsModuleProcessRunning { get; set; } = false;

        [DependsOn(nameof(IsModuleProcessRunning),nameof(IsConnected))]
		public bool IsModuleProcessRunningAndNotConnected => IsModuleProcessRunning && !IsConnected;

		[DependsOn(nameof(IsModuleProcessRunning), nameof(IsConnected))]
		public bool IsModuleProcessRunningAndConnected => IsModuleProcessRunning && IsConnected;

        [DependsOn(nameof(IsModuleProcessRunning), nameof(IsConnected))]
        public bool IsModuleProcessRunningAndConnectedNotCalibrator => IsModuleProcessRunning && IsConnected && RunningModule != null && RunningModule.id.ToLower() != "calibrator";

		public PackageModule RunningModule { get; set; } = null;

        public PackageModule SelectedModule { get; set; } = null;   // used when opening windows (ex: info) before starting

		public bool IsDrawingStructure { get; set; } = false;

        public bool AreStructuresLocked { get; set; } = false;

		//public bool ShowConnectButton => IsModuleProcessRunning

		[DependsOn(nameof(IsConnected))]
		public string ConnectButtonText => IsConnected ? "Disconnect" : "Connect";

        #endregion

        private bool _refresh_hierarchy_enabled = true;

        public bool IsRefreshHierarchyEnabled { get; set; } = true;

		public void RefreshHierarchy(object select_item=null)
        {
            if (!IsRefreshHierarchyEnabled)
                return;

            MainSelectedItem = select_item;

            //TODO: when a collection item is added or removed, we cannot know?
            Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(async () =>
            {
				RaisePropertyChanged(nameof(Hierarchy));
            });
        }      

        public MainWindowUI()
        {
            Instance = this;

			SARGAME.iMainWindowUI = this;
		}

		#region Cursors

		public void SetCursorDefault()
			 => MainView.Instance.Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Arrow);

		public void SetCursorCross()
			 => MainView.Instance.Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Cross);

		public void SetCursorHand()
			=> MainView.Instance.Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Hand);

		public void SetCursorWait()
			=> MainView.Instance.Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Wait);

		public void SetCursorIcon(string icon, int width = 32)
		{
			if (icon == null)
				MainView.Instance.Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Arrow);
			else
			{
				var bmp = UserBitmap.LoadBitmap(icon, width: width);
				if (bmp != null)
					MainView.Instance.Cursor = new Avalonia.Input.Cursor(bmp, new PixelPoint(width / 2, width / 2));
			}
		}

		#endregion

		// TODO: rename these methods

		public void ZoomOut()
		{
            MainView.Instance.GetFirstVisibleScene().DeltaScale = 0.6f;
		}

        public void ZoomIn()
        {
            MainView.Instance.GetFirstVisibleScene().DeltaScale = 1.4f;
        }

        public void Fit()
		{
            SKRect rect = default;
           
            if (View != null)
                rect = new SKRect(0, 0, View.Screen.size.width, View.Screen.size.height);

            MainView.Instance.GetFirstVisibleScene()?.Scene.CenterAndFit(rect);
        }
        
        public async Task<bool> DeleteSelectedObject()
        {
			// deletes depending on object type
			if (SelectedItem == null)
				return false;

            if (ProgressDialog.IsOpen)
                return false;

			ProgressDialog.Choice choice;

			switch (SelectedItem)
			{
				case StructureView sv:

					choice = await ProgressDialog.ShowChoicesAsync("Remove the structure?", new string[] { "Yes", "No" });

					if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
						return false;

					sv.Remove();
					break;

				case EntityView ev:

					if (!ev.CanBeDeleted)
					{
						await ProgressDialog.ShowMessageAsync("This object cannot be deleted.");
						return false;
					}

					choice = await ProgressDialog.ShowChoicesAsync("Remove the object?", new string[] { "Yes", "No" });

					if (choice == ProgressDialog.Choice.Choice2)
						return false;

					ModelView.Instance.RemoveEntity(ev);
					break;

			}

            return true;
		}


        public void OpenObjectMenu(IPMView obj)
        {
			void _create_entity_menu(PMEntityView ev, Point position)
			{
                if (ev.View.MenuItems == null || ev.View.MenuItems.Count == 0)
					return;

				var contextMenu = new ContextMenu
				{
                    DataContext = ev.View,
					ItemsSource = ev.View.MenuItems
				};

				// transparent placeholder
				var rectangle = new Rectangle
				{
					Width = 10,
					Height = 10,
					Fill = Brushes.Transparent
				};

				Canvas.SetLeft(rectangle, position.X);
				Canvas.SetTop(rectangle, position.Y);

                // Add to LevelDesignPage canvas
				LevelDesignPage.Instance.canvas.Children.Add(rectangle);

				contextMenu.Closed += (s, e)
					=> LevelDesignPage.Instance.canvas.Children.Remove(rectangle);

				contextMenu.Open(rectangle);
			}

			Dispatcher.Dispatch(() =>
            { 
                switch (obj)
                {
                    case PMEntityView ev:

                        if (MainView.Instance.tab_level_design.IsSelected)
                        {
                            var v = obj.GetView();
                            if (v == null)
                                return;

                             MainSelectedItem =v;
							_create_entity_menu(ev, LevelEditor.Instance.MousePosition);
						}

                        break;
                }
            });
        }

		public void SendKey_Delete()
		{
            /*
            MainView.Instance.GetFirstVisibleScene()?.HandleKey(true,
                new Avalonia.Input.KeyEventArgs() { Key = Avalonia.Input.Key.Delete });
            */
        }
    }
    
    // Specializeed for structure group
    [AddINotifyPropertyChangedInterface]
    public class StructureGroup : HierarchyItem
    {
		public override FontWeight FontWeight => FontWeight.Bold;
		public override double FontSize => 14;

        public bool HasItems => Items != null && Items.Count > 0;

		public Thickness Padding => new Thickness(0, 5, 5, 5);

		public override ObservableCollection<MenuItem> MenuItems
		{
			get
			{
				var menuitems = new List<MenuItem>();

				menuitems.Add(new MenuItem() { Header = "Remove All", Tag = "remove_all" });

				foreach (var mi2 in menuitems)
					mi2.Click += menuitem_structuregroup;

				return new ObservableCollection<MenuItem>(menuitems);
			}
		}

		async void menuitem_structuregroup(object sender, RoutedEventArgs args)
		{
			var mi = (sender as MenuItem);
			switch (mi.Tag)
			{
				case "remove_all":

					var choice = await ProgressDialog.ShowChoicesAsync("Clear all Structures", new string[] { "Yes", "No" });

					if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
						return;

                    MainWindowUI.Instance.View.Structures_Clear();

					break;
			}
		}

	}

	// Specializeed for output surface
	[AddINotifyPropertyChangedInterface]
    public class OutputSurfaceGroup : HierarchyItem
    { }
}