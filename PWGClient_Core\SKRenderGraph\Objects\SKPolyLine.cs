﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;

// Draws a polyline contour, useful for temporary shape or contour drawing
// This is now used for the temporary Polygon used to draw new structures, has limited editing functionality

namespace Tabula.SKRenderGraph
{
    public class SKPolyLine : SKObject
    {
        public bool DrawLastLineToMouse = false;
        public bool ShowClosedPath = true;      // will show the closed path if first and last point are close enough

        public float ClosedPathPointDistance = 20;
        public SKColor ClosedPathColor = new SKColor(255, 255, 255, 150);


        public SKColor LineColor = SKColors.White;
        public SKColor DotColor = SKColors.Red;
        public SKColor LastLineColor = SKColors.Cyan;

        public List<SKPoint> Vertices = new List<SKPoint>();

        private SKPath path = new SKPath();

        public override bool Contains(SKPoint pos) => path.Contains(pos.X, pos.Y);

        private SKPaint paint_dot;

        public SKPolyLine(SKScene scene) : base(scene)
        {
            Paint = new SKPaint()
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = 3,
                StrokeCap = SKStrokeCap.Round,
                IsAntialias = true,
                Color = LineColor
            };

            paint_dot = new SKPaint()
            {
                Style = SKPaintStyle.StrokeAndFill,
                StrokeWidth = 3,
                IsAntialias = true,
                Color = DotColor
            };
        }

        public SKPolyLine(SKScene scene, List<SKPoint> vertices) : base(scene)
        {
            Vertices = vertices;
        }

        public override SKRect GetBoundingBox() => SKRect.Empty;

		public override void Update()
		{
            if (Vertices.Count > 0)
                Scene.Refresh();

			base.Update();
		}

		public override void LateDraw()
        {
            if (Vertices.Count > 0)
            {
                Paint.Color = LineColor;

                if (Vertices.Count>1)
                    for (int i=0; i<(Vertices.Count-1); i++)
                            Canvas.DrawLine(Vertices[i], Vertices[i+1], Paint);

                foreach (var v in Vertices)
                    Canvas.DrawCircle(v, 10, paint_dot);

                if (DrawLastLineToMouse && Scene.IsMouseInScene)
				{
                    // Debug.WriteLine($"MousePosition: {Scene.MousePositionUnscaled.X} {Scene.MousePositionUnscaled.Y}");

                    Paint.Color = LastLineColor;

                    Canvas.DrawLine(Vertices[Vertices.Count - 1], Scene.MousePosition, Paint);
				}

                if (ShowClosedPath && Vertices.Count > 2 && Scene.IsMouseInScene)
                {
                    var dist = SKPoint.Distance(Scene.MousePosition, Vertices[0]);
                    if (dist < ClosedPathPointDistance)
                    {
                        path = new SKPath();

						path.MoveTo(Vertices[0]);
                        foreach (var v in Vertices)
                            path.LineTo(v);

						path.Close();

						var paint = new SKPaint()
						{
							Style = SKPaintStyle.Fill,
							StrokeWidth = 1,
							StrokeCap = SKStrokeCap.Round,
							IsAntialias = true,
							Color = ClosedPathColor
						};

						Canvas.DrawPath(path, paint);
					}
                }
            }

            base.Draw();
        }
    }
}
